import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import '@testing-library/jest-dom';

import { AppRoutes } from '../../../routes';
import { authSlice } from '../../auth/authSlice';
import { vehiclesSlice } from '../vehiclesSlice';
import type { Vehicle } from '../../../types';

// Mock components that aren't needed for routing tests
jest.mock('../components/VehicleAnalyticsDashboard', () => {
  return function MockVehicleAnalyticsDashboard() {
    return <div data-testid="vehicle-analytics">Vehicle Analytics</div>;
  };
});

jest.mock('../components/VehicleMap', () => {
  return function MockVehicleMap() {
    return <div data-testid="vehicle-map">Vehicle Map</div>;
  };
});

jest.mock('../components/VehicleAssignmentHistory', () => {
  return function MockVehicleAssignmentHistory() {
    return <div data-testid="vehicle-assignment-history">Assignment History</div>;
  };
});

const mockVehicle: Vehicle = {
  id: 'vehicle-1',
  vehicleName: 'Test Vehicle',
  make: 'Ford',
  model: 'Transit',
  year: 2022,
  licensePlate: 'ABC-123',
  vin: '1HGBH41JXMN109186',
  vehicleType: 'Van',
  brand: 'Ford',
  color: 'White',
  fuelType: 'Diesel',
  fuelTankCapacity: 80,
  status: 'active',
  driverId: 'driver-1',
  currentMileage: 15000,
  lastMaintenanceDate: '2024-01-15T10:00:00Z',
  nextMaintenanceDate: '2024-04-15T10:00:00Z',
  currentLocation: undefined,
  currentSpeed: undefined,
  currentFuelLevel: undefined,
  engineStatus: undefined,
  lastTelemetryUpdate: undefined,
  isMoving: false,
  needsMaintenance: false,
  isAvailable: true,
  requiresImmediateAttention: false,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-20T14:30:00Z'
};

const createMockStore = (userRole: 'admin' | 'manager' | 'driver' = 'admin') => {
  return configureStore({
    reducer: {
      auth: authSlice.reducer,
      vehicles: vehiclesSlice.reducer,
    },
    preloadedState: {
      auth: {
        user: {
          id: 'user-1',
          email: '<EMAIL>',
          name: 'Test User',
          role: userRole,
          permissions: [],
          isActive: true,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        },
        token: 'mock-token',
        isAuthenticated: true,
        isLoading: false,
        error: null,
      },
      vehicles: {
        vehicles: [mockVehicle],
        currentVehicle: mockVehicle,
        selectedVehicles: [],
        vehicleAnalytics: {},
        maintenanceAlerts: [],
        assignmentHistory: [],
        maintenanceHistory: [],
        realTimeUpdates: [],
        isLoading: false,
        isCreating: false,
        isUpdating: false,
        isDeleting: false,
        isBulkOperating: false,
        isLoadingAnalytics: false,
        isLoadingMaintenanceAlerts: false,
        error: null,
        bulkOperationError: null,
        analyticsError: null,
        pagination: {
          page: 1,
          pageSize: 10,
          total: 1,
          totalPages: 1,
        },
        filters: {},
        assignmentConflicts: [],
      },
    },
  });
};

const renderWithRouter = (
  initialEntries: string[] = ['/vehicles'],
  userRole: 'admin' | 'manager' | 'driver' = 'admin'
) => {
  const store = createMockStore(userRole);
  return {
    ...render(
      <Provider store={store}>
        <MemoryRouter initialEntries={initialEntries}>
          <AppRoutes />
        </MemoryRouter>
      </Provider>
    ),
    store,
  };
};

describe('Vehicle Routing Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Vehicle List Route', () => {
    it('should render vehicles list at /vehicles', async () => {
      renderWithRouter(['/vehicles']);

      await waitFor(() => {
        expect(screen.getByText('Vehicles')).toBeInTheDocument();
      });
    });

    it('should show Add Vehicle button for admin users', async () => {
      renderWithRouter(['/vehicles'], 'admin');

      await waitFor(() => {
        expect(screen.getByText('Add Vehicle')).toBeInTheDocument();
      });
    });

    it('should hide Add Vehicle button for driver users', async () => {
      renderWithRouter(['/vehicles'], 'driver');

      await waitFor(() => {
        expect(screen.queryByText('Add Vehicle')).not.toBeInTheDocument();
      });
    });
  });

  describe('Vehicle Details Routes', () => {
    it('should render vehicle overview at /vehicles/:id', async () => {
      renderWithRouter(['/vehicles/vehicle-1']);

      await waitFor(() => {
        expect(screen.getByText('Test Vehicle')).toBeInTheDocument();
        expect(screen.getByText('Overview')).toBeInTheDocument();
      });
    });

    it('should render vehicle analytics at /vehicles/:id/analytics', async () => {
      renderWithRouter(['/vehicles/vehicle-1/analytics']);

      await waitFor(() => {
        expect(screen.getByTestId('vehicle-analytics')).toBeInTheDocument();
      });
    });

    it('should render vehicle location at /vehicles/:id/location', async () => {
      renderWithRouter(['/vehicles/vehicle-1/location']);

      await waitFor(() => {
        expect(screen.getByTestId('vehicle-map')).toBeInTheDocument();
      });
    });

    it('should render vehicle assignments at /vehicles/:id/assignments', async () => {
      renderWithRouter(['/vehicles/vehicle-1/assignments']);

      await waitFor(() => {
        expect(screen.getByTestId('vehicle-assignment-history')).toBeInTheDocument();
      });
    });
  });

  describe('Navigation Between Routes', () => {
    it('should navigate from list to vehicle details', async () => {
      renderWithRouter(['/vehicles']);

      await waitFor(() => {
        const viewButton = screen.getByText('View');
        fireEvent.click(viewButton);
      });

      // Note: In a real test, we'd need to mock the navigation
      // This is a simplified test structure
    });

    it('should navigate back to list from vehicle details', async () => {
      renderWithRouter(['/vehicles/vehicle-1']);

      await waitFor(() => {
        const backButton = screen.getByText('Back to Vehicles');
        expect(backButton).toBeInTheDocument();
      });
    });
  });

  describe('Permission-based Navigation', () => {
    it('should show all navigation tabs for admin users', async () => {
      renderWithRouter(['/vehicles/vehicle-1'], 'admin');

      await waitFor(() => {
        expect(screen.getByText('Overview')).toBeInTheDocument();
        expect(screen.getByText('Location')).toBeInTheDocument();
        expect(screen.getByText('Analytics')).toBeInTheDocument();
        expect(screen.getByText('Maintenance')).toBeInTheDocument();
        expect(screen.getByText('Assignments')).toBeInTheDocument();
      });
    });

    it('should show limited navigation tabs for driver users', async () => {
      renderWithRouter(['/vehicles/vehicle-1'], 'driver');

      await waitFor(() => {
        expect(screen.getByText('Overview')).toBeInTheDocument();
        expect(screen.getByText('Location')).toBeInTheDocument();
        // Analytics, Maintenance, and Assignments should not be visible for drivers
        expect(screen.queryByText('Analytics')).not.toBeInTheDocument();
        expect(screen.queryByText('Maintenance')).not.toBeInTheDocument();
        expect(screen.queryByText('Assignments')).not.toBeInTheDocument();
      });
    });

    it('should hide edit button for driver users', async () => {
      renderWithRouter(['/vehicles/vehicle-1'], 'driver');

      await waitFor(() => {
        expect(screen.queryByText('Edit')).not.toBeInTheDocument();
      });
    });

    it('should show edit button for admin users', async () => {
      renderWithRouter(['/vehicles/vehicle-1'], 'admin');

      await waitFor(() => {
        expect(screen.getByText('Edit')).toBeInTheDocument();
      });
    });
  });

  describe('Route Guards', () => {
    it('should allow access to analytics route for admin users', async () => {
      renderWithRouter(['/vehicles/vehicle-1/analytics'], 'admin');

      await waitFor(() => {
        expect(screen.getByTestId('vehicle-analytics')).toBeInTheDocument();
      });
    });

    it('should redirect or show error for analytics route for driver users', async () => {
      renderWithRouter(['/vehicles/vehicle-1/analytics'], 'driver');

      await waitFor(() => {
        // Should either redirect or show permission denied
        // The exact behavior depends on the PermissionGuard implementation
        expect(
          screen.queryByTestId('vehicle-analytics') || 
          screen.getByText(/permission/i) ||
          screen.getByText(/access denied/i)
        ).toBeTruthy();
      });
    });
  });

  describe('URL Parameter Handling', () => {
    it('should handle invalid vehicle IDs gracefully', async () => {
      renderWithRouter(['/vehicles/invalid-id']);

      await waitFor(() => {
        expect(screen.getByText('Vehicle not found')).toBeInTheDocument();
      });
    });

    it('should preserve URL parameters when navigating between sections', async () => {
      const { container } = renderWithRouter(['/vehicles/vehicle-1/analytics']);

      await waitFor(() => {
        expect(window.location.pathname).toBe('/vehicles/vehicle-1/analytics');
      });
    });
  });
});
