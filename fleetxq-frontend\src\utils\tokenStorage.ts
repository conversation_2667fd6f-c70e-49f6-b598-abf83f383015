// Token storage utility with secure handling and encryption support

interface TokenData {
  token: string;
  refreshToken: string;
  expiresAt: number;
  issuedAt: number;
}

interface StorageOptions {
  useSessionStorage?: boolean;
  encrypt?: boolean;
}

class TokenStorage {
  private readonly ACCESS_TOKEN_KEY = 'fleetxq_access_token';
  private readonly REFRESH_TOKEN_KEY = 'fleetxq_refresh_token';
  private readonly TOKEN_DATA_KEY = 'fleetxq_token_data';
  private readonly REMEMBER_ME_KEY = 'fleetxq_remember_me';

  /**
   * Get the appropriate storage based on remember me preference
   */
  private getStorage(useSessionStorage = false): Storage {
    const rememberMe = localStorage.getItem(this.REMEMBER_ME_KEY) === 'true';
    
    if (useSessionStorage || !rememberMe) {
      return sessionStorage;
    }
    
    return localStorage;
  }

  /**
   * Simple encryption/decryption (for basic obfuscation)
   * Note: This is not cryptographically secure, just basic obfuscation
   */
  private encrypt(data: string): string {
    try {
      return btoa(encodeURIComponent(data));
    } catch {
      return data;
    }
  }

  private decrypt(data: string): string {
    try {
      return decodeURIComponent(atob(data));
    } catch {
      return data;
    }
  }

  /**
   * Store token data
   */
  setTokens(
    accessToken: string, 
    refreshToken: string, 
    options: StorageOptions = {}
  ): void {
    const { useSessionStorage = false, encrypt = true } = options;
    const storage = this.getStorage(useSessionStorage);

    try {
      // Decode token to get expiry information
      const tokenPayload = this.decodeToken(accessToken);
      const expiresAt = tokenPayload?.exp ? tokenPayload.exp * 1000 : Date.now() + (24 * 60 * 60 * 1000);
      const issuedAt = tokenPayload?.iat ? tokenPayload.iat * 1000 : Date.now();

      const tokenData: TokenData = {
        token: accessToken,
        refreshToken,
        expiresAt,
        issuedAt,
      };

      // Store individual tokens (for backward compatibility)
      const tokenToStore = encrypt ? this.encrypt(accessToken) : accessToken;
      const refreshTokenToStore = encrypt ? this.encrypt(refreshToken) : refreshToken;
      
      storage.setItem(this.ACCESS_TOKEN_KEY, tokenToStore);
      storage.setItem(this.REFRESH_TOKEN_KEY, refreshTokenToStore);
      
      // Store complete token data
      const dataToStore = encrypt ? this.encrypt(JSON.stringify(tokenData)) : JSON.stringify(tokenData);
      storage.setItem(this.TOKEN_DATA_KEY, dataToStore);

    } catch (error) {
      console.error('Error storing tokens:', error);
      // Fallback to simple storage
      storage.setItem(this.ACCESS_TOKEN_KEY, accessToken);
      storage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);
    }
  }

  /**
   * Get access token
   */
  getAccessToken(decrypt = true): string | null {
    const storage = this.getStorage();
    const token = storage.getItem(this.ACCESS_TOKEN_KEY);
    
    if (!token) return null;
    
    return decrypt ? this.decrypt(token) : token;
  }

  /**
   * Get refresh token
   */
  getRefreshToken(decrypt = true): string | null {
    const storage = this.getStorage();
    const token = storage.getItem(this.REFRESH_TOKEN_KEY);
    
    if (!token) return null;
    
    return decrypt ? this.decrypt(token) : token;
  }

  /**
   * Get complete token data
   */
  getTokenData(decrypt = true): TokenData | null {
    const storage = this.getStorage();
    const data = storage.getItem(this.TOKEN_DATA_KEY);
    
    if (!data) return null;
    
    try {
      const decryptedData = decrypt ? this.decrypt(data) : data;
      return JSON.parse(decryptedData);
    } catch (error) {
      console.error('Error parsing token data:', error);
      return null;
    }
  }

  /**
   * Check if token is expired
   */
  isTokenExpired(): boolean {
    const tokenData = this.getTokenData();
    
    if (!tokenData) return true;
    
    return Date.now() >= tokenData.expiresAt;
  }

  /**
   * Check if token needs refresh (within 5 minutes of expiry)
   */
  needsRefresh(): boolean {
    const tokenData = this.getTokenData();
    
    if (!tokenData) return false;
    
    const fiveMinutes = 5 * 60 * 1000;
    return (tokenData.expiresAt - Date.now()) <= fiveMinutes;
  }

  /**
   * Get time until token expires (in milliseconds)
   */
  getTimeUntilExpiry(): number {
    const tokenData = this.getTokenData();
    
    if (!tokenData) return 0;
    
    return Math.max(0, tokenData.expiresAt - Date.now());
  }

  /**
   * Clear all tokens
   */
  clearTokens(): void {
    // Clear from both storages to be safe
    [localStorage, sessionStorage].forEach(storage => {
      storage.removeItem(this.ACCESS_TOKEN_KEY);
      storage.removeItem(this.REFRESH_TOKEN_KEY);
      storage.removeItem(this.TOKEN_DATA_KEY);
    });
  }

  /**
   * Set remember me preference
   */
  setRememberMe(remember: boolean): void {
    if (remember) {
      localStorage.setItem(this.REMEMBER_ME_KEY, 'true');
    } else {
      localStorage.removeItem(this.REMEMBER_ME_KEY);
    }
  }

  /**
   * Get remember me preference
   */
  getRememberMe(): boolean {
    return localStorage.getItem(this.REMEMBER_ME_KEY) === 'true';
  }

  /**
   * Decode JWT token payload
   */
  private decodeToken(token: string): any {
    try {
      const payload = token.split('.')[1];
      return JSON.parse(atob(payload));
    } catch (error) {
      console.error('Error decoding token:', error);
      return null;
    }
  }

  /**
   * Migrate tokens from old storage format
   */
  migrateTokens(): void {
    try {
      // Check if we have old format tokens
      const oldToken = localStorage.getItem('authToken');
      const oldRefreshToken = localStorage.getItem('refreshToken');
      
      if (oldToken && oldRefreshToken) {
        // Migrate to new format
        this.setTokens(oldToken, oldRefreshToken);
        
        // Clean up old tokens
        localStorage.removeItem('authToken');
        localStorage.removeItem('refreshToken');
      }
    } catch (error) {
      console.error('Error migrating tokens:', error);
    }
  }
}

// Export singleton instance
export const tokenStorage = new TokenStorage();

// Export class for testing
export { TokenStorage };
