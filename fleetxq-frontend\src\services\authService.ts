import { apiService } from './api';
import type {
  ApiResponse,
  LoginCredentials,
  AuthResponse,
  User,
  RefreshTokenRequest,
  RefreshTokenResponse,
} from '../types';

export class AuthService {
  private readonly baseUrl = '/auth';

  /**
   * Login user with email and password
   */
  async login(
    credentials: LoginCredentials
  ): Promise<ApiResponse<AuthResponse>> {
    try {
      const response = await apiService.post<AuthResponse>(
        `${this.baseUrl}/login`,
        credentials
      );

      if (response.success && response.data) {
        // Store tokens
        localStorage.setItem('authToken', response.data.token);
        localStorage.setItem('refreshToken', response.data.refreshToken);

        // Store user data
        localStorage.setItem('user', JSON.stringify(response.data.user));
      }

      return response;
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  }

  /**
   * Register new user
   */
  async register(userData: {
    name: string;
    email: string;
    password: string;
    role?: 'admin' | 'manager' | 'driver';
  }): Promise<ApiResponse<AuthResponse>> {
    try {
      const response = await apiService.post<AuthResponse>(
        `${this.baseUrl}/register`,
        userData
      );

      if (response.success && response.data) {
        // Store tokens
        localStorage.setItem('authToken', response.data.token);
        localStorage.setItem('refreshToken', response.data.refreshToken);

        // Store user data
        localStorage.setItem('user', JSON.stringify(response.data.user));
      }

      return response;
    } catch (error) {
      console.error('Registration failed:', error);
      throw error;
    }
  }

  /**
   * Logout user and clear tokens
   */
  async logout(): Promise<ApiResponse<void>> {
    try {
      const refreshToken = localStorage.getItem('refreshToken');

      if (refreshToken) {
        // Call logout endpoint to invalidate tokens on server
        await apiService.post<void>(`${this.baseUrl}/logout`, {
          refreshToken,
        });
      }
    } catch (error) {
      console.error('Logout API call failed:', error);
      // Continue with local cleanup even if API call fails
    } finally {
      // Clear local storage
      localStorage.removeItem('authToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('user');
    }

    return {
      success: true,
      data: undefined,
      message: 'Logged out successfully',
    };
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(): Promise<ApiResponse<RefreshTokenResponse>> {
    try {
      const refreshToken = localStorage.getItem('refreshToken');

      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await apiService.post<RefreshTokenResponse>(
        `${this.baseUrl}/refresh`,
        { refreshToken } as RefreshTokenRequest
      );

      if (response.success && response.data) {
        // Update stored tokens
        localStorage.setItem('authToken', response.data.token);
        localStorage.setItem('refreshToken', response.data.refreshToken);
      }

      return response;
    } catch (error) {
      console.error('Token refresh failed:', error);
      // Clear tokens on refresh failure
      this.logout();
      throw error;
    }
  }

  /**
   * Get current user profile
   */
  async getProfile(): Promise<ApiResponse<User>> {
    try {
      return await apiService.get<User>(`${this.baseUrl}/profile`);
    } catch (error) {
      console.error('Failed to get user profile:', error);
      throw error;
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(userData: Partial<User>): Promise<ApiResponse<User>> {
    try {
      return await apiService.put<User>(`${this.baseUrl}/profile`, userData);
    } catch (error) {
      console.error('Failed to update user profile:', error);
      throw error;
    }
  }

  /**
   * Change user password
   */
  async changePassword(passwordData: {
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
  }): Promise<ApiResponse<void>> {
    try {
      return await apiService.post<void>(
        `${this.baseUrl}/change-password`,
        passwordData
      );
    } catch (error) {
      console.error('Failed to change password:', error);
      throw error;
    }
  }

  /**
   * Request password reset
   */
  async requestPasswordReset(email: string): Promise<ApiResponse<void>> {
    try {
      return await apiService.post<void>(`${this.baseUrl}/forgot-password`, {
        email,
      });
    } catch (error) {
      console.error('Failed to request password reset:', error);
      throw error;
    }
  }

  /**
   * Reset password with token
   */
  async resetPassword(resetData: {
    token: string;
    newPassword: string;
    confirmPassword: string;
  }): Promise<ApiResponse<void>> {
    try {
      return await apiService.post<void>(
        `${this.baseUrl}/reset-password`,
        resetData
      );
    } catch (error) {
      console.error('Failed to reset password:', error);
      throw error;
    }
  }

  /**
   * Verify email with token
   */
  async verifyEmail(token: string): Promise<ApiResponse<void>> {
    try {
      return await apiService.post<void>(`${this.baseUrl}/verify-email`, {
        token,
      });
    } catch (error) {
      console.error('Failed to verify email:', error);
      throw error;
    }
  }

  /**
   * Resend email verification
   */
  async resendEmailVerification(): Promise<ApiResponse<void>> {
    try {
      return await apiService.post<void>(`${this.baseUrl}/resend-verification`);
    } catch (error) {
      console.error('Failed to resend email verification:', error);
      throw error;
    }
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    const token = localStorage.getItem('authToken');
    const user = localStorage.getItem('user');
    return !!(token && user);
  }

  /**
   * Get current user from local storage
   */
  getCurrentUser(): User | null {
    try {
      const userStr = localStorage.getItem('user');
      return userStr ? JSON.parse(userStr) : null;
    } catch (error) {
      console.error('Failed to parse user data:', error);
      return null;
    }
  }

  /**
   * Get current auth token
   */
  getAuthToken(): string | null {
    return localStorage.getItem('authToken');
  }

  /**
   * Get current refresh token
   */
  getRefreshToken(): string | null {
    return localStorage.getItem('refreshToken');
  }
}

// Create and export singleton instance
export const authService = new AuthService();
export default authService;
