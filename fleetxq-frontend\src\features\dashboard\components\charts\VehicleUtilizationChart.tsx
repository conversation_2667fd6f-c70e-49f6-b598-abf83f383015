import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
} from 'recharts';

export interface VehicleUtilizationData {
  time: string;
  utilization: number;
  activeVehicles: number;
  totalVehicles: number;
}

export interface VehicleUtilizationChartProps {
  data: VehicleUtilizationData[];
  height?: number;
  showLegend?: boolean;
  showGrid?: boolean;
  timeFormat?: string;
}

const VehicleUtilizationChart: React.FC<VehicleUtilizationChartProps> = ({
  data,
  height = 300,
  showLegend = true,
  showGrid = true,
  timeFormat = 'HH:mm',
}) => {
  const formatTime = (tickItem: string) => {
    const date = new Date(tickItem);
    return date.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    });
  };

  const formatTooltipTime = (value: string) => {
    const date = new Date(value);
    return date.toLocaleString();
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="text-sm font-medium text-gray-900 mb-2">
            {formatTooltipTime(label)}
          </p>
          {payload.map((entry: any, index: number) => (
            <div key={index} className="flex items-center space-x-2 text-sm">
              <div 
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: entry.color }}
              />
              <span className="text-gray-600">{entry.name}:</span>
              <span className="font-medium text-gray-900">
                {entry.name === 'Utilization' 
                  ? `${entry.value}%` 
                  : entry.value
                }
              </span>
            </div>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <ResponsiveContainer width="100%" height={height}>
      <LineChart
        data={data}
        margin={{
          top: 5,
          right: 30,
          left: 20,
          bottom: 5,
        }}
      >
        {showGrid && (
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        )}
        <XAxis 
          dataKey="time" 
          tickFormatter={formatTime}
          stroke="#6b7280"
          fontSize={12}
        />
        <YAxis 
          yAxisId="left"
          stroke="#6b7280"
          fontSize={12}
          label={{ value: 'Utilization (%)', angle: -90, position: 'insideLeft' }}
        />
        <YAxis 
          yAxisId="right" 
          orientation="right"
          stroke="#6b7280"
          fontSize={12}
          label={{ value: 'Vehicles', angle: 90, position: 'insideRight' }}
        />
        <Tooltip content={<CustomTooltip />} />
        {showLegend && (
          <Legend 
            wrapperStyle={{ paddingTop: '20px' }}
            iconType="line"
          />
        )}
        <Line
          yAxisId="left"
          type="monotone"
          dataKey="utilization"
          stroke="#3b82f6"
          strokeWidth={2}
          dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
          activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2 }}
          name="Utilization"
        />
        <Line
          yAxisId="right"
          type="monotone"
          dataKey="activeVehicles"
          stroke="#10b981"
          strokeWidth={2}
          dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
          activeDot={{ r: 6, stroke: '#10b981', strokeWidth: 2 }}
          name="Active Vehicles"
        />
        <Line
          yAxisId="right"
          type="monotone"
          dataKey="totalVehicles"
          stroke="#6b7280"
          strokeWidth={2}
          strokeDasharray="5 5"
          dot={{ fill: '#6b7280', strokeWidth: 2, r: 4 }}
          activeDot={{ r: 6, stroke: '#6b7280', strokeWidth: 2 }}
          name="Total Vehicles"
        />
      </LineChart>
    </ResponsiveContainer>
  );
};

export default VehicleUtilizationChart;
