import { useDispatch, useSelector } from 'react-redux';
import type { TypedUseSelectorHook } from 'react-redux';
import type { RootState, AppDispatch } from './index';

// Use throughout your app instead of plain `useDispatch` and `useSelector`
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

// Additional typed hooks for common patterns
export const useAppStore = () => {
  const dispatch = useAppDispatch();
  const selector = useAppSelector;

  return { dispatch, selector };
};

// Hook for accessing loading states across multiple slices
export const useLoadingStates = () => {
  return useAppSelector(state => ({
    auth: state.auth.isLoading,
    vehicles: state.vehicles.isLoading,
    drivers: state.drivers.isLoading,
    telemetry: state.telemetry.isLoading,
    alerts: state.alerts.isLoading,
    dashboard: state.dashboard.isLoading,
  }));
};

// Hook for accessing error states across multiple slices
export const useErrorStates = () => {
  return useAppSelector(state => ({
    auth: state.auth.error,
    vehicles: state.vehicles.error,
    drivers: state.drivers.error,
    telemetry: state.telemetry.error,
    alerts: state.alerts.error,
    dashboard: state.dashboard.error,
  }));
};

// Hook for checking if any slice is loading
export const useIsAnyLoading = () => {
  return useAppSelector(
    state =>
      state.auth.isLoading ||
      state.vehicles.isLoading ||
      state.drivers.isLoading ||
      state.telemetry.isLoading ||
      state.alerts.isLoading ||
      state.dashboard.isLoading
  );
};

// Hook for getting all active errors
export const useActiveErrors = () => {
  return useAppSelector(state => {
    const errors: string[] = [];

    if (state.auth.error) errors.push(state.auth.error);
    if (state.vehicles.error) errors.push(state.vehicles.error);
    if (state.drivers.error) errors.push(state.drivers.error);
    if (state.telemetry.error) errors.push(state.telemetry.error);
    if (state.alerts.error) errors.push(state.alerts.error);
    if (state.dashboard.error) errors.push(state.dashboard.error);

    return errors;
  });
};
