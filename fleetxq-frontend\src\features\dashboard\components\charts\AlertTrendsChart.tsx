import React from 'react';
import {
  Area<PERSON>hart,
  Area,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
} from 'recharts';

export interface AlertTrendsData {
  date: string;
  critical: number;
  high: number;
  medium: number;
  low: number;
  total: number;
}

export interface AlertTrendsChartProps {
  data: AlertTrendsData[];
  height?: number;
  showLegend?: boolean;
  showGrid?: boolean;
  stacked?: boolean;
}

const AlertTrendsChart: React.FC<AlertTrendsChartProps> = ({
  data,
  height = 300,
  showLegend = true,
  showGrid = true,
  stacked = true,
}) => {
  const formatDate = (tickItem: string) => {
    const date = new Date(tickItem);
    return date.toLocaleDateString([], { 
      month: 'short', 
      day: 'numeric' 
    });
  };

  const formatTooltipDate = (value: string) => {
    const date = new Date(value);
    return date.toLocaleDateString([], {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const total = payload.reduce((sum: number, entry: any) => sum + entry.value, 0);
      
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="text-sm font-medium text-gray-900 mb-2">
            {formatTooltipDate(label)}
          </p>
          <div className="space-y-1 text-sm">
            <div className="flex justify-between font-medium border-b border-gray-200 pb-1 mb-2">
              <span>Total Alerts:</span>
              <span>{total}</span>
            </div>
            {payload
              .sort((a: any, b: any) => {
                const order = ['critical', 'high', 'medium', 'low'];
                return order.indexOf(a.dataKey) - order.indexOf(b.dataKey);
              })
              .map((entry: any, index: number) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div 
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: entry.color }}
                    />
                    <span className="text-gray-600 capitalize">{entry.dataKey}:</span>
                  </div>
                  <span className="font-medium text-gray-900">{entry.value}</span>
                </div>
              ))}
          </div>
        </div>
      );
    }
    return null;
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return '#dc2626';
      case 'high':
        return '#ea580c';
      case 'medium':
        return '#d97706';
      case 'low':
        return '#2563eb';
      default:
        return '#6b7280';
    }
  };

  return (
    <ResponsiveContainer width="100%" height={height}>
      <AreaChart
        data={data}
        margin={{
          top: 5,
          right: 30,
          left: 20,
          bottom: 5,
        }}
      >
        {showGrid && (
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        )}
        <XAxis 
          dataKey="date" 
          tickFormatter={formatDate}
          stroke="#6b7280"
          fontSize={12}
        />
        <YAxis 
          stroke="#6b7280"
          fontSize={12}
          label={{ value: 'Number of Alerts', angle: -90, position: 'insideLeft' }}
        />
        <Tooltip content={<CustomTooltip />} />
        {showLegend && (
          <Legend 
            wrapperStyle={{ paddingTop: '20px' }}
            iconType="rect"
          />
        )}
        <Area
          type="monotone"
          dataKey="critical"
          stackId={stacked ? "1" : undefined}
          stroke={getSeverityColor('critical')}
          fill={getSeverityColor('critical')}
          fillOpacity={0.8}
          name="Critical"
        />
        <Area
          type="monotone"
          dataKey="high"
          stackId={stacked ? "1" : undefined}
          stroke={getSeverityColor('high')}
          fill={getSeverityColor('high')}
          fillOpacity={0.8}
          name="High"
        />
        <Area
          type="monotone"
          dataKey="medium"
          stackId={stacked ? "1" : undefined}
          stroke={getSeverityColor('medium')}
          fill={getSeverityColor('medium')}
          fillOpacity={0.8}
          name="Medium"
        />
        <Area
          type="monotone"
          dataKey="low"
          stackId={stacked ? "1" : undefined}
          stroke={getSeverityColor('low')}
          fill={getSeverityColor('low')}
          fillOpacity={0.8}
          name="Low"
        />
      </AreaChart>
    </ResponsiveContainer>
  );
};

export default AlertTrendsChart;
