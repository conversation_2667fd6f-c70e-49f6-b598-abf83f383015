# Fleet XQ Technical Interview - Comprehensive Preparation Guide

## 📋 Quick Reference Checklist

### Pre-Interview Setup
- [ ] .NET 8 SDK installed and verified
- [ ] Visual Studio 2022 or VS Code with C# extension
- [ ] SQL Server Express + SSMS ready
- [ ] AI tools (<PERSON>urs<PERSON>, <PERSON>tGP<PERSON>, <PERSON>) logged in and tested
- [ ] This preparation guide accessible during interview

### Key Technologies Stack
- **Backend**: .NET 8, Entity Framework Core, MediatR, FluentValidation
- **Database**: SQL Server, Database-First EF Core approach
- **Architecture**: Clean Architecture with CQRS pattern
- **Testing**: xUnit, Moq, FluentAssertions
- **Frontend**: React 18+ with TypeScript, Vite, Tailwind CSS v4, Redux Toolkit
- **Real-time**: SignalR for live updates
- **Code Quality**: ESLint, Prettier, TypeScript strict mode

## 🏗️ Phase 1: Database Foundation (20 min)

### Key Concepts to Remember
- Complex JOINs and aggregations
- Window functions for time-series data
- Fleet management specific queries (vehicle status, telemetry analysis)
- Performance optimization techniques

### Common SQL Patterns for Fleet Management

#### Vehicle Status Analysis
```sql
-- Get vehicle status distribution
SELECT 
    Status,
    COUNT(*) as VehicleCount,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as Percentage
FROM Vehicles 
GROUP BY Status;

-- Latest telemetry per vehicle with window function
SELECT 
    v.VehicleId,
    v.LicensePlate,
    t.Speed,
    t.FuelLevel,
    t.Timestamp,
    ROW_NUMBER() OVER (PARTITION BY v.VehicleId ORDER BY t.Timestamp DESC) as rn
FROM Vehicles v
LEFT JOIN TelemetryData t ON v.VehicleId = t.VehicleId
WHERE ROW_NUMBER() OVER (PARTITION BY v.VehicleId ORDER BY t.Timestamp DESC) = 1;
```

#### Time-Series Analysis
```sql
-- Average speed by hour for last 24 hours
SELECT 
    DATEPART(HOUR, Timestamp) as Hour,
    AVG(Speed) as AvgSpeed,
    COUNT(*) as ReadingCount
FROM TelemetryData 
WHERE Timestamp >= DATEADD(HOUR, -24, GETUTCDATE())
GROUP BY DATEPART(HOUR, Timestamp)
ORDER BY Hour;
```

### AI Prompt for SQL Generation
```
Generate a SQL query for fleet management that:
- Analyzes [specific metric like fuel consumption, speed patterns, etc.]
- Uses [specific time period like last 7 days, current month]
- Includes [specific grouping like by vehicle, by driver, by route]
- Returns results optimized for [dashboard display, reporting, alerts]

Database schema includes: Vehicles, Drivers, TelemetryData, Alerts, Routes tables.
Use SQL Server syntax with proper indexing considerations.
```

## 🔧 Phase 2: Core API Development (25 min)

### Clean Architecture Project Structure
```
/src
  /FleetXQ.Api          - Controllers, SignalR hubs, middleware
  /FleetXQ.Application  - CQRS handlers, DTOs, validation
  /FleetXQ.Domain       - Entities, value objects, interfaces
  /FleetXQ.Infrastructure - EF Core, repositories, external services
/tests
  /FleetXQ.Application.Tests - Unit tests with base classes
```

### CQRS Command Pattern Template
```csharp
// Command
public record CreateVehicleCommand(
    string Name,
    string LicensePlate,
    string VehicleType) : IRequest<Result<VehicleDto>>;

// Command Handler
public class CreateVehicleCommandHandler : IRequestHandler<CreateVehicleCommand, Result<VehicleDto>>
{
    private readonly IVehicleRepository _vehicleRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<CreateVehicleCommandHandler> _logger;

    public CreateVehicleCommandHandler(
        IVehicleRepository vehicleRepository,
        IMapper mapper,
        ILogger<CreateVehicleCommandHandler> logger)
    {
        _vehicleRepository = vehicleRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<Result<VehicleDto>> Handle(CreateVehicleCommand request, CancellationToken cancellationToken)
    {
        // 1. Validation (handled by FluentValidation pipeline)
        // 2. Business logic
        var vehicle = new Vehicle(request.Name, request.LicensePlate, request.VehicleType);
        
        // 3. Persistence
        await _vehicleRepository.AddAsync(vehicle, cancellationToken);
        
        // 4. Mapping and return
        var dto = _mapper.Map<VehicleDto>(vehicle);
        return Result<VehicleDto>.Success(dto);
    }
}

// Validator
public class CreateVehicleCommandValidator : AbstractValidator<CreateVehicleCommand>
{
    public CreateVehicleCommandValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty()
            .MaximumLength(100);
            
        RuleFor(x => x.LicensePlate)
            .NotEmpty()
            .Matches(@"^[A-Z0-9-]+$");
    }
}
```

### CQRS Query Pattern Template
```csharp
// Query
public record GetVehicleByIdQuery(Guid VehicleId) : IRequest<Result<VehicleDto>>;

// Query Handler
public class GetVehicleByIdQueryHandler : IRequestHandler<GetVehicleByIdQuery, Result<VehicleDto>>
{
    private readonly IVehicleRepository _vehicleRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetVehicleByIdQueryHandler> _logger;

    public async Task<Result<VehicleDto>> Handle(GetVehicleByIdQuery request, CancellationToken cancellationToken)
    {
        var vehicle = await _vehicleRepository.GetByIdAsync(request.VehicleId, cancellationToken);
        
        if (vehicle == null)
            return Result<VehicleDto>.Failure("Vehicle not found");
            
        var dto = _mapper.Map<VehicleDto>(vehicle);
        return Result<VehicleDto>.Success(dto);
    }
}
```

### Complete Service-to-Controller Wiring Examples

#### 1. Dependency Injection Setup (Program.cs)
```csharp
// Program.cs - Complete DI Configuration
var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Database Configuration
builder.Services.AddDbContext<FleetXQDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

// MediatR Configuration
builder.Services.AddMediatR(cfg => {
    cfg.RegisterServicesFromAssembly(typeof(CreateVehicleCommandHandler).Assembly);
});

// AutoMapper Configuration
builder.Services.AddAutoMapper(typeof(MappingProfile));

// FluentValidation Configuration
builder.Services.AddFluentValidationAutoValidation();
builder.Services.AddValidatorsFromAssembly(typeof(CreateVehicleCommandValidator).Assembly);

// Repository Registration
builder.Services.AddScoped<IVehicleRepository, VehicleRepository>();
builder.Services.AddScoped<IDriverRepository, DriverRepository>();
builder.Services.AddScoped<IUserRepository, UserRepository>();
builder.Services.AddScoped<IAlertRepository, AlertRepository>();

// Service Registration
builder.Services.AddScoped<IPasswordHashingService, PasswordHashingService>();
builder.Services.AddScoped<ITokenService, TokenService>();
builder.Services.AddScoped<IAlertEvaluationService, AlertEvaluationService>();

// Authentication & Authorization
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = builder.Configuration["Jwt:Issuer"],
            ValidAudience = builder.Configuration["Jwt:Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(
                Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Key"]))
        };
    });

// CORS Configuration
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowReactApp", policy =>
        policy.WithOrigins("http://localhost:3000")
              .AllowAnyHeader()
              .AllowAnyMethod()
              .AllowCredentials());
});

// SignalR Configuration
builder.Services.AddSignalR();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseCors("AllowReactApp");
app.UseAuthentication();
app.UseAuthorization();
app.MapControllers();
app.MapHub<TelemetryHub>("/telemetryHub");

app.Run();
```

#### 2. Base Controller with MediatR Integration
```csharp
// BaseApiController.cs
using MediatR;
using Microsoft.AspNetCore.Mvc;

[ApiController]
public abstract class BaseApiController : ControllerBase
{
    private ISender _mediator = null!;

    protected ISender Mediator => _mediator ??= HttpContext.RequestServices.GetRequiredService<ISender>();

    protected ActionResult<ApiResponse<T>> HandleResult<T>(Result<T> result)
    {
        if (result.IsSuccess)
            return Ok(ApiResponse<T>.Success(result.Value));

        return result.Error switch
        {
            "NotFound" => NotFound(ApiResponse<T>.Failure(result.Error)),
            "Unauthorized" => Unauthorized(ApiResponse<T>.Failure(result.Error)),
            "Forbidden" => Forbid(),
            _ => BadRequest(ApiResponse<T>.Failure(result.Error))
        };
    }

    protected ActionResult<PaginatedApiResponse<T>> HandlePaginatedResult<T>(PaginatedResult<T> result)
    {
        if (result.IsSuccess)
            return Ok(PaginatedApiResponse<T>.Success(
                result.Value,
                result.PageNumber,
                result.PageSize,
                result.TotalCount));

        return BadRequest(PaginatedApiResponse<T>.Failure(result.Error));
    }
}
```

#### 3. Complete Controller Implementation
```csharp
// VehiclesController.cs - Full Implementation
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class VehiclesController : BaseApiController
{
    /// <summary>
    /// Creates a new vehicle
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin,Manager")]
    [ProducesResponseType(typeof(ApiResponse<VehicleDto>), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ApiResponse<VehicleDto>), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<ApiResponse<VehicleDto>>> CreateVehicle(CreateVehicleCommand command)
    {
        var result = await Mediator.Send(command);

        if (result.IsSuccess)
            return CreatedAtAction(nameof(GetVehicle), new { id = result.Value.Id },
                ApiResponse<VehicleDto>.Success(result.Value));

        return HandleResult(result);
    }

    /// <summary>
    /// Gets a vehicle by ID
    /// </summary>
    [HttpGet("{id:guid}")]
    [ProducesResponseType(typeof(ApiResponse<VehicleDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<VehicleDto>), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ApiResponse<VehicleDto>>> GetVehicle(Guid id)
    {
        var result = await Mediator.Send(new GetVehicleByIdQuery(id));
        return HandleResult(result);
    }

    /// <summary>
    /// Gets all vehicles with pagination
    /// </summary>
    [HttpGet]
    [ProducesResponseType(typeof(PaginatedApiResponse<VehicleListDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<PaginatedApiResponse<VehicleListDto>>> GetVehicles(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? searchTerm = null,
        [FromQuery] VehicleStatus? status = null)
    {
        var query = new GetVehicleListQuery(pageNumber, pageSize, searchTerm, status);
        var result = await Mediator.Send(query);
        return HandlePaginatedResult(result);
    }

    /// <summary>
    /// Updates a vehicle
    /// </summary>
    [HttpPut("{id:guid}")]
    [Authorize(Roles = "Admin,Manager")]
    [ProducesResponseType(typeof(ApiResponse<VehicleDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<VehicleDto>), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ApiResponse<VehicleDto>>> UpdateVehicle(Guid id, UpdateVehicleCommand command)
    {
        if (id != command.Id)
            return BadRequest(ApiResponse<VehicleDto>.Failure("ID mismatch"));

        var result = await Mediator.Send(command);
        return HandleResult(result);
    }

    /// <summary>
    /// Updates vehicle status
    /// </summary>
    [HttpPut("{id:guid}/status")]
    [Authorize(Roles = "Admin,Manager,Driver")]
    [ProducesResponseType(typeof(ApiResponse<VehicleDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<ApiResponse<VehicleDto>>> UpdateVehicleStatus(
        Guid id,
        [FromBody] UpdateVehicleStatusRequest request)
    {
        var command = new UpdateVehicleStatusCommand(id, request.Status, request.Notes);
        var result = await Mediator.Send(command);
        return HandleResult(result);
    }

    /// <summary>
    /// Deletes a vehicle
    /// </summary>
    [HttpDelete("{id:guid}")]
    [Authorize(Roles = "Admin")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status404NotFound)]
    public async Task<ActionResult> DeleteVehicle(Guid id)
    {
        var result = await Mediator.Send(new DeleteVehicleCommand(id));

        if (result.IsSuccess)
            return NoContent();

        return HandleResult(Result<object>.Failure(result.Error));
    }
}
```

#### 4. Service Layer Integration Examples

##### Authentication Controller with Service Dependencies
```csharp
// AuthController.cs - Complete Service Integration
[ApiController]
[Route("api/[controller]")]
public class AuthController : BaseApiController
{
    /// <summary>
    /// User login endpoint
    /// </summary>
    [HttpPost("login")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(ApiResponse<LoginResponseDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<ApiResponse<LoginResponseDto>>> Login(LoginUserCommand command)
    {
        var result = await Mediator.Send(command);
        return HandleResult(result);
    }

    /// <summary>
    /// Token refresh endpoint
    /// </summary>
    [HttpPost("refresh")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(ApiResponse<TokenResponseDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<ApiResponse<TokenResponseDto>>> RefreshToken(RefreshTokenCommand command)
    {
        var result = await Mediator.Send(command);
        return HandleResult(result);
    }

    /// <summary>
    /// User logout endpoint
    /// </summary>
    [HttpPost("logout")]
    [Authorize]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    public async Task<ActionResult> Logout()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (Guid.TryParse(userId, out var userGuid))
        {
            await Mediator.Send(new LogoutUserCommand(userGuid));
        }
        return NoContent();
    }

    /// <summary>
    /// Get current user profile
    /// </summary>
    [HttpGet("profile")]
    [Authorize]
    [ProducesResponseType(typeof(ApiResponse<UserProfileDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<ApiResponse<UserProfileDto>>> GetProfile()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (!Guid.TryParse(userId, out var userGuid))
            return Unauthorized(ApiResponse<UserProfileDto>.Failure("Invalid user token"));

        var result = await Mediator.Send(new GetUserProfileQuery(userGuid));
        return HandleResult(result);
    }
}
```

##### Telemetry Controller with Real-time Integration
```csharp
// TelemetryController.cs - SignalR Integration
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class TelemetryController : BaseApiController
{
    /// <summary>
    /// Process telemetry data batch
    /// </summary>
    [HttpPost("batch")]
    [Authorize(Roles = "Admin,System")]
    [ProducesResponseType(typeof(ApiResponse<BatchProcessResultDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<ApiResponse<BatchProcessResultDto>>> ProcessTelemetryBatch(
        ProcessTelemetryBatchCommand command)
    {
        var result = await Mediator.Send(command);
        return HandleResult(result);
    }

    /// <summary>
    /// Get latest telemetry for a vehicle
    /// </summary>
    [HttpGet("{vehicleId:guid}/latest")]
    [ProducesResponseType(typeof(ApiResponse<TelemetryDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<ApiResponse<TelemetryDto>>> GetLatestTelemetry(Guid vehicleId)
    {
        var result = await Mediator.Send(new GetLatestTelemetryQuery(vehicleId));
        return HandleResult(result);
    }

    /// <summary>
    /// Get telemetry history with pagination
    /// </summary>
    [HttpGet("{vehicleId:guid}/history")]
    [ProducesResponseType(typeof(PaginatedApiResponse<TelemetryDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<PaginatedApiResponse<TelemetryDto>>> GetTelemetryHistory(
        Guid vehicleId,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 50)
    {
        var query = new GetTelemetryHistoryQuery(
            vehicleId,
            startDate ?? DateTime.UtcNow.AddDays(-1),
            endDate ?? DateTime.UtcNow,
            pageNumber,
            pageSize);

        var result = await Mediator.Send(query);
        return HandlePaginatedResult(result);
    }
}
```

#### 5. SignalR Hub Integration
```csharp
// TelemetryHub.cs - Real-time Communication
[Authorize]
public class TelemetryHub : Hub
{
    private readonly ILogger<TelemetryHub> _logger;
    private readonly IVehicleRepository _vehicleRepository;

    public TelemetryHub(ILogger<TelemetryHub> logger, IVehicleRepository vehicleRepository)
    {
        _logger = logger;
        _vehicleRepository = vehicleRepository;
    }

    /// <summary>
    /// Subscribe to vehicle updates
    /// </summary>
    public async Task SubscribeToVehicle(Guid vehicleId)
    {
        var vehicle = await _vehicleRepository.GetByIdAsync(vehicleId);
        if (vehicle == null)
        {
            await Clients.Caller.SendAsync("Error", "Vehicle not found");
            return;
        }

        await Groups.AddToGroupAsync(Context.ConnectionId, $"Vehicle_{vehicleId}");
        _logger.LogInformation("User {UserId} subscribed to vehicle {VehicleId}",
            Context.UserIdentifier, vehicleId);

        await Clients.Caller.SendAsync("SubscriptionConfirmed", vehicleId);
    }

    /// <summary>
    /// Unsubscribe from vehicle updates
    /// </summary>
    public async Task UnsubscribeFromVehicle(Guid vehicleId)
    {
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"Vehicle_{vehicleId}");
        _logger.LogInformation("User {UserId} unsubscribed from vehicle {VehicleId}",
            Context.UserIdentifier, vehicleId);
    }

    /// <summary>
    /// Subscribe to all alerts
    /// </summary>
    public async Task SubscribeToAlerts()
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, "Alerts");
        await Clients.Caller.SendAsync("AlertSubscriptionConfirmed");
    }

    public override async Task OnConnectedAsync()
    {
        _logger.LogInformation("User {UserId} connected to telemetry hub", Context.UserIdentifier);
        await base.OnConnectedAsync();
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        _logger.LogInformation("User {UserId} disconnected from telemetry hub", Context.UserIdentifier);
        await base.OnDisconnectedAsync(exception);
    }
}
```

#### 6. Domain Event Handler Integration
```csharp
// TelemetryDataReceivedEventHandler.cs - Domain Event to SignalR
public class TelemetryDataReceivedEventHandler : INotificationHandler<TelemetryDataReceivedEvent>
{
    private readonly IHubContext<TelemetryHub> _hubContext;
    private readonly IMapper _mapper;
    private readonly ILogger<TelemetryDataReceivedEventHandler> _logger;

    public TelemetryDataReceivedEventHandler(
        IHubContext<TelemetryHub> hubContext,
        IMapper mapper,
        ILogger<TelemetryDataReceivedEventHandler> logger)
    {
        _hubContext = hubContext;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task Handle(TelemetryDataReceivedEvent notification, CancellationToken cancellationToken)
    {
        try
        {
            var telemetryDto = _mapper.Map<TelemetryDto>(notification.TelemetryData);

            // Send to vehicle-specific group
            await _hubContext.Clients.Group($"Vehicle_{notification.TelemetryData.VehicleId}")
                .SendAsync("TelemetryUpdate", telemetryDto, cancellationToken);

            // Send to dashboard group if needed
            await _hubContext.Clients.Group("Dashboard")
                .SendAsync("VehicleStatusUpdate", new
                {
                    VehicleId = notification.TelemetryData.VehicleId,
                    LastUpdate = notification.TelemetryData.Timestamp,
                    Status = "Active"
                }, cancellationToken);

            _logger.LogDebug("Telemetry update sent for vehicle {VehicleId}",
                notification.TelemetryData.VehicleId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending telemetry update for vehicle {VehicleId}",
                notification.TelemetryData.VehicleId);
        }
    }
}
```

#### 7. Middleware Integration Examples

##### Global Exception Handling Middleware
```csharp
// GlobalExceptionMiddleware.cs
public class GlobalExceptionMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<GlobalExceptionMiddleware> _logger;

    public GlobalExceptionMiddleware(RequestDelegate next, ILogger<GlobalExceptionMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An unhandled exception occurred");
            await HandleExceptionAsync(context, ex);
        }
    }

    private static async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        context.Response.ContentType = "application/json";

        var response = exception switch
        {
            ValidationException validationEx => new ApiResponse<object>
            {
                Success = false,
                StatusCode = StatusCodes.Status400BadRequest,
                Message = "Validation failed",
                Errors = validationEx.Errors.Select(e => e.ErrorMessage).ToList()
            },
            UnauthorizedAccessException => new ApiResponse<object>
            {
                Success = false,
                StatusCode = StatusCodes.Status401Unauthorized,
                Message = "Unauthorized access"
            },
            NotFoundException => new ApiResponse<object>
            {
                Success = false,
                StatusCode = StatusCodes.Status404NotFound,
                Message = "Resource not found"
            },
            _ => new ApiResponse<object>
            {
                Success = false,
                StatusCode = StatusCodes.Status500InternalServerError,
                Message = "An internal server error occurred"
            }
        };

        context.Response.StatusCode = response.StatusCode;
        await context.Response.WriteAsync(JsonSerializer.Serialize(response));
    }
}

// Extension method for middleware registration
public static class MiddlewareExtensions
{
    public static IApplicationBuilder UseGlobalExceptionHandling(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<GlobalExceptionMiddleware>();
    }
}
```

##### Request Logging Middleware
```csharp
// RequestLoggingMiddleware.cs
public class RequestLoggingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<RequestLoggingMiddleware> _logger;

    public RequestLoggingMiddleware(RequestDelegate next, ILogger<RequestLoggingMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var stopwatch = Stopwatch.StartNew();

        _logger.LogInformation("Request started: {Method} {Path} from {RemoteIpAddress}",
            context.Request.Method,
            context.Request.Path,
            context.Connection.RemoteIpAddress);

        await _next(context);

        stopwatch.Stop();

        _logger.LogInformation("Request completed: {Method} {Path} responded {StatusCode} in {ElapsedMilliseconds}ms",
            context.Request.Method,
            context.Request.Path,
            context.Response.StatusCode,
            stopwatch.ElapsedMilliseconds);
    }
}
```

#### 8. Complete Startup Configuration with All Wiring
```csharp
// Program.cs - Complete Production-Ready Configuration
var builder = WebApplication.CreateBuilder(args);

// Logging Configuration
builder.Logging.ClearProviders();
builder.Logging.AddConsole();
builder.Logging.AddDebug();

// Add services to the container
builder.Services.AddControllers(options =>
{
    options.Filters.Add<ValidationFilter>();
    options.SuppressAsyncSuffixInActionNames = false;
});

builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo { Title = "FleetXQ API", Version = "v1" });
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });
    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });
});

// Database Configuration
builder.Services.AddDbContext<FleetXQDbContext>(options =>
{
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection"));
    options.EnableSensitiveDataLogging(builder.Environment.IsDevelopment());
    options.EnableDetailedErrors(builder.Environment.IsDevelopment());
});

// MediatR Configuration with Pipeline Behaviors
builder.Services.AddMediatR(cfg =>
{
    cfg.RegisterServicesFromAssembly(typeof(CreateVehicleCommandHandler).Assembly);
    cfg.AddBehavior(typeof(IPipelineBehavior<,>), typeof(ValidationBehavior<,>));
    cfg.AddBehavior(typeof(IPipelineBehavior<,>), typeof(LoggingBehavior<,>));
    cfg.AddBehavior(typeof(IPipelineBehavior<,>), typeof(PerformanceBehavior<,>));
});

// AutoMapper Configuration
builder.Services.AddAutoMapper(typeof(MappingProfile));

// FluentValidation Configuration
builder.Services.AddFluentValidationAutoValidation();
builder.Services.AddValidatorsFromAssembly(typeof(CreateVehicleCommandValidator).Assembly);

// Repository Registration
builder.Services.AddScoped<IVehicleRepository, VehicleRepository>();
builder.Services.AddScoped<IDriverRepository, DriverRepository>();
builder.Services.AddScoped<IUserRepository, UserRepository>();
builder.Services.AddScoped<IAlertRepository, AlertRepository>();
builder.Services.AddScoped<ITelemetryRepository, TelemetryRepository>();

// Service Registration
builder.Services.AddScoped<IPasswordHashingService, PasswordHashingService>();
builder.Services.AddScoped<ITokenService, TokenService>();
builder.Services.AddScoped<IAlertEvaluationService, AlertEvaluationService>();
builder.Services.AddScoped<IEmailService, EmailService>();
builder.Services.AddScoped<ISmsService, SmsService>();

// Authentication & Authorization
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = builder.Configuration["Jwt:Issuer"],
            ValidAudience = builder.Configuration["Jwt:Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(
                Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Key"])),
            ClockSkew = TimeSpan.Zero
        };

        // Configure SignalR authentication
        options.Events = new JwtBearerEvents
        {
            OnMessageReceived = context =>
            {
                var accessToken = context.Request.Query["access_token"];
                var path = context.HttpContext.Request.Path;

                if (!string.IsNullOrEmpty(accessToken) && path.StartsWithSegments("/telemetryHub"))
                {
                    context.Token = accessToken;
                }
                return Task.CompletedTask;
            }
        };
    });

builder.Services.AddAuthorization(options =>
{
    options.AddPolicy("AdminOnly", policy => policy.RequireRole("Admin"));
    options.AddPolicy("ManagerOrAdmin", policy => policy.RequireRole("Admin", "Manager"));
    options.AddPolicy("DriverAccess", policy => policy.RequireRole("Admin", "Manager", "Driver"));
});

// CORS Configuration
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowReactApp", policy =>
        policy.WithOrigins(builder.Configuration.GetSection("AllowedOrigins").Get<string[]>() ?? new[] { "http://localhost:3000" })
              .AllowAnyHeader()
              .AllowAnyMethod()
              .AllowCredentials());
});

// SignalR Configuration
builder.Services.AddSignalR(options =>
{
    options.EnableDetailedErrors = builder.Environment.IsDevelopment();
    options.KeepAliveInterval = TimeSpan.FromSeconds(15);
    options.ClientTimeoutInterval = TimeSpan.FromSeconds(30);
});

// Health Checks
builder.Services.AddHealthChecks()
    .AddDbContextCheck<FleetXQDbContext>()
    .AddCheck("SignalR", () => HealthCheckResult.Healthy());

// Memory Cache
builder.Services.AddMemoryCache();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "FleetXQ API V1");
        c.RoutePrefix = string.Empty; // Serve Swagger UI at root
    });
}

// Middleware Pipeline Order is Important!
app.UseGlobalExceptionHandling();
app.UseMiddleware<RequestLoggingMiddleware>();
app.UseHttpsRedirection();
app.UseCors("AllowReactApp");
app.UseAuthentication();
app.UseAuthorization();

// Map endpoints
app.MapControllers();
app.MapHub<TelemetryHub>("/telemetryHub");
app.MapHub<AlertHub>("/alertHub");
app.MapHealthChecks("/health");

// Ensure database is created
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<FleetXQDbContext>();
    context.Database.EnsureCreated();
}

app.Run();
```

#### 9. API Response Models and Validation
```csharp
// ApiResponse.cs - Standardized API Response
public class ApiResponse<T>
{
    public bool Success { get; set; }
    public int StatusCode { get; set; }
    public string Message { get; set; } = string.Empty;
    public T? Data { get; set; }
    public List<string> Errors { get; set; } = new();
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    public static ApiResponse<T> Success(T data, string message = "Success")
    {
        return new ApiResponse<T>
        {
            Success = true,
            StatusCode = 200,
            Message = message,
            Data = data
        };
    }

    public static ApiResponse<T> Failure(string message, int statusCode = 400, List<string>? errors = null)
    {
        return new ApiResponse<T>
        {
            Success = false,
            StatusCode = statusCode,
            Message = message,
            Errors = errors ?? new List<string>()
        };
    }
}

// PaginatedApiResponse.cs - For paginated results
public class PaginatedApiResponse<T> : ApiResponse<IEnumerable<T>>
{
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalCount { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    public bool HasNextPage => PageNumber < TotalPages;
    public bool HasPreviousPage => PageNumber > 1;

    public static PaginatedApiResponse<T> Success(
        IEnumerable<T> data,
        int pageNumber,
        int pageSize,
        int totalCount,
        string message = "Success")
    {
        return new PaginatedApiResponse<T>
        {
            Success = true,
            StatusCode = 200,
            Message = message,
            Data = data,
            PageNumber = pageNumber,
            PageSize = pageSize,
            TotalCount = totalCount
        };
    }

    public static PaginatedApiResponse<T> Failure(string message, int statusCode = 400)
    {
        return new PaginatedApiResponse<T>
        {
            Success = false,
            StatusCode = statusCode,
            Message = message,
            Data = Enumerable.Empty<T>()
        };
    }
}
```

#### 10. Validation Filter and Action Filters
```csharp
// ValidationFilter.cs - Global Model Validation
public class ValidationFilter : IActionFilter
{
    public void OnActionExecuting(ActionExecutingContext context)
    {
        if (!context.ModelState.IsValid)
        {
            var errors = context.ModelState
                .Where(x => x.Value?.Errors.Count > 0)
                .SelectMany(x => x.Value!.Errors)
                .Select(x => x.ErrorMessage)
                .ToList();

            var response = ApiResponse<object>.Failure("Validation failed", 400, errors);
            context.Result = new BadRequestObjectResult(response);
        }
    }

    public void OnActionExecuted(ActionExecutedContext context)
    {
        // No implementation needed
    }
}

// LoggingActionFilter.cs - Action-level logging
public class LoggingActionFilter : IActionFilter
{
    private readonly ILogger<LoggingActionFilter> _logger;

    public LoggingActionFilter(ILogger<LoggingActionFilter> logger)
    {
        _logger = logger;
    }

    public void OnActionExecuting(ActionExecutingContext context)
    {
        var actionName = context.ActionDescriptor.DisplayName;
        var controllerName = context.Controller.GetType().Name;

        _logger.LogInformation("Executing action {ActionName} in {ControllerName}",
            actionName, controllerName);
    }

    public void OnActionExecuted(ActionExecutedContext context)
    {
        var actionName = context.ActionDescriptor.DisplayName;
        var statusCode = context.HttpContext.Response.StatusCode;

        _logger.LogInformation("Action {ActionName} completed with status code {StatusCode}",
            actionName, statusCode);
    }
}
```

#### 11. Request/Response DTOs with Validation
```csharp
// UpdateVehicleStatusRequest.cs - Request DTO
public class UpdateVehicleStatusRequest
{
    [Required]
    [EnumDataType(typeof(VehicleStatus))]
    public VehicleStatus Status { get; set; }

    [MaxLength(500)]
    public string? Notes { get; set; }
}

// VehicleDto.cs - Response DTO
public class VehicleDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string LicensePlate { get; set; } = string.Empty;
    public string VehicleType { get; set; } = string.Empty;
    public VehicleStatus Status { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastUpdated { get; set; }
    public DriverDto? AssignedDriver { get; set; }
    public TelemetryDto? LatestTelemetry { get; set; }
}

// VehicleListDto.cs - Simplified DTO for lists
public class VehicleListDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string LicensePlate { get; set; } = string.Empty;
    public VehicleStatus Status { get; set; }
    public string? DriverName { get; set; }
    public DateTime? LastTelemetryUpdate { get; set; }
}
```

#### 12. Complete AI Prompt for Service-to-Controller Wiring
```
Generate complete service-to-controller wiring for FleetXQ feature:

Feature: [Feature name like Vehicle Management, Driver Assignment, etc.]
Operations: [List of CRUD and custom operations needed]

Requirements:
1. Complete DI registration in Program.cs
2. Controller with all endpoints and proper HTTP verbs
3. Request/Response DTOs with validation attributes
4. Proper error handling with standardized ApiResponse
5. Authorization attributes based on user roles
6. SignalR integration if real-time updates needed
7. Action filters for logging and validation
8. Swagger documentation attributes
9. Proper async/await patterns
10. Integration with MediatR for CQRS

Generate:
- DI registration code for Program.cs
- Complete controller class with all endpoints
- Request/Response DTO classes
- Any required middleware or filters
- SignalR hub methods if applicable

Follow Clean Architecture patterns and existing codebase conventions.
```

### AI Prompts for Backend Development

#### Generate CQRS Command/Query
```
Create a CQRS [command/query] for FleetXQ fleet management system:

Feature: [Vehicle Management, Driver Assignment, Telemetry Processing, Alert Management]
Operation: [Create, Update, Delete, Get, List, Process]
Entity: [Vehicle, Driver, TelemetryData, Alert]

Requirements:
- Follow Clean Architecture patterns
- Use MediatR for request handling
- Include FluentValidation for commands
- Use AutoMapper for DTO mapping
- Include proper error handling with Result pattern
- Add logging with ILogger
- Follow existing codebase conventions

Generate: Command/Query class, Handler, Validator (if command), and DTO classes.
```

#### Generate Repository Interface and Implementation
```
Create repository interface and EF Core implementation for [Entity Name] in FleetXQ:

Requirements:
- Follow repository pattern from existing codebase
- Include standard CRUD operations
- Add domain-specific query methods for [specific operations]
- Use async/await pattern
- Include proper cancellation token support
- Follow database-first EF Core approach
- Include proper error handling

Entity context: [Brief description of the entity and its relationships]
```

## 🧪 Comprehensive Unit Testing Strategy

### Test Base Classes Structure
```csharp
// Application Test Base
public abstract class TestBase
{
    protected readonly IMapper Mapper;
    protected readonly Mock<IVehicleRepository> MockVehicleRepository;
    protected readonly Mock<IUserRepository> MockUserRepository;
    // ... other repository mocks
    
    protected TestBase()
    {
        // Setup AutoMapper with production profiles
        var mapperConfig = new MapperConfiguration(cfg => cfg.AddProfile<MappingProfile>());
        Mapper = mapperConfig.CreateMapper();
        
        // Initialize all repository mocks
        MockVehicleRepository = new Mock<IVehicleRepository>();
        // ... setup other mocks
    }
}

// Command Handler Test Base
public abstract class CommandHandlerTestBase<THandler> : TestBase where THandler : class
{
    protected readonly Mock<ILogger<THandler>> MockLogger;
    
    protected CommandHandlerTestBase()
    {
        MockLogger = CreateMockLogger<THandler>();
    }
    
    protected void VerifyRepositoryAddCalled<TEntity>(Mock<IRepository<TEntity>> mockRepository)
        where TEntity : class
    {
        mockRepository.Verify(x => x.AddAsync(It.IsAny<TEntity>(), It.IsAny<CancellationToken>()), Times.Once);
    }
}
```

### Complete Test Template
```csharp
public class CreateVehicleCommandHandlerTests : CommandHandlerTestBase<CreateVehicleCommandHandler>
{
    private readonly CreateVehicleCommandHandler _handler;

    public CreateVehicleCommandHandlerTests()
    {
        _handler = new CreateVehicleCommandHandler(
            MockVehicleRepository.Object,
            Mapper,
            MockLogger.Object);
    }

    [Fact]
    public async Task Handle_ValidCommand_ShouldCreateVehicleSuccessfully()
    {
        // Arrange
        var command = new CreateVehicleCommand("Test Vehicle", "ABC-123", "Truck");
        var cancellationToken = new CancellationToken();

        MockVehicleRepository
            .Setup(x => x.AddAsync(It.IsAny<Vehicle>(), cancellationToken))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _handler.Handle(command, cancellationToken);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().NotBeNull();
        result.Value.Name.Should().Be("Test Vehicle");
        result.Value.LicensePlate.Should().Be("ABC-123");

        VerifyRepositoryAddCalled(MockVehicleRepository);
        VerifyInformationLogged();
    }

    [Fact]
    public async Task Handle_RepositoryThrowsException_ShouldReturnFailure()
    {
        // Arrange
        var command = new CreateVehicleCommand("Test Vehicle", "ABC-123", "Truck");
        var cancellationToken = new CancellationToken();

        MockVehicleRepository
            .Setup(x => x.AddAsync(It.IsAny<Vehicle>(), cancellationToken))
            .ThrowsAsync(new InvalidOperationException("Database error"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => 
            _handler.Handle(command, cancellationToken));

        VerifyErrorLogged();
    }
}
```

### AI Prompt for Test Generation
```
Generate comprehensive unit tests for [CommandHandler/QueryHandler] in FleetXQ using the established testing patterns:

Handler: [Handler class name]
Feature: [Brief description of what the handler does]

Requirements:
- Use CommandHandlerTestBase<T> or QueryHandlerTestBase<T> as base class
- Include these test scenarios:
  * Happy path with valid input
  * Validation failures (if applicable)
  * Business rule violations
  * Repository exceptions
  * Edge cases specific to the feature
- Use FluentAssertions for assertions
- Mock all dependencies using Moq
- Follow AAA pattern (Arrange, Act, Assert)
- Include verification of repository calls and logging
- Use existing test patterns from the codebase

Generate complete test class with all scenarios.
```

## 🎨 Phase 4: Frontend Implementation (45 min)

### Frontend Implementation Overview

The FleetXQ frontend has been implemented as a modern React application with the following comprehensive setup:

#### Technology Stack
- **React 18+** with TypeScript for type safety
- **Vite** as the build tool for fast development and optimized builds
- **Tailwind CSS v4** for utility-first styling with custom FleetXQ theming
- **Redux Toolkit** for predictable state management
- **React Router v6** for client-side routing with lazy loading
- **Axios** for HTTP client with interceptors
- **SignalR** for real-time communication
- **ESLint + Prettier** for code quality and formatting

#### Project Structure (Feature-Based Architecture)
```
/fleetxq-frontend
  /src
    /assets          - Static files, images, icons
    /components      - Reusable UI components
      Header.tsx     - Application header with user info
      Sidebar.tsx    - Navigation sidebar with menu items
      LoadingSpinner.tsx - Loading indicator component
    /features        - Domain-specific modules
      /auth          - Authentication logic
        LoginPage.tsx      - Login form component
        authSlice.ts       - Redux auth state management
      /dashboard     - Main dashboard
        DashboardPage.tsx  - Overview with stats and activity
      /vehicles      - Vehicle management
        VehiclesPage.tsx   - Vehicle CRUD operations
      /drivers       - Driver management
        DriversPage.tsx    - Driver management interface
      /telemetry     - Real-time data display
        TelemetryPage.tsx  - Telemetry visualization
      /alerts        - Alert management
        AlertsPage.tsx     - Alert handling interface
    /hooks           - Custom React hooks
      useAuth.ts     - Authentication hook
      useLocalStorage.ts - Local storage hook
    /layouts         - Page layout components
      MainLayout.tsx - Main app layout with sidebar/header
      AuthLayout.tsx - Authentication pages layout
    /routes          - Routing configuration
      index.tsx      - Route definitions with lazy loading
      ProtectedRoute.tsx - Authentication guard component
    /services        - API abstraction layer
      api.ts         - Axios configuration and service methods
    /store           - Redux store configuration
      index.ts       - Store setup with auth slice
    /types           - TypeScript type definitions
      index.ts       - Common interfaces and types
    /utils           - Utility functions
      index.ts       - Helper functions for formatting, etc.
```

#### Key Dependencies and Their Purposes
```json
{
  "dependencies": {
    "@headlessui/react": "UI components (modals, dropdowns)",
    "@heroicons/react": "SVG icon library",
    "@reduxjs/toolkit": "State management with RTK Query",
    "react-redux": "React bindings for Redux",
    "react-router-dom": "Client-side routing",
    "axios": "HTTP client for API calls",
    "@microsoft/signalr": "Real-time communication",
    "recharts": "Data visualization charts",
    "react-hook-form": "Form handling and validation",
    "yup": "Schema validation for forms"
  },
  "devDependencies": {
    "tailwindcss": "Utility-first CSS framework",
    "@tailwindcss/postcss": "PostCSS plugin for Tailwind v4",
    "eslint": "Code linting",
    "prettier": "Code formatting",
    "typescript": "Type checking"
  }
}
```

#### Build and Development Workflow
```bash
# Development server with hot reload
npm run dev

# Type checking without emitting files
npm run type-check

# Linting with auto-fix
npm run lint:fix

# Code formatting
npm run format

# Production build
npm run build

# Preview production build
npm run preview
```

## 🏗️ Layout and Architecture Details

### Main Application Layout Structure

#### MainLayout Component
```typescript
// MainLayout.tsx - Primary application shell
const MainLayout = () => {
  return (
    <div className="flex h-screen bg-gray-50">
      {/* Fixed sidebar navigation */}
      <Sidebar />

      {/* Main content area */}
      <div className="flex flex-1 flex-col overflow-hidden">
        {/* Header with user info and actions */}
        <Header />

        {/* Dynamic page content */}
        <main className="flex-1 overflow-y-auto p-6">
          <Outlet /> {/* React Router outlet for page content */}
        </main>
      </div>
    </div>
  );
};
```

#### Sidebar Navigation Structure
```typescript
// Navigation menu items with icons and routes
const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },
  { name: 'Vehicles', href: '/vehicles', icon: TruckIcon },
  { name: 'Drivers', href: '/drivers', icon: UserGroupIcon },
  { name: 'Telemetry', href: '/telemetry', icon: ChartBarIcon },
  { name: 'Alerts', href: '/alerts', icon: ExclamationTriangleIcon },
];

// Active state styling with Tailwind classes
className={({ isActive }) =>
  `sidebar-nav-item ${
    isActive ? 'sidebar-nav-item-active' : 'sidebar-nav-item-inactive'
  }`
}
```

#### Header Component Features
- User profile display with name and role
- Authentication status indicator
- Responsive design for mobile/desktop
- Integration with Redux auth state

#### Authentication Layout (AuthLayout)
```typescript
// AuthLayout.tsx - Centered layout for login/register
const AuthLayout = () => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-primary-600">FleetXQ</h1>
          <p className="mt-2 text-sm text-gray-600">Fleet Management System</p>
        </div>
        <Outlet /> {/* Login form or other auth components */}
      </div>
    </div>
  );
};
```

### Protected Routing System

#### Route Protection with Role-Based Access Control
```typescript
// ProtectedRoute.tsx - Authentication and authorization guard
const ProtectedRoute = ({ children, requiredRole }: ProtectedRouteProps) => {
  const { isAuthenticated, user } = useSelector((state: RootState) => state.auth);
  const location = useLocation();

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/auth/login" state={{ from: location }} replace />;
  }

  // Check role-based permissions
  if (requiredRole && user?.role !== requiredRole) {
    return <Navigate to="/dashboard" replace />;
  }

  return <>{children}</>;
};
```

#### Route Configuration with Lazy Loading
```typescript
// routes/index.tsx - Complete routing setup
export const router = createBrowserRouter([
  {
    path: '/',
    element: <Navigate to="/dashboard" replace />,
  },
  {
    path: '/auth',
    element: <AuthLayout />,
    children: [
      {
        path: 'login',
        element: (
          <Suspense fallback={<LoadingSpinner />}>
            <LoginPage />
          </Suspense>
        ),
      },
    ],
  },
  {
    path: '/',
    element: (
      <ProtectedRoute>
        <MainLayout />
      </ProtectedRoute>
    ),
    children: [
      { path: 'dashboard', element: <LazyWrapper><DashboardPage /></LazyWrapper> },
      { path: 'vehicles', element: <LazyWrapper><VehiclesPage /></LazyWrapper> },
      { path: 'drivers', element: <LazyWrapper><DriversPage /></LazyWrapper> },
      { path: 'telemetry', element: <LazyWrapper><TelemetryPage /></LazyWrapper> },
      { path: 'alerts', element: <LazyWrapper><AlertsPage /></LazyWrapper> },
    ],
  },
]);
```

## 🎯 Interview Talking Points - Frontend Architecture

### React Best Practices Implemented

#### 1. Component Composition and Reusability
- **Feature-based folder structure** for better maintainability
- **Compound components** for complex UI elements
- **Custom hooks** for shared logic (useAuth, useLocalStorage)
- **Higher-order components** for cross-cutting concerns

#### 2. State Management Strategy
```typescript
// Redux Toolkit slice example
const authSlice = createSlice({
  name: 'auth',
  initialState: {
    user: null,
    isAuthenticated: false,
    isLoading: false,
    error: null,
  },
  reducers: {
    loginStart: (state) => {
      state.isLoading = true;
      state.error = null;
    },
    loginSuccess: (state, action: PayloadAction<User>) => {
      state.isLoading = false;
      state.isAuthenticated = true;
      state.user = action.payload;
    },
    // ... other reducers
  },
});
```

#### 3. TypeScript Integration Benefits
- **Type safety** for props, state, and API responses
- **Interface definitions** for consistent data structures
- **Generic types** for reusable components
- **Strict null checks** to prevent runtime errors

### Tailwind CSS v4 Configuration and Custom Theming

#### 1. Modern CSS Architecture
```css
/* index.css - Tailwind v4 approach */
@import "tailwindcss";

/* Custom component classes */
.btn-primary {
  background-color: rgb(37 99 235);
  color: white;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background-color: rgb(29 78 216);
}
```

#### 2. FleetXQ Brand Integration
- **Custom color palette** with primary, secondary, success, warning, danger
- **Consistent spacing** and typography scales
- **Component-based styling** for maintainability
- **Responsive design** with mobile-first approach

### Redux Toolkit State Management Architecture

#### 1. Centralized State Structure
```typescript
// store/index.ts - Store configuration
export const store = configureStore({
  reducer: {
    auth: authSlice,
    vehicles: vehiclesSlice,
    drivers: driversSlice,
    telemetry: telemetrySlice,
    alerts: alertsSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
});
```

#### 2. Async Actions with RTK Query
- **Automatic caching** for API responses
- **Background refetching** for data freshness
- **Optimistic updates** for better UX
- **Error handling** with retry logic

### Routing Strategy with React Router v6

#### 1. Code Splitting Benefits
- **Lazy loading** reduces initial bundle size
- **Route-based splitting** for better performance
- **Suspense boundaries** for loading states
- **Error boundaries** for graceful error handling

#### 2. Authentication Flow
- **Protected routes** with role-based access
- **Redirect after login** to intended destination
- **Route guards** for unauthorized access prevention
- **Deep linking** support with state preservation

### Code Quality Tools Configuration

#### 1. ESLint Configuration
```javascript
// eslint.config.js - TypeScript-aware linting
export default [
  {
    files: ['**/*.{ts,tsx}'],
    extends: [
      js.configs.recommended,
      ...tseslint.configs.recommended,
      prettierConfig,
    ],
    rules: {
      '@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
      '@typescript-eslint/explicit-function-return-type': 'off',
      'prefer-const': 'error',
      'no-var': 'error',
    },
  },
];
```

#### 2. Prettier Integration
```json
// .prettierrc - Consistent code formatting
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "plugins": ["prettier-plugin-tailwindcss"]
}
```

## 🎨 Phase 4: Frontend Integration (30 min)

## 🚀 Project Generation Prompt - Frontend

### Complete Frontend Project Recreation Prompt

```
Create a comprehensive React TypeScript frontend for FleetXQ fleet management system following these exact specifications:

**Technology Stack:**
- React 18+ with TypeScript
- Vite as build tool
- Tailwind CSS v4 with custom theming
- Redux Toolkit for state management
- React Router v6 with lazy loading
- Axios for API integration
- SignalR for real-time updates
- ESLint + Prettier for code quality

**Exact Folder Structure:**
```
/src
  /assets - Static files, images, icons
  /components - Reusable UI components
    Header.tsx - App header with user info
    Sidebar.tsx - Navigation with menu items
    LoadingSpinner.tsx - Loading indicator
  /features - Domain-specific modules
    /auth
      LoginPage.tsx - Authentication form
      authSlice.ts - Redux auth state
    /dashboard
      DashboardPage.tsx - Main overview with stats
    /vehicles
      VehiclesPage.tsx - Vehicle CRUD operations
    /drivers
      DriversPage.tsx - Driver management
    /telemetry
      TelemetryPage.tsx - Real-time data display
    /alerts
      AlertsPage.tsx - Alert management
  /hooks - Custom React hooks
    useAuth.ts - Authentication hook
    useLocalStorage.ts - Local storage hook
  /layouts - Page layout components
    MainLayout.tsx - Main app shell
    AuthLayout.tsx - Auth pages layout
  /routes - Routing configuration
    index.tsx - Route definitions
    ProtectedRoute.tsx - Auth guard
  /services - API abstraction
    api.ts - Axios service layer
  /store - Redux configuration
    index.ts - Store setup
  /types - TypeScript definitions
    index.ts - Common interfaces
  /utils - Utility functions
    index.ts - Helper functions
```

**Component Hierarchy and Relationships:**
1. App.tsx - Root component with Redux Provider and RouterProvider
2. MainLayout.tsx - Contains Sidebar + Header + Outlet for pages
3. AuthLayout.tsx - Centered layout for login/register
4. ProtectedRoute.tsx - Guards routes based on authentication
5. Feature pages - Dashboard, Vehicles, Drivers, Telemetry, Alerts

**Styling Approach:**
- Tailwind CSS v4 with @import "tailwindcss"
- Custom FleetXQ theme colors (primary, secondary, success, warning, danger)
- Component classes: .btn-primary, .btn-secondary, .card, .input-field
- Responsive design with mobile-first approach
- Inter font integration

**State Management Patterns:**
- Redux Toolkit with feature-based slices
- Auth slice for user authentication state
- Async thunks for API calls
- Proper TypeScript integration with RootState type

**API Integration Setup:**
- Axios instance with base URL configuration
- Request interceptors for auth tokens
- Response interceptors for error handling
- Service layer abstraction for API calls

**Development Scripts:**
```json
{
  "scripts": {
    "dev": "vite",
    "build": "tsc -b && vite build",
    "lint": "eslint .",
    "lint:fix": "eslint . --fix",
    "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"",
    "type-check": "tsc --noEmit",
    "preview": "vite preview"
  }
}
```

**Key Implementation Requirements:**
1. Feature-based architecture for scalability
2. TypeScript strict mode with proper type definitions
3. Protected routing with role-based access control
4. Lazy loading for code splitting
5. Custom hooks for reusable logic
6. Consistent error handling and loading states
7. Responsive design with Tailwind utilities
8. ESLint + Prettier integration for code quality
9. Environment variable support for API configuration
10. Production-ready build optimization

**Dependencies to Install:**
```bash
# Core dependencies
npm install @headlessui/react @heroicons/react @reduxjs/toolkit react-redux react-router-dom axios @microsoft/signalr recharts react-hook-form @hookform/resolvers yup

# Development dependencies
npm install -D @tailwindcss/postcss prettier eslint-config-prettier eslint-plugin-prettier prettier-plugin-tailwindcss
```

Generate the complete project structure with all components, proper TypeScript types, Redux setup, routing configuration, and styling following the FleetXQ design system.
```

### Advanced Frontend Features Implementation

#### 1. Real-time Data Integration
```typescript
// hooks/useSignalR.ts - SignalR integration hook
export const useSignalR = (hubUrl: string) => {
  const [connection, setConnection] = useState<HubConnection | null>(null);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    const newConnection = new HubConnectionBuilder()
      .withUrl(hubUrl, {
        accessTokenFactory: () => localStorage.getItem('authToken') || '',
      })
      .withAutomaticReconnect()
      .build();

    setConnection(newConnection);

    newConnection.start()
      .then(() => {
        setIsConnected(true);
        console.log('SignalR Connected');
      })
      .catch(error => console.error('SignalR Connection Error:', error));

    return () => {
      newConnection.stop();
    };
  }, [hubUrl]);

  return { connection, isConnected };
};
```

#### 2. Form Handling with Validation
```typescript
// components/VehicleForm.tsx - Form with validation
const VehicleForm = ({ onSubmit, initialData }: VehicleFormProps) => {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<VehicleFormData>({
    resolver: yupResolver(vehicleSchema),
    defaultValues: initialData,
  });

  const onFormSubmit = async (data: VehicleFormData) => {
    try {
      await onSubmit(data);
      reset();
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  return (
    <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700">
          Vehicle Name
        </label>
        <input
          {...register('name')}
          className="input-field"
          placeholder="Enter vehicle name"
        />
        {errors.name && (
          <p className="text-sm text-red-600">{errors.name.message}</p>
        )}
      </div>

      <button
        type="submit"
        disabled={isSubmitting}
        className="btn-primary w-full"
      >
        {isSubmitting ? 'Saving...' : 'Save Vehicle'}
      </button>
    </form>
  );
};
```

#### 3. Data Visualization Components
```typescript
// components/TelemetryChart.tsx - Chart component
const TelemetryChart = ({ vehicleId, timeRange }: TelemetryChartProps) => {
  const [data, setData] = useState<TelemetryData[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const response = await apiService.get<TelemetryData[]>(
          `/telemetry/${vehicleId}/history?range=${timeRange}`
        );
        setData(response.data);
      } catch (error) {
        console.error('Failed to fetch telemetry data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [vehicleId, timeRange]);

  if (loading) return <LoadingSpinner />;

  return (
    <div className="card">
      <h3 className="text-lg font-medium mb-4">Vehicle Telemetry</h3>
      <ResponsiveContainer width="100%" height={300}>
        <LineChart data={data}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="timestamp" />
          <YAxis />
          <Tooltip />
          <Legend />
          <Line type="monotone" dataKey="speed" stroke="#2563eb" />
          <Line type="monotone" dataKey="fuelLevel" stroke="#dc2626" />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};
```

#### 4. Error Boundary Implementation
```typescript
// components/ErrorBoundary.tsx - Error handling
class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Something went wrong
            </h1>
            <p className="text-gray-600 mb-4">
              We're sorry, but something unexpected happened.
            </p>
            <button
              onClick={() => window.location.reload()}
              className="btn-primary"
            >
              Reload Page
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}
```

### React Component Structure
```jsx
// Vehicle Management Component
import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { fetchVehicles, createVehicle } from '../store/vehicleSlice';

const VehicleManagement = () => {
    const dispatch = useDispatch();
    const { vehicles, loading, error } = useSelector(state => state.vehicles);
    const [formData, setFormData] = useState({
        name: '',
        licensePlate: '',
        vehicleType: ''
    });

    useEffect(() => {
        dispatch(fetchVehicles());
    }, [dispatch]);

    const handleSubmit = async (e) => {
        e.preventDefault();
        try {
            await dispatch(createVehicle(formData)).unwrap();
            setFormData({ name: '', licensePlate: '', vehicleType: '' });
        } catch (error) {
            console.error('Failed to create vehicle:', error);
        }
    };

    return (
        <div className="vehicle-management">
            <form onSubmit={handleSubmit}>
                <input
                    type="text"
                    placeholder="Vehicle Name"
                    value={formData.name}
                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                    required
                />
                {/* Additional form fields */}
                <button type="submit" disabled={loading}>
                    {loading ? 'Creating...' : 'Create Vehicle'}
                </button>
            </form>
            
            <div className="vehicle-list">
                {vehicles.map(vehicle => (
                    <div key={vehicle.id} className="vehicle-card">
                        <h3>{vehicle.name}</h3>
                        <p>License: {vehicle.licensePlate}</p>
                        <p>Status: {vehicle.status}</p>
                    </div>
                ))}
            </div>
        </div>
    );
};
```

### API Service Layer
```javascript
// vehicleService.js
import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'https://localhost:7001/api';

const apiClient = axios.create({
    baseURL: API_BASE_URL,
    headers: {
        'Content-Type': 'application/json',
    },
});

// Add auth token to requests
apiClient.interceptors.request.use((config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
});

export const vehicleService = {
    getAll: () => apiClient.get('/vehicles'),
    getById: (id) => apiClient.get(`/vehicles/${id}`),
    create: (vehicle) => apiClient.post('/vehicles', vehicle),
    update: (id, vehicle) => apiClient.put(`/vehicles/${id}`, vehicle),
    delete: (id) => apiClient.delete(`/vehicles/${id}`),
};
```

### AI Prompt for Frontend Development
```
Create a React component for FleetXQ fleet management:

Component: [Component name and purpose]
Features needed:
- [List specific features like CRUD operations, real-time updates, etc.]
- API integration with backend endpoints
- Form handling with validation
- Loading and error states
- Responsive design

Requirements:
- Use functional components with hooks
- Implement Redux Toolkit for state management
- Include proper error handling
- Add loading indicators
- Use modern JavaScript (ES6+)
- Follow React best practices
- Include TypeScript types if applicable

API endpoints available: [List relevant endpoints]
```

## 📊 Phase 5: Data Visualization (25 min)

### Chart.js Integration
```javascript
// TelemetryChart.jsx
import React, { useEffect, useRef } from 'react';
import Chart from 'chart.js/auto';

const TelemetryChart = ({ vehicleId, timeRange = '24h' }) => {
    const chartRef = useRef(null);
    const chartInstance = useRef(null);

    useEffect(() => {
        const fetchTelemetryData = async () => {
            try {
                const response = await fetch(`/api/telemetry/${vehicleId}/history?range=${timeRange}`);
                const data = await response.json();
                
                if (chartInstance.current) {
                    chartInstance.current.destroy();
                }

                const ctx = chartRef.current.getContext('2d');
                chartInstance.current = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: data.timestamps,
                        datasets: [
                            {
                                label: 'Speed (km/h)',
                                data: data.speeds,
                                borderColor: 'rgb(75, 192, 192)',
                                tension: 0.1
                            },
                            {
                                label: 'Fuel Level (%)',
                                data: data.fuelLevels,
                                borderColor: 'rgb(255, 99, 132)',
                                tension: 0.1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            } catch (error) {
                console.error('Failed to fetch telemetry data:', error);
            }
        };

        fetchTelemetryData();
        
        return () => {
            if (chartInstance.current) {
                chartInstance.current.destroy();
            }
        };
    }, [vehicleId, timeRange]);

    return <canvas ref={chartRef} />;
};
```

### AI Prompt for Data Visualization
```
Create a data visualization component for FleetXQ dashboard:

Chart type: [Line chart, Bar chart, Pie chart, etc.]
Data source: [Telemetry data, Vehicle status, Alert trends, etc.]
Features needed:
- Real-time updates every [time interval]
- Interactive tooltips and legends
- Time range selection (1h, 24h, 7d, 30d)
- Export functionality
- Responsive design

Requirements:
- Use Chart.js or D3.js
- Implement proper data fetching with error handling
- Add loading states
- Include chart configuration options
- Support multiple data series
- Optimize for performance with large datasets

Data format: [Describe the expected data structure from API]
```

## 🚀 Advanced Features & Optimization

### SignalR Real-time Integration
```javascript
// signalRService.js
import * as signalR from '@microsoft/signalr';

class SignalRService {
    constructor() {
        this.connection = null;
    }

    async startConnection() {
        this.connection = new signalR.HubConnectionBuilder()
            .withUrl('/telemetryHub', {
                accessTokenFactory: () => localStorage.getItem('authToken')
            })
            .build();

        try {
            await this.connection.start();
            console.log('SignalR Connected');
        } catch (error) {
            console.error('SignalR Connection Error:', error);
        }
    }

    subscribeToVehicle(vehicleId, callback) {
        if (this.connection) {
            this.connection.invoke('SubscribeToVehicle', vehicleId);
            this.connection.on(`VehicleUpdate_${vehicleId}`, callback);
        }
    }

    unsubscribeFromVehicle(vehicleId) {
        if (this.connection) {
            this.connection.invoke('UnsubscribeFromVehicle', vehicleId);
            this.connection.off(`VehicleUpdate_${vehicleId}`);
        }
    }
}

export default new SignalRService();
```

## 💡 Interview Success Strategies

### Time Management Tips
1. **Start Simple**: Get basic CRUD working before adding complexity
2. **Use AI Strategically**: Generate boilerplate, then customize for requirements
3. **Test Early**: Write tests as you go, don't leave them for the end
4. **Communicate**: Explain your approach and ask clarifying questions

### Common Pitfalls to Avoid
- Don't over-engineer the solution
- Don't forget error handling and validation
- Don't skip the testing phase
- Don't ignore performance considerations

### Key Questions to Ask
- "What's the expected data volume for this feature?"
- "Are there any specific performance requirements?"
- "Should I prioritize real-time updates or data consistency?"
- "What level of error handling detail do you want to see?"

## 🎯 Final Checklist

### Before Starting Each Phase
- [ ] Understand the requirements clearly
- [ ] Plan your approach (2-3 minutes)
- [ ] Identify which AI prompts to use
- [ ] Set up your development environment

### During Development
- [ ] Use provided code templates as starting points
- [ ] Leverage AI for boilerplate generation
- [ ] Test functionality as you build
- [ ] Explain your decisions to the interviewer

### Before Moving to Next Phase
- [ ] Verify current functionality works
- [ ] Run any tests you've written
- [ ] Commit your changes (if using version control)
- [ ] Briefly summarize what you've accomplished

---

**Remember**: This is a collaborative coding session, not a test to trip you up. Use this guide as your reference, leverage AI tools effectively, and focus on demonstrating your problem-solving approach and technical communication skills.

## 🔧 Essential AI Prompts Library

### Database & SQL Prompts

#### Complex Query Generation
```
I need a SQL query for fleet management analysis:

Context: Fleet management system with tables: Vehicles, Drivers, TelemetryData, Alerts, Routes
Goal: [Specific analysis like "Find vehicles with unusual fuel consumption patterns"]
Time Period: [Last 24 hours, past week, current month]
Grouping: [By vehicle, by driver, by route, by time period]
Metrics: [Speed, fuel consumption, engine hours, alerts count]

Requirements:
- Use SQL Server syntax
- Include proper JOINs and WHERE clauses
- Add window functions for ranking/partitioning if needed
- Optimize for performance with appropriate indexes
- Return results suitable for dashboard display

Generate the complete query with comments explaining the logic.
```

#### Performance Optimization
```
Optimize this SQL query for a fleet management system:

[Paste your existing query here]

Context:
- Table sizes: Vehicles (~10K), TelemetryData (~1M records/day), Drivers (~1K)
- Query frequency: [Real-time, hourly reports, daily analytics]
- Performance target: [Sub-second response, under 5 seconds, etc.]

Please provide:
1. Optimized query with explanations
2. Recommended indexes
3. Alternative approaches if applicable
4. Potential bottlenecks to watch for
```

### Backend Development Prompts

#### CQRS Implementation
```
Generate a complete CQRS implementation for FleetXQ:

Feature: [Vehicle Management, Driver Assignment, Telemetry Processing, Alert Management]
Operation: [Create, Update, Delete, Get, List, Process, Assign]
Entity: [Vehicle, Driver, TelemetryData, Alert, Route]

Context:
- .NET 8 Web API with Clean Architecture
- MediatR for CQRS pattern
- FluentValidation for input validation
- AutoMapper for DTO mapping
- Entity Framework Core with repository pattern
- Result pattern for error handling

Generate:
1. Command/Query class with proper validation attributes
2. Handler class with business logic
3. Validator class using FluentValidation
4. DTO classes for request/response
5. Controller action method
6. Repository interface method (if new)

Follow existing codebase patterns and include proper error handling, logging, and async/await.
```

#### Repository Pattern
```
Create a repository interface and implementation for [EntityName] in FleetXQ:

Entity Details:
- Primary purpose: [Brief description]
- Key relationships: [Related entities]
- Special operations needed: [Domain-specific queries]

Requirements:
- Follow existing IRepository<T> pattern
- Include standard CRUD operations
- Add domain-specific query methods
- Use Entity Framework Core
- Include proper async/await and cancellation tokens
- Add appropriate error handling
- Follow database-first approach

Generate both interface and implementation with XML documentation.
```

#### API Controller Generation
```
Create a REST API controller for FleetXQ:

Controller: [ControllerName]Controller
Entity: [EntityName]
Operations needed: [GET, POST, PUT, DELETE, custom operations]

Requirements:
- Inherit from BaseApiController
- Use MediatR for command/query dispatch
- Include proper HTTP status codes
- Add authorization attributes based on roles:
  * Admin: Full access
  * Manager: Read/write operational data
  * Driver: Limited to assigned resources
- Include proper error handling with ApiResponse wrapper
- Add XML documentation for API docs
- Follow RESTful conventions

Generate complete controller with all CRUD operations and custom endpoints.
```

### Testing Prompts

#### Unit Test Generation
```
Generate comprehensive unit tests for [HandlerName] in FleetXQ:

Handler Type: [CommandHandler/QueryHandler]
Feature: [Brief description of functionality]
Dependencies: [List of injected dependencies]

Test Requirements:
- Use [CommandHandlerTestBase<T>/QueryHandlerTestBase<T>] as base class
- Include these scenarios:
  * Happy path with valid input
  * Invalid input validation failures
  * Business rule violations
  * Repository/service exceptions
  * Edge cases specific to the feature
- Use FluentAssertions for assertions
- Mock all dependencies with Moq
- Follow AAA pattern (Arrange, Act, Assert)
- Verify repository calls and logging
- Include cancellation token testing

Generate complete test class with all test methods and proper setup.
```

#### Integration Test Creation
```
Create integration tests for [FeatureName] API endpoints:

Endpoints to test:
- [List specific endpoints like GET /api/vehicles, POST /api/vehicles, etc.]

Requirements:
- Use WebApplicationFactory for test server
- Include authentication testing with different roles
- Test both success and error scenarios
- Validate HTTP status codes and response format
- Include database state verification
- Test concurrent access scenarios if applicable
- Use in-memory database for isolation

Generate complete integration test class with setup and teardown.
```

### Frontend Development Prompts

#### React Component Generation
```
Create a React component for FleetXQ dashboard:

Component: [ComponentName]
Purpose: [Brief description of functionality]
Data: [What data it displays/manages]

Features needed:
- [CRUD operations, real-time updates, filtering, pagination, etc.]
- Form handling with validation
- API integration with error handling
- Loading states and user feedback
- Responsive design

Technical Requirements:
- Use functional components with hooks
- Implement Redux Toolkit for state management
- Include TypeScript types
- Use modern JavaScript (ES6+)
- Follow React best practices
- Include proper error boundaries
- Add accessibility features

API endpoints: [List available endpoints]
State structure: [Describe expected Redux state shape]
```

#### State Management Setup
```
Create Redux Toolkit slice for [FeatureName] in FleetXQ:

Feature: [Vehicle management, driver assignment, telemetry monitoring, etc.]
Operations: [List CRUD and custom operations]
API endpoints: [List relevant endpoints]

Requirements:
- Use createSlice and createAsyncThunk
- Include proper error handling
- Add loading states for async operations
- Implement optimistic updates where appropriate
- Include proper TypeScript types
- Add selectors for computed state
- Handle real-time updates from SignalR

Generate complete slice with actions, reducers, and selectors.
```

#### Chart/Visualization Component
```
Create a data visualization component for FleetXQ:

Chart Type: [Line chart, bar chart, pie chart, heatmap, etc.]
Data Source: [Telemetry data, vehicle status, alert trends, performance metrics]
Update Frequency: [Real-time, every 30 seconds, hourly, etc.]

Features:
- Interactive tooltips and legends
- Time range selection (1h, 24h, 7d, 30d)
- Data filtering and grouping options
- Export functionality (PNG, PDF, CSV)
- Responsive design for mobile/desktop
- Real-time updates via SignalR

Technical Requirements:
- Use Chart.js or D3.js
- Implement proper data fetching with error handling
- Add loading states and empty state handling
- Optimize for performance with large datasets
- Include accessibility features
- Support multiple data series

Data format: [Describe expected API response structure]
```

## 🎯 Advanced Frontend Interview Talking Points

### Architecture Decision Rationale

#### Why Feature-Based Structure Over Traditional MVC?
- **Scalability**: Each feature is self-contained with its own components, state, and logic
- **Team Collaboration**: Multiple developers can work on different features without conflicts
- **Code Reusability**: Shared components in `/components` folder, feature-specific in feature folders
- **Maintainability**: Easy to locate and modify feature-specific code
- **Testing**: Feature-based tests are more focused and easier to maintain

#### TypeScript Integration Benefits
- **Compile-time Error Detection**: Catches type mismatches before runtime
- **Enhanced IDE Support**: Better autocomplete, refactoring, and navigation
- **API Contract Enforcement**: Ensures frontend matches backend API contracts
- **Reduced Runtime Errors**: Eliminates common JavaScript pitfalls
- **Better Documentation**: Types serve as living documentation

#### Tailwind CSS v4 vs Traditional CSS Approaches
- **Utility-First Benefits**: Faster development, consistent design system
- **Bundle Size Optimization**: Only includes used utilities in production
- **Responsive Design**: Built-in responsive utilities reduce custom media queries
- **Maintainability**: Changes in HTML don't require CSS file modifications
- **Team Consistency**: Prevents CSS specificity wars and naming conflicts

### Performance Optimization Strategies

#### Code Splitting Implementation
```typescript
// Lazy loading with React.lazy and Suspense
const DashboardPage = lazy(() => import('../features/dashboard/DashboardPage'));

// Route-based splitting reduces initial bundle size
const LazyWrapper = ({ children }: { children: React.ReactNode }) => (
  <Suspense fallback={<LoadingSpinner />}>{children}</Suspense>
);
```

#### State Management Optimization
- **Redux Toolkit Benefits**: Reduces boilerplate, includes Immer for immutability
- **Selective Subscriptions**: Components only re-render when relevant state changes
- **Memoization**: Use React.memo and useMemo for expensive computations
- **Normalized State**: Flat state structure for better performance with large datasets

#### Bundle Optimization Techniques
- **Tree Shaking**: Import only needed functions from libraries
- **Dynamic Imports**: Load features on-demand
- **Asset Optimization**: Compress images, use WebP format
- **CDN Integration**: Serve static assets from CDN

### Real-time Data Handling

#### SignalR Integration Strategy
```typescript
// Custom hook for SignalR connection management
const useSignalR = (hubUrl: string) => {
  const [connection, setConnection] = useState<HubConnection | null>(null);

  useEffect(() => {
    const newConnection = new HubConnectionBuilder()
      .withUrl(hubUrl, {
        accessTokenFactory: () => localStorage.getItem('authToken') || '',
      })
      .withAutomaticReconnect([0, 2000, 10000, 30000]) // Retry intervals
      .build();

    // Connection lifecycle management
    newConnection.start()
      .then(() => console.log('Connected to SignalR'))
      .catch(err => console.error('SignalR Connection Error:', err));

    setConnection(newConnection);

    return () => {
      newConnection.stop();
    };
  }, [hubUrl]);

  return connection;
};
```

#### Data Synchronization Patterns
- **Optimistic Updates**: Update UI immediately, rollback on error
- **Conflict Resolution**: Handle concurrent updates gracefully
- **Offline Support**: Cache data locally, sync when connection restored
- **Real-time Indicators**: Show connection status and data freshness

### Security Implementation

#### Authentication & Authorization
```typescript
// JWT token management with automatic refresh
const useAuth = () => {
  const [token, setToken] = useLocalStorage('authToken', null);

  const refreshToken = useCallback(async () => {
    try {
      const response = await apiService.post('/auth/refresh');
      setToken(response.data.token);
      return response.data.token;
    } catch (error) {
      logout();
      throw error;
    }
  }, [setToken]);

  // Automatic token refresh before expiration
  useEffect(() => {
    if (token) {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const expirationTime = payload.exp * 1000;
      const refreshTime = expirationTime - Date.now() - 60000; // 1 minute before expiry

      const timeoutId = setTimeout(refreshToken, refreshTime);
      return () => clearTimeout(timeoutId);
    }
  }, [token, refreshToken]);
};
```

#### Input Validation & Sanitization
- **Client-side Validation**: Immediate user feedback with react-hook-form + yup
- **XSS Prevention**: Sanitize user inputs, use dangerouslySetInnerHTML carefully
- **CSRF Protection**: Include CSRF tokens in API requests
- **Content Security Policy**: Implement CSP headers for additional security

### Testing Strategy

#### Component Testing Approach
```typescript
// Example component test with React Testing Library
describe('VehicleForm', () => {
  it('should submit form with valid data', async () => {
    const mockOnSubmit = jest.fn();
    render(<VehicleForm onSubmit={mockOnSubmit} />);

    // User interactions
    await user.type(screen.getByLabelText(/vehicle name/i), 'Test Vehicle');
    await user.type(screen.getByLabelText(/license plate/i), 'ABC-123');
    await user.click(screen.getByRole('button', { name: /save/i }));

    // Assertions
    expect(mockOnSubmit).toHaveBeenCalledWith({
      name: 'Test Vehicle',
      licensePlate: 'ABC-123',
    });
  });
});
```

#### Integration Testing Strategy
- **API Integration**: Mock API responses for consistent testing
- **State Management**: Test Redux actions and reducers
- **Routing**: Test navigation and protected routes
- **Real-time Features**: Mock SignalR connections

### Accessibility (A11Y) Implementation

#### WCAG Compliance Features
- **Keyboard Navigation**: All interactive elements accessible via keyboard
- **Screen Reader Support**: Proper ARIA labels and semantic HTML
- **Color Contrast**: Meet WCAG AA standards for text contrast
- **Focus Management**: Visible focus indicators and logical tab order

#### Implementation Examples
```typescript
// Accessible form component
const AccessibleForm = () => {
  return (
    <form role="form" aria-labelledby="form-title">
      <h2 id="form-title">Vehicle Information</h2>

      <label htmlFor="vehicle-name">
        Vehicle Name
        <span aria-label="required" className="text-red-500">*</span>
      </label>
      <input
        id="vehicle-name"
        type="text"
        required
        aria-describedby="name-error"
        aria-invalid={errors.name ? 'true' : 'false'}
      />
      {errors.name && (
        <div id="name-error" role="alert" className="text-red-600">
          {errors.name.message}
        </div>
      )}
    </form>
  );
};
```

### Mobile-First Responsive Design

#### Breakpoint Strategy
```css
/* Tailwind CSS responsive design approach */
.dashboard-grid {
  @apply grid grid-cols-1 gap-4;
  @apply md:grid-cols-2 md:gap-6;
  @apply lg:grid-cols-3 lg:gap-8;
  @apply xl:grid-cols-4;
}

/* Custom breakpoints for fleet management */
@media (min-width: 1440px) {
  .dashboard-grid {
    grid-template-columns: repeat(5, 1fr);
  }
}
```

#### Touch-Friendly Interface
- **Minimum Touch Targets**: 44px minimum for touch elements
- **Gesture Support**: Swipe navigation for mobile tables
- **Progressive Enhancement**: Core functionality works without JavaScript
- **Offline Capabilities**: Service worker for offline data access

## 🎯 Phase-Specific Quick Start Commands

### Phase 1: Database Setup
```bash
# Create new migration
dotnet ef migrations add InitialCreate --project FleetXQ.Infrastructure

# Update database
dotnet ef database update --project FleetXQ.Infrastructure

# Scaffold existing database
dotnet ef dbcontext scaffold "Server=localhost;Database=FleetXQ;Trusted_Connection=true;" Microsoft.EntityFrameworkCore.SqlServer --output-dir Models --data-annotations
```

### Phase 2: Backend API
```bash
# Create new Web API project
dotnet new webapi -n FleetXQ.Api

# Add required packages
dotnet add package MediatR
dotnet add package FluentValidation.AspNetCore
dotnet add package AutoMapper.Extensions.Microsoft.DependencyInjection
dotnet add package Microsoft.EntityFrameworkCore.SqlServer

# Run the API
dotnet run --project FleetXQ.Api
```

### Phase 3: Testing Setup
```bash
# Create test project
dotnet new xunit -n FleetXQ.Application.Tests

# Add test packages
dotnet add package Moq
dotnet add package FluentAssertions
dotnet add package Microsoft.AspNetCore.Mvc.Testing

# Run tests
dotnet test
```

### Phase 4: Frontend Setup
```bash
# Create React app
npx create-react-app fleetxq-frontend --template typescript

# Add required packages
npm install @reduxjs/toolkit react-redux axios chart.js react-chartjs-2 @microsoft/signalr

# Start development server
npm start
```

## 🔍 Debugging & Troubleshooting Guide

### Common Backend Issues

#### MediatR Handler Not Found
```csharp
// Ensure handler is registered in DI container
services.AddMediatR(typeof(CreateVehicleCommandHandler).Assembly);

// Check handler implements correct interface
public class CreateVehicleCommandHandler : IRequestHandler<CreateVehicleCommand, Result<VehicleDto>>
```

#### EF Core Migration Issues
```bash
# Reset migrations
dotnet ef database drop
dotnet ef migrations remove
dotnet ef migrations add InitialCreate
dotnet ef database update
```

#### Validation Not Working
```csharp
// Register FluentValidation
services.AddFluentValidationAutoValidation();
services.AddValidatorsFromAssembly(typeof(CreateVehicleCommandValidator).Assembly);
```

### Common Frontend Issues

#### CORS Errors
```csharp
// In Program.cs
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowReactApp",
        policy => policy
            .WithOrigins("http://localhost:3000")
            .AllowAnyHeader()
            .AllowAnyMethod()
            .AllowCredentials());
});

app.UseCors("AllowReactApp");
```

#### SignalR Connection Issues
```javascript
// Check authentication token
const connection = new signalR.HubConnectionBuilder()
    .withUrl('/telemetryHub', {
        accessTokenFactory: () => localStorage.getItem('authToken')
    })
    .configureLogging(signalR.LogLevel.Debug) // Add for debugging
    .build();
```

## 📚 Quick Reference APIs

### FleetXQ API Endpoints
```
Authentication:
POST /api/auth/login
POST /api/auth/refresh
POST /api/auth/logout

Vehicles:
GET /api/vehicles
GET /api/vehicles/{id}
POST /api/vehicles
PUT /api/vehicles/{id}
DELETE /api/vehicles/{id}
PUT /api/vehicles/{id}/status

Drivers:
GET /api/drivers
POST /api/drivers
PUT /api/drivers/{id}/assign-vehicle

Telemetry:
GET /api/telemetry/{vehicleId}/latest
GET /api/telemetry/{vehicleId}/history
POST /api/telemetry/batch

Alerts:
GET /api/alerts
POST /api/alerts/{id}/acknowledge
GET /api/alerts/active
```

### HTTP Status Codes Reference
```
200 OK - Successful GET, PUT
201 Created - Successful POST
204 No Content - Successful DELETE
400 Bad Request - Validation errors
401 Unauthorized - Authentication required
403 Forbidden - Insufficient permissions
404 Not Found - Resource doesn't exist
409 Conflict - Resource already exists
500 Internal Server Error - Server error
```

Good luck with your interview! 🚀
