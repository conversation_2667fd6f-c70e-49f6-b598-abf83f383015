import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import VehicleForm from '../VehicleForm';
import { vehiclesSlice } from '../../vehiclesSlice';
import type { Vehicle } from '../../../../types';

// Mock icons
jest.mock('@heroicons/react/24/outline', () => ({
  XMarkIcon: () => <div data-testid="x-mark-icon" />,
}));

// Mock LoadingSpinner
jest.mock('../../../../components/LoadingSpinner', () => {
  return function LoadingSpinner({ size }: { size?: string }) {
    return <div data-testid="loading-spinner" data-size={size} />;
  };
});

const mockVehicle: Vehicle = {
  id: 'vehicle-1',
  vehicleName: 'Fleet Vehicle 001',
  make: 'Ford',
  model: 'Transit',
  year: 2022,
  licensePlate: 'ABC-123',
  vin: '1HGBH41JXMN109186',
  vehicleType: 'Van',
  brand: 'Ford',
  color: 'White',
  fuelType: 'Diesel',
  fuelTankCapacity: 80,
  status: 'active',
  driverId: 'driver-1',
  currentMileage: 15000,
  lastMaintenanceDate: '2024-01-15T10:00:00Z',
  nextMaintenanceDate: '2024-04-15T10:00:00Z',
  currentLocation: undefined,
  currentSpeed: undefined,
  currentFuelLevel: undefined,
  engineStatus: undefined,
  lastTelemetryUpdate: undefined,
  isMoving: false,
  needsMaintenance: false,
  isAvailable: true,
  requiresImmediateAttention: false,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-20T14:30:00Z'
};

const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      vehicles: vehiclesSlice.reducer,
    },
    preloadedState: {
      vehicles: {
        vehicles: [],
        currentVehicle: null,
        selectedVehicles: [],
        vehicleAnalytics: {},
        maintenanceAlerts: [],
        assignmentHistory: [],
        maintenanceHistory: [],
        realTimeUpdates: [],
        isLoading: false,
        isCreating: false,
        isUpdating: false,
        isDeleting: false,
        isBulkOperating: false,
        isLoadingAnalytics: false,
        isLoadingMaintenanceAlerts: false,
        error: null,
        bulkOperationError: null,
        analyticsError: null,
        pagination: {
          page: 1,
          pageSize: 10,
          total: 0,
          totalPages: 0,
        },
        filters: {},
        assignmentConflicts: [],
        ...initialState,
      },
    },
  });
};

const renderWithStore = (component: React.ReactElement, initialState = {}) => {
  const store = createMockStore(initialState);
  return {
    ...render(<Provider store={store}>{component}</Provider>),
    store,
  };
};

describe('VehicleForm', () => {
  const defaultProps = {
    vehicle: null,
    isOpen: true,
    onClose: jest.fn(),
    onSuccess: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders create form when no vehicle is provided', () => {
    renderWithStore(<VehicleForm {...defaultProps} />);

    expect(screen.getByText('Add New Vehicle')).toBeInTheDocument();
    expect(screen.getByText('Create Vehicle')).toBeInTheDocument();
  });

  it('renders edit form when vehicle is provided', () => {
    renderWithStore(<VehicleForm {...defaultProps} vehicle={mockVehicle} />);

    expect(screen.getByText('Edit Vehicle')).toBeInTheDocument();
    expect(screen.getByText('Update Vehicle')).toBeInTheDocument();
  });

  it('does not render when isOpen is false', () => {
    renderWithStore(<VehicleForm {...defaultProps} isOpen={false} />);

    expect(screen.queryByText('Add New Vehicle')).not.toBeInTheDocument();
  });

  it('populates form fields when editing a vehicle', () => {
    renderWithStore(<VehicleForm {...defaultProps} vehicle={mockVehicle} />);

    expect(screen.getByDisplayValue('Fleet Vehicle 001')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Ford')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Transit')).toBeInTheDocument();
    expect(screen.getByDisplayValue('2022')).toBeInTheDocument();
    expect(screen.getByDisplayValue('ABC-123')).toBeInTheDocument();
    expect(screen.getByDisplayValue('1HGBH41JXMN109186')).toBeInTheDocument();
  });

  it('shows validation errors for required fields', async () => {
    const user = userEvent.setup();
    renderWithStore(<VehicleForm {...defaultProps} />);

    const submitButton = screen.getByText('Create Vehicle');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Vehicle name is required')).toBeInTheDocument();
      expect(screen.getByText('Make is required')).toBeInTheDocument();
      expect(screen.getByText('Model is required')).toBeInTheDocument();
      expect(screen.getByText('License plate is required')).toBeInTheDocument();
      expect(screen.getByText('VIN is required')).toBeInTheDocument();
    });
  });

  it('validates VIN format correctly', async () => {
    const user = userEvent.setup();
    renderWithStore(<VehicleForm {...defaultProps} />);

    const vinInput = screen.getByPlaceholderText('17-character VIN');
    await user.type(vinInput, 'INVALID_VIN');

    const submitButton = screen.getByText('Create Vehicle');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('VIN must be exactly 17 characters')).toBeInTheDocument();
    });
  });

  it('validates year range correctly', async () => {
    const user = userEvent.setup();
    renderWithStore(<VehicleForm {...defaultProps} />);

    const yearInput = screen.getByRole('spinbutton', { name: /year/i });
    await user.clear(yearInput);
    await user.type(yearInput, '1800');

    const submitButton = screen.getByText('Create Vehicle');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Year must be 1900 or later')).toBeInTheDocument();
    });
  });

  it('calls onClose when cancel button is clicked', async () => {
    const user = userEvent.setup();
    const onClose = jest.fn();
    renderWithStore(<VehicleForm {...defaultProps} onClose={onClose} />);

    const cancelButton = screen.getByText('Cancel');
    await user.click(cancelButton);

    expect(onClose).toHaveBeenCalled();
  });

  it('calls onClose when X button is clicked', async () => {
    const user = userEvent.setup();
    const onClose = jest.fn();
    renderWithStore(<VehicleForm {...defaultProps} onClose={onClose} />);

    const closeButton = screen.getByTestId('x-mark-icon').parentElement!;
    await user.click(closeButton);

    expect(onClose).toHaveBeenCalled();
  });

  it('disables form when loading', () => {
    renderWithStore(<VehicleForm {...defaultProps} />, { isCreating: true });

    const vehicleNameInput = screen.getByPlaceholderText('e.g., Fleet Vehicle 001');
    const submitButton = screen.getByText('Create Vehicle');

    expect(vehicleNameInput).toBeDisabled();
    expect(submitButton).toBeDisabled();
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });

  it('shows error message when there is an error', () => {
    const errorMessage = 'Failed to create vehicle';
    renderWithStore(<VehicleForm {...defaultProps} />, { error: errorMessage });

    expect(screen.getByText(errorMessage)).toBeInTheDocument();
  });

  it('resets form when switching from edit to create mode', () => {
    const { rerender } = renderWithStore(<VehicleForm {...defaultProps} vehicle={mockVehicle} />);

    // Verify form is populated
    expect(screen.getByDisplayValue('Fleet Vehicle 001')).toBeInTheDocument();

    // Switch to create mode
    rerender(
      <Provider store={createMockStore()}>
        <VehicleForm {...defaultProps} vehicle={null} />
      </Provider>
    );

    // Verify form is reset
    const vehicleNameInput = screen.getByPlaceholderText('e.g., Fleet Vehicle 001');
    expect(vehicleNameInput).toHaveValue('');
  });

  it('validates fuel tank capacity range', async () => {
    const user = userEvent.setup();
    renderWithStore(<VehicleForm {...defaultProps} />);

    const fuelCapacityInput = screen.getByPlaceholderText('e.g., 60');
    await user.type(fuelCapacityInput, '2000');

    const submitButton = screen.getByText('Create Vehicle');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Fuel tank capacity must be less than 1000 liters')).toBeInTheDocument();
    });
  });

  it('shows available drivers in dropdown', () => {
    renderWithStore(<VehicleForm {...defaultProps} />);

    const driverSelect = screen.getByRole('combobox', { name: /assigned driver/i });
    expect(driverSelect).toBeInTheDocument();
    
    // Check for default option
    expect(screen.getByText('No driver assigned')).toBeInTheDocument();
  });

  it('allows selecting vehicle type from dropdown', async () => {
    const user = userEvent.setup();
    renderWithStore(<VehicleForm {...defaultProps} />);

    const vehicleTypeSelect = screen.getByRole('combobox', { name: /vehicle type/i });
    await user.selectOptions(vehicleTypeSelect, 'Truck');

    expect(screen.getByDisplayValue('Truck')).toBeInTheDocument();
  });

  it('allows selecting fuel type from dropdown', async () => {
    const user = userEvent.setup();
    renderWithStore(<VehicleForm {...defaultProps} />);

    const fuelTypeSelect = screen.getByRole('combobox', { name: /fuel type/i });
    await user.selectOptions(fuelTypeSelect, 'Electric');

    expect(screen.getByDisplayValue('Electric')).toBeInTheDocument();
  });

  it('allows selecting status from dropdown', async () => {
    const user = userEvent.setup();
    renderWithStore(<VehicleForm {...defaultProps} />);

    const statusSelect = screen.getByRole('combobox', { name: /status/i });
    await user.selectOptions(statusSelect, 'maintenance');

    expect(screen.getByDisplayValue('maintenance')).toBeInTheDocument();
  });

  it('disables submit button when form is not dirty', () => {
    renderWithStore(<VehicleForm {...defaultProps} />);

    const submitButton = screen.getByText('Create Vehicle');
    expect(submitButton).toBeDisabled();
  });

  it('enables submit button when form is dirty and valid', async () => {
    const user = userEvent.setup();
    renderWithStore(<VehicleForm {...defaultProps} />);

    // Fill in required fields
    await user.type(screen.getByPlaceholderText('e.g., Fleet Vehicle 001'), 'Test Vehicle');
    await user.type(screen.getByPlaceholderText('e.g., Ford'), 'Toyota');
    await user.type(screen.getByPlaceholderText('e.g., Transit'), 'Camry');
    await user.type(screen.getByPlaceholderText('e.g., ABC-123'), 'XYZ-789');
    await user.type(screen.getByPlaceholderText('17-character VIN'), '1HGBH41JXMN109187');

    const submitButton = screen.getByText('Create Vehicle');
    
    await waitFor(() => {
      expect(submitButton).not.toBeDisabled();
    });
  });

  it('shows correct form title based on mode', () => {
    const { rerender } = renderWithStore(<VehicleForm {...defaultProps} />);
    expect(screen.getByText('Add New Vehicle')).toBeInTheDocument();

    rerender(
      <Provider store={createMockStore()}>
        <VehicleForm {...defaultProps} vehicle={mockVehicle} />
      </Provider>
    );
    expect(screen.getByText('Edit Vehicle')).toBeInTheDocument();
  });

  it('prevents closing when form is loading', async () => {
    const user = userEvent.setup();
    const onClose = jest.fn();
    renderWithStore(<VehicleForm {...defaultProps} onClose={onClose} />, { isCreating: true });

    const closeButton = screen.getByTestId('x-mark-icon').parentElement!;
    await user.click(closeButton);

    expect(onClose).not.toHaveBeenCalled();
  });
});
