import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import VehicleCard from '../VehicleCard';
import type { Vehicle, VehicleMaintenanceAlert } from '../../../../types';

// Mock icons
jest.mock('@heroicons/react/24/outline', () => ({
  MapPinIcon: () => <div data-testid="map-pin-icon" />,
  FuelIcon: () => <div data-testid="fuel-icon" />,
  CogIcon: () => <div data-testid="cog-icon" />,
  ExclamationTriangleIcon: () => <div data-testid="exclamation-triangle-icon" />,
  ClockIcon: () => <div data-testid="clock-icon" />,
  UserIcon: () => <div data-testid="user-icon" />,
  TruckIcon: () => <div data-testid="truck-icon" />,
}));

jest.mock('@heroicons/react/24/solid', () => ({
  CheckCircleIcon: () => <div data-testid="check-circle-icon" />,
  XCircleIcon: () => <div data-testid="x-circle-icon" />,
  PlayIcon: () => <div data-testid="play-icon" />,
  PauseIcon: () => <div data-testid="pause-icon" />,
}));

const mockVehicle: Vehicle = {
  id: 'vehicle-1',
  vehicleName: 'Fleet Vehicle 001',
  make: 'Ford',
  model: 'Transit',
  year: 2022,
  licensePlate: 'ABC-123',
  vin: '1HGBH41JXMN109186',
  vehicleType: 'Van',
  brand: 'Ford',
  color: 'White',
  fuelType: 'Diesel',
  fuelTankCapacity: 80,
  status: 'active',
  driverId: 'driver-1',
  currentMileage: 15000,
  lastMaintenanceDate: '2024-01-15T10:00:00Z',
  nextMaintenanceDate: '2024-04-15T10:00:00Z',
  currentLocation: {
    latitude: 40.7128,
    longitude: -74.0060,
    address: '123 Main St, New York, NY',
    timestamp: '2024-01-20T14:30:00Z'
  },
  currentSpeed: 45,
  currentFuelLevel: 75,
  engineStatus: 'on',
  lastTelemetryUpdate: '2024-01-20T14:30:00Z',
  isMoving: true,
  needsMaintenance: false,
  isAvailable: true,
  requiresImmediateAttention: false,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-20T14:30:00Z'
};

const mockMaintenanceAlerts: VehicleMaintenanceAlert[] = [
  {
    id: 'alert-1',
    vehicleId: 'vehicle-1',
    type: 'scheduled',
    title: 'Oil Change Due',
    description: 'Regular oil change maintenance',
    dueDate: '2024-02-01T00:00:00Z',
    priority: 'medium',
    estimatedCost: 120,
    createdAt: '2024-01-15T00:00:00Z'
  },
  {
    id: 'alert-2',
    vehicleId: 'vehicle-1',
    type: 'urgent',
    title: 'Brake Inspection',
    description: 'Urgent brake system inspection required',
    dueDate: '2024-01-25T00:00:00Z',
    priority: 'critical',
    estimatedCost: 350,
    createdAt: '2024-01-20T00:00:00Z'
  }
];

describe('VehicleCard', () => {
  const defaultProps = {
    vehicle: mockVehicle,
    realTimeStatus: 'moving' as const,
    maintenanceAlerts: [],
    onSelect: jest.fn(),
    onViewDetails: jest.fn(),
    onAssignDriver: jest.fn(),
    isSelected: false,
    showSelection: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders vehicle information correctly', () => {
    render(<VehicleCard {...defaultProps} />);

    expect(screen.getByText('Fleet Vehicle 001')).toBeInTheDocument();
    expect(screen.getByText('ABC-123')).toBeInTheDocument();
    expect(screen.getByText('active')).toBeInTheDocument();
    expect(screen.getByText('moving')).toBeInTheDocument();
  });

  it('displays vehicle location when available', () => {
    render(<VehicleCard {...defaultProps} />);

    expect(screen.getByText('123 Main St, New York, NY')).toBeInTheDocument();
  });

  it('shows unknown location when location is not available', () => {
    const vehicleWithoutLocation = {
      ...mockVehicle,
      currentLocation: undefined
    };

    render(<VehicleCard {...defaultProps} vehicle={vehicleWithoutLocation} />);

    expect(screen.getByText('Unknown')).toBeInTheDocument();
  });

  it('displays fuel level correctly', () => {
    render(<VehicleCard {...defaultProps} />);

    expect(screen.getByText('75%')).toBeInTheDocument();
  });

  it('shows N/A when fuel level is not available', () => {
    const vehicleWithoutFuel = {
      ...mockVehicle,
      currentFuelLevel: undefined
    };

    render(<VehicleCard {...defaultProps} vehicle={vehicleWithoutFuel} />);

    expect(screen.getByText('N/A')).toBeInTheDocument();
  });

  it('displays engine status correctly', () => {
    render(<VehicleCard {...defaultProps} />);

    expect(screen.getByText('On')).toBeInTheDocument();
  });

  it('shows driver assignment status', () => {
    render(<VehicleCard {...defaultProps} />);

    expect(screen.getByText('Assigned')).toBeInTheDocument();
  });

  it('shows unassigned when no driver is assigned', () => {
    const vehicleWithoutDriver = {
      ...mockVehicle,
      driverId: undefined
    };

    render(<VehicleCard {...defaultProps} vehicle={vehicleWithoutDriver} />);

    expect(screen.getByText('Unassigned')).toBeInTheDocument();
  });

  it('displays mileage correctly', () => {
    render(<VehicleCard {...defaultProps} />);

    expect(screen.getByText('Mileage: 15,000 mi')).toBeInTheDocument();
  });

  it('shows selection checkbox when showSelection is true', () => {
    render(<VehicleCard {...defaultProps} showSelection={true} />);

    const checkbox = screen.getByRole('checkbox');
    expect(checkbox).toBeInTheDocument();
  });

  it('hides selection checkbox when showSelection is false', () => {
    render(<VehicleCard {...defaultProps} showSelection={false} />);

    const checkbox = screen.queryByRole('checkbox');
    expect(checkbox).not.toBeInTheDocument();
  });

  it('calls onSelect when checkbox is clicked', () => {
    const onSelect = jest.fn();
    render(<VehicleCard {...defaultProps} showSelection={true} onSelect={onSelect} />);

    const checkbox = screen.getByRole('checkbox');
    fireEvent.click(checkbox);

    expect(onSelect).toHaveBeenCalledWith('vehicle-1');
  });

  it('calls onViewDetails when View Details button is clicked', () => {
    const onViewDetails = jest.fn();
    render(<VehicleCard {...defaultProps} onViewDetails={onViewDetails} />);

    const viewDetailsButton = screen.getByText('View Details');
    fireEvent.click(viewDetailsButton);

    expect(onViewDetails).toHaveBeenCalledWith('vehicle-1');
  });

  it('shows Assign Driver button when no driver is assigned', () => {
    const vehicleWithoutDriver = {
      ...mockVehicle,
      driverId: undefined
    };

    render(<VehicleCard {...defaultProps} vehicle={vehicleWithoutDriver} />);

    expect(screen.getByText('Assign Driver')).toBeInTheDocument();
  });

  it('hides Assign Driver button when driver is assigned', () => {
    render(<VehicleCard {...defaultProps} />);

    expect(screen.queryByText('Assign Driver')).not.toBeInTheDocument();
  });

  it('calls onAssignDriver when Assign Driver button is clicked', () => {
    const onAssignDriver = jest.fn();
    const vehicleWithoutDriver = {
      ...mockVehicle,
      driverId: undefined
    };

    render(<VehicleCard {...defaultProps} vehicle={vehicleWithoutDriver} onAssignDriver={onAssignDriver} />);

    const assignDriverButton = screen.getByText('Assign Driver');
    fireEvent.click(assignDriverButton);

    expect(onAssignDriver).toHaveBeenCalledWith('vehicle-1');
  });

  it('displays maintenance alerts correctly', () => {
    render(<VehicleCard {...defaultProps} maintenanceAlerts={mockMaintenanceAlerts} />);

    // Should show critical alerts
    expect(screen.getByText('1')).toBeInTheDocument(); // Critical alert count
  });

  it('applies selected styling when isSelected is true', () => {
    const { container } = render(<VehicleCard {...defaultProps} isSelected={true} />);

    const cardElement = container.firstChild as HTMLElement;
    expect(cardElement).toHaveClass('ring-2', 'ring-blue-500', 'border-blue-200');
  });

  it('does not apply selected styling when isSelected is false', () => {
    const { container } = render(<VehicleCard {...defaultProps} isSelected={false} />);

    const cardElement = container.firstChild as HTMLElement;
    expect(cardElement).not.toHaveClass('ring-2', 'ring-blue-500', 'border-blue-200');
  });

  it('displays correct real-time status colors', () => {
    const { rerender } = render(<VehicleCard {...defaultProps} realTimeStatus="online" />);
    expect(screen.getByText('online')).toBeInTheDocument();

    rerender(<VehicleCard {...defaultProps} realTimeStatus="offline" />);
    expect(screen.getByText('offline')).toBeInTheDocument();

    rerender(<VehicleCard {...defaultProps} realTimeStatus="idle" />);
    expect(screen.getByText('idle')).toBeInTheDocument();
  });

  it('formats last update time correctly', () => {
    // Mock Date.now to return a fixed time for consistent testing
    const mockNow = new Date('2024-01-20T14:35:00Z').getTime();
    jest.spyOn(Date, 'now').mockReturnValue(mockNow);

    render(<VehicleCard {...defaultProps} />);

    expect(screen.getByText('Updated 5m ago')).toBeInTheDocument();

    jest.restoreAllMocks();
  });

  it('shows Never when no telemetry update is available', () => {
    const vehicleWithoutTelemetry = {
      ...mockVehicle,
      lastTelemetryUpdate: undefined
    };

    render(<VehicleCard {...defaultProps} vehicle={vehicleWithoutTelemetry} />);

    expect(screen.getByText('Updated Never')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    const { container } = render(<VehicleCard {...defaultProps} className="custom-class" />);

    const cardElement = container.firstChild as HTMLElement;
    expect(cardElement).toHaveClass('custom-class');
  });

  it('displays fuel level bar with correct color based on level', () => {
    // Test low fuel level (red)
    const lowFuelVehicle = { ...mockVehicle, currentFuelLevel: 15 };
    const { rerender } = render(<VehicleCard {...defaultProps} vehicle={lowFuelVehicle} />);
    
    // Test medium fuel level (yellow)
    const mediumFuelVehicle = { ...mockVehicle, currentFuelLevel: 35 };
    rerender(<VehicleCard {...defaultProps} vehicle={mediumFuelVehicle} />);
    
    // Test high fuel level (green)
    const highFuelVehicle = { ...mockVehicle, currentFuelLevel: 80 };
    rerender(<VehicleCard {...defaultProps} vehicle={highFuelVehicle} />);
    
    // All should render without errors
    expect(screen.getByText('80%')).toBeInTheDocument();
  });
});
