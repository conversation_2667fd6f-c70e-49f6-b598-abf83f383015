import { useEffect, useState, useCallback, useRef } from 'react';
import { useAppSelector } from '../store/hooks';
import { selectIsAuthenticated, selectUser } from '../features/auth/authSlice';
import getSignalRService, { type ConnectionStatus } from '../services/signalRService';
import { tokenStorage } from '../utils/tokenStorage';

export interface UseSignalROptions {
  autoConnect?: boolean;
  groups?: string[];
  onConnected?: () => void;
  onDisconnected?: () => void;
  onReconnecting?: () => void;
  onReconnected?: () => void;
  onError?: (error: string) => void;
}

export interface UseSignalRReturn {
  connectionStatus: ConnectionStatus;
  isConnected: boolean;
  connect: () => Promise<void>;
  disconnect: () => Promise<void>;
  joinGroup: (groupName: string) => Promise<void>;
  leaveGroup: (groupName: string) => Promise<void>;
  on: (event: string, callback: Function) => void;
  off: (event: string, callback: Function) => void;
  requestNotificationPermission: () => Promise<NotificationPermission>;
}

export const useSignalR = (options: UseSignalROptions = {}): UseSignalRReturn => {
  const {
    autoConnect = true,
    groups = [],
    onConnected,
    onDisconnected,
    onReconnecting,
    onReconnected,
    onError,
  } = options;

  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const user = useAppSelector(selectUser);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    state: 0, // Disconnected
    isConnected: false,
    reconnectAttempts: 0,
  });

  const signalRServiceRef = useRef<ReturnType<typeof getSignalRService> | null>(null);
  const joinedGroupsRef = useRef<Set<string>>(new Set());

  // Initialize SignalR service
  useEffect(() => {
    if (isAuthenticated && !signalRServiceRef.current) {
      const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000';
      const hubUrl = `${baseURL}/hubs/dashboard`;

      signalRServiceRef.current = getSignalRService({
        url: hubUrl,
        accessTokenFactory: () => {
          const token = tokenStorage.getAccessToken();
          return token || '';
        },
        automaticReconnect: true,
        reconnectIntervals: [0, 2000, 10000, 30000],
      });

      // Initialize the connection
      signalRServiceRef.current.initialize().catch((error) => {
        console.error('Failed to initialize SignalR:', error);
        onError?.(error.message);
      });
    }
  }, [isAuthenticated, onError]);

  // Setup event listeners
  useEffect(() => {
    const service = signalRServiceRef.current;
    if (!service) return;

    const handleConnected = () => {
      setConnectionStatus(service.getConnectionStatus());
      onConnected?.();
      
      // Auto-join groups
      groups.forEach(group => {
        service.joinGroup(group).catch(console.error);
        joinedGroupsRef.current.add(group);
      });
    };

    const handleDisconnected = () => {
      setConnectionStatus(service.getConnectionStatus());
      onDisconnected?.();
      joinedGroupsRef.current.clear();
    };

    const handleReconnecting = () => {
      setConnectionStatus(service.getConnectionStatus());
      onReconnecting?.();
    };

    const handleReconnected = () => {
      setConnectionStatus(service.getConnectionStatus());
      onReconnected?.();
      
      // Re-join groups after reconnection
      groups.forEach(group => {
        service.joinGroup(group).catch(console.error);
        joinedGroupsRef.current.add(group);
      });
    };

    const handleConnectionClosed = ({ error }: { error?: Error }) => {
      setConnectionStatus(service.getConnectionStatus());
      if (error) {
        onError?.(error.message);
      }
    };

    service.on('connected', handleConnected);
    service.on('disconnected', handleDisconnected);
    service.on('reconnecting', handleReconnecting);
    service.on('reconnected', handleReconnected);
    service.on('connectionClosed', handleConnectionClosed);

    return () => {
      service.off('connected', handleConnected);
      service.off('disconnected', handleDisconnected);
      service.off('reconnecting', handleReconnecting);
      service.off('reconnected', handleReconnected);
      service.off('connectionClosed', handleConnectionClosed);
    };
  }, [groups, onConnected, onDisconnected, onReconnecting, onReconnected, onError]);

  // Auto-connect when authenticated
  useEffect(() => {
    if (isAuthenticated && autoConnect && signalRServiceRef.current) {
      signalRServiceRef.current.connect().catch((error) => {
        console.error('Failed to connect to SignalR:', error);
        onError?.(error.message);
      });
    }
  }, [isAuthenticated, autoConnect, onError]);

  // Disconnect when user logs out
  useEffect(() => {
    if (!isAuthenticated && signalRServiceRef.current) {
      signalRServiceRef.current.disconnect().catch(console.error);
    }
  }, [isAuthenticated]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (signalRServiceRef.current) {
        signalRServiceRef.current.disconnect().catch(console.error);
      }
    };
  }, []);

  const connect = useCallback(async () => {
    if (signalRServiceRef.current) {
      await signalRServiceRef.current.connect();
    }
  }, []);

  const disconnect = useCallback(async () => {
    if (signalRServiceRef.current) {
      await signalRServiceRef.current.disconnect();
    }
  }, []);

  const joinGroup = useCallback(async (groupName: string) => {
    if (signalRServiceRef.current) {
      await signalRServiceRef.current.joinGroup(groupName);
      joinedGroupsRef.current.add(groupName);
    }
  }, []);

  const leaveGroup = useCallback(async (groupName: string) => {
    if (signalRServiceRef.current) {
      await signalRServiceRef.current.leaveGroup(groupName);
      joinedGroupsRef.current.delete(groupName);
    }
  }, []);

  const on = useCallback((event: string, callback: Function) => {
    if (signalRServiceRef.current) {
      signalRServiceRef.current.on(event, callback);
    }
  }, []);

  const off = useCallback((event: string, callback: Function) => {
    if (signalRServiceRef.current) {
      signalRServiceRef.current.off(event, callback);
    }
  }, []);

  const requestNotificationPermission = useCallback(async () => {
    if (signalRServiceRef.current) {
      return await signalRServiceRef.current.requestNotificationPermission();
    }
    return 'denied' as NotificationPermission;
  }, []);

  return {
    connectionStatus,
    isConnected: connectionStatus.isConnected,
    connect,
    disconnect,
    joinGroup,
    leaveGroup,
    on,
    off,
    requestNotificationPermission,
  };
};
