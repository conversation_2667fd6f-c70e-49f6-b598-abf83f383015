import React from 'react';
import { screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import ProtectedRoute from '../ProtectedRoute';
import { renderWithProviders, mockUsers, createAuthenticatedState, createUnauthenticatedState } from '../../test/utils';

// Mock the LoadingSpinner component
vi.mock('../../components/LoadingSpinner', () => ({
  default: () => <div data-testid="loading-spinner">Loading...</div>,
}));

describe('ProtectedRoute', () => {
  const TestComponent = () => <div data-testid="protected-content">Protected Content</div>;

  describe('when user is not authenticated', () => {
    it('should redirect to login page', () => {
      renderWithProviders(
        <ProtectedRoute>
          <TestComponent />
        </ProtectedRoute>,
        {
          preloadedState: createUnauthenticatedState(),
          initialEntries: ['/dashboard'],
        }
      );

      // Should not render protected content
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });
  });

  describe('when user is authenticated', () => {
    it('should render protected content for authenticated user', () => {
      renderWithProviders(
        <ProtectedRoute>
          <TestComponent />
        </ProtectedRoute>,
        {
          preloadedState: createAuthenticatedState(mockUsers.admin),
        }
      );

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('should show loading spinner when isLoading is true', () => {
      const loadingState = {
        auth: {
          user: null,
          token: null,
          refreshToken: null,
          isAuthenticated: false,
          isLoading: true,
          error: null,
        },
      };

      renderWithProviders(
        <ProtectedRoute>
          <TestComponent />
        </ProtectedRoute>,
        {
          preloadedState: loadingState,
        }
      );

      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });

    it('should not show loading spinner when showLoadingSpinner is false', () => {
      const loadingState = {
        auth: {
          user: null,
          token: null,
          refreshToken: null,
          isAuthenticated: false,
          isLoading: true,
          error: null,
        },
      };

      renderWithProviders(
        <ProtectedRoute showLoadingSpinner={false}>
          <TestComponent />
        </ProtectedRoute>,
        {
          preloadedState: loadingState,
        }
      );

      expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
    });
  });

  describe('role-based access control', () => {
    it('should allow access when user has required role', () => {
      renderWithProviders(
        <ProtectedRoute requiredRole="admin">
          <TestComponent />
        </ProtectedRoute>,
        {
          preloadedState: createAuthenticatedState(mockUsers.admin),
        }
      );

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('should deny access when user does not have required role', () => {
      renderWithProviders(
        <ProtectedRoute requiredRole="admin">
          <TestComponent />
        </ProtectedRoute>,
        {
          preloadedState: createAuthenticatedState(mockUsers.driver),
          initialEntries: ['/admin'],
        }
      );

      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });

    it('should allow access with role hierarchy (admin accessing manager content)', () => {
      renderWithProviders(
        <ProtectedRoute requiredRole="manager">
          <TestComponent />
        </ProtectedRoute>,
        {
          preloadedState: createAuthenticatedState(mockUsers.admin),
        }
      );

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('should deny access with role hierarchy (driver accessing manager content)', () => {
      renderWithProviders(
        <ProtectedRoute requiredRole="manager">
          <TestComponent />
        </ProtectedRoute>,
        {
          preloadedState: createAuthenticatedState(mockUsers.driver),
          initialEntries: ['/manager'],
        }
      );

      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });

    it('should allow access when user role is in allowedRoles', () => {
      renderWithProviders(
        <ProtectedRoute allowedRoles={['admin', 'manager']}>
          <TestComponent />
        </ProtectedRoute>,
        {
          preloadedState: createAuthenticatedState(mockUsers.manager),
        }
      );

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('should deny access when user role is not in allowedRoles', () => {
      renderWithProviders(
        <ProtectedRoute allowedRoles={['admin', 'manager']}>
          <TestComponent />
        </ProtectedRoute>,
        {
          preloadedState: createAuthenticatedState(mockUsers.driver),
        }
      );

      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });
  });

  describe('permission-based access control', () => {
    it('should allow access when user has required permissions', () => {
      renderWithProviders(
        <ProtectedRoute requiredPermissions={['view_dashboard']}>
          <TestComponent />
        </ProtectedRoute>,
        {
          preloadedState: createAuthenticatedState(mockUsers.driver),
        }
      );

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('should deny access when user does not have required permissions', () => {
      renderWithProviders(
        <ProtectedRoute requiredPermissions={['manage_users']}>
          <TestComponent />
        </ProtectedRoute>,
        {
          preloadedState: createAuthenticatedState(mockUsers.driver),
        }
      );

      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });

    it('should allow access when no permissions are required', () => {
      renderWithProviders(
        <ProtectedRoute>
          <TestComponent />
        </ProtectedRoute>,
        {
          preloadedState: createAuthenticatedState(mockUsers.driver),
        }
      );

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });
  });

  describe('custom fallback paths', () => {
    it('should use custom fallback path', () => {
      renderWithProviders(
        <ProtectedRoute requiredRole="admin" fallbackPath="/custom-fallback">
          <TestComponent />
        </ProtectedRoute>,
        {
          preloadedState: createAuthenticatedState(mockUsers.driver),
          initialEntries: ['/admin'],
        }
      );

      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });
  });

  describe('path-based redirects', () => {
    it('should redirect to dashboard when driver tries to access admin routes', () => {
      renderWithProviders(
        <ProtectedRoute>
          <TestComponent />
        </ProtectedRoute>,
        {
          preloadedState: createAuthenticatedState(mockUsers.driver),
          initialEntries: ['/admin/users'],
        }
      );

      // Should not render protected content due to path-based redirect
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });

    it('should redirect to dashboard when driver tries to access manager routes', () => {
      renderWithProviders(
        <ProtectedRoute>
          <TestComponent />
        </ProtectedRoute>,
        {
          preloadedState: createAuthenticatedState(mockUsers.driver),
          initialEntries: ['/manager/fleet'],
        }
      );

      // Should not render protected content due to path-based redirect
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });
  });
});
