import { apiService } from './api';
import type {
  ApiResponse,
  DashboardSummary,
  FleetMetrics,
  AlertMetrics,
  TelemetryMetrics,
  PerformanceMetrics,
  RecentActivity,
} from '../types';

export class DashboardService {
  private readonly baseUrl = '/dashboard';

  /**
   * Get complete dashboard summary
   */
  async getDashboardSummary(): Promise<ApiResponse<DashboardSummary>> {
    try {
      return await apiService.get<DashboardSummary>(`${this.baseUrl}/summary`);
    } catch (error) {
      console.error('Failed to get dashboard summary:', error);
      throw error;
    }
  }

  /**
   * Get fleet metrics
   */
  async getFleetMetrics(): Promise<ApiResponse<FleetMetrics>> {
    try {
      return await apiService.get<FleetMetrics>(
        `${this.baseUrl}/fleet-metrics`
      );
    } catch (error) {
      console.error('Failed to get fleet metrics:', error);
      throw error;
    }
  }

  /**
   * Get alert metrics
   */
  async getAlertMetrics(): Promise<ApiResponse<AlertMetrics>> {
    try {
      return await apiService.get<AlertMetrics>(
        `${this.baseUrl}/alert-metrics`
      );
    } catch (error) {
      console.error('Failed to get alert metrics:', error);
      throw error;
    }
  }

  /**
   * Get telemetry metrics
   */
  async getTelemetryMetrics(): Promise<ApiResponse<TelemetryMetrics>> {
    try {
      return await apiService.get<TelemetryMetrics>(
        `${this.baseUrl}/telemetry-metrics`
      );
    } catch (error) {
      console.error('Failed to get telemetry metrics:', error);
      throw error;
    }
  }

  /**
   * Get performance metrics
   */
  async getPerformanceMetrics(
    period: 'day' | 'week' | 'month' = 'week'
  ): Promise<ApiResponse<PerformanceMetrics>> {
    try {
      return await apiService.get<PerformanceMetrics>(
        `${this.baseUrl}/performance-metrics?period=${period}`
      );
    } catch (error) {
      console.error('Failed to get performance metrics:', error);
      throw error;
    }
  }

  /**
   * Get recent activity
   */
  async getRecentActivity(
    limit: number = 10,
    type?: 'alert' | 'maintenance' | 'driver_assignment' | 'vehicle_status'
  ): Promise<ApiResponse<RecentActivity[]>> {
    try {
      const params = new URLSearchParams({ limit: limit.toString() });
      if (type) params.append('type', type);

      return await apiService.get<RecentActivity[]>(
        `${this.baseUrl}/recent-activity?${params.toString()}`
      );
    } catch (error) {
      console.error('Failed to get recent activity:', error);
      throw error;
    }
  }

  /**
   * Get vehicle utilization data
   */
  async getVehicleUtilization(
    period: 'day' | 'week' | 'month' = 'week'
  ): Promise<ApiResponse<any[]>> {
    try {
      return await apiService.get<any[]>(
        `${this.baseUrl}/vehicle-utilization?period=${period}`
      );
    } catch (error) {
      console.error('Failed to get vehicle utilization:', error);
      throw error;
    }
  }

  /**
   * Get driver performance summary
   */
  async getDriverPerformanceSummary(
    period: 'day' | 'week' | 'month' = 'week',
    limit: number = 10
  ): Promise<ApiResponse<any[]>> {
    try {
      return await apiService.get<any[]>(
        `${this.baseUrl}/driver-performance?period=${period}&limit=${limit}`
      );
    } catch (error) {
      console.error('Failed to get driver performance summary:', error);
      throw error;
    }
  }

  /**
   * Get fuel consumption trends
   */
  async getFuelConsumptionTrends(
    period: 'day' | 'week' | 'month' = 'week'
  ): Promise<ApiResponse<any[]>> {
    try {
      return await apiService.get<any[]>(
        `${this.baseUrl}/fuel-consumption-trends?period=${period}`
      );
    } catch (error) {
      console.error('Failed to get fuel consumption trends:', error);
      throw error;
    }
  }

  /**
   * Get maintenance schedule overview
   */
  async getMaintenanceSchedule(
    upcoming: boolean = true,
    limit: number = 10
  ): Promise<ApiResponse<any[]>> {
    try {
      return await apiService.get<any[]>(
        `${this.baseUrl}/maintenance-schedule?upcoming=${upcoming}&limit=${limit}`
      );
    } catch (error) {
      console.error('Failed to get maintenance schedule:', error);
      throw error;
    }
  }

  /**
   * Get cost analysis data
   */
  async getCostAnalysis(
    period: 'day' | 'week' | 'month' | 'quarter' | 'year' = 'month'
  ): Promise<ApiResponse<any>> {
    try {
      return await apiService.get<any>(
        `${this.baseUrl}/cost-analysis?period=${period}`
      );
    } catch (error) {
      console.error('Failed to get cost analysis:', error);
      throw error;
    }
  }

  /**
   * Get route efficiency metrics
   */
  async getRouteEfficiency(
    period: 'day' | 'week' | 'month' = 'week'
  ): Promise<ApiResponse<any[]>> {
    try {
      return await apiService.get<any[]>(
        `${this.baseUrl}/route-efficiency?period=${period}`
      );
    } catch (error) {
      console.error('Failed to get route efficiency:', error);
      throw error;
    }
  }

  /**
   * Get safety metrics
   */
  async getSafetyMetrics(
    period: 'day' | 'week' | 'month' = 'week'
  ): Promise<ApiResponse<any>> {
    try {
      return await apiService.get<any>(
        `${this.baseUrl}/safety-metrics?period=${period}`
      );
    } catch (error) {
      console.error('Failed to get safety metrics:', error);
      throw error;
    }
  }

  /**
   * Get environmental impact data
   */
  async getEnvironmentalImpact(
    period: 'day' | 'week' | 'month' = 'month'
  ): Promise<ApiResponse<any>> {
    try {
      return await apiService.get<any>(
        `${this.baseUrl}/environmental-impact?period=${period}`
      );
    } catch (error) {
      console.error('Failed to get environmental impact:', error);
      throw error;
    }
  }

  /**
   * Get fleet health overview
   */
  async getFleetHealth(): Promise<ApiResponse<any>> {
    try {
      return await apiService.get<any>(`${this.baseUrl}/fleet-health`);
    } catch (error) {
      console.error('Failed to get fleet health:', error);
      throw error;
    }
  }

  /**
   * Get top performing vehicles
   */
  async getTopPerformingVehicles(
    metric:
      | 'fuel_efficiency'
      | 'uptime'
      | 'distance'
      | 'safety' = 'fuel_efficiency',
    limit: number = 5
  ): Promise<ApiResponse<any[]>> {
    try {
      return await apiService.get<any[]>(
        `${this.baseUrl}/top-vehicles?metric=${metric}&limit=${limit}`
      );
    } catch (error) {
      console.error('Failed to get top performing vehicles:', error);
      throw error;
    }
  }

  /**
   * Get top performing drivers
   */
  async getTopPerformingDrivers(
    metric:
      | 'fuel_efficiency'
      | 'safety'
      | 'punctuality'
      | 'distance' = 'safety',
    limit: number = 5
  ): Promise<ApiResponse<any[]>> {
    try {
      return await apiService.get<any[]>(
        `${this.baseUrl}/top-drivers?metric=${metric}&limit=${limit}`
      );
    } catch (error) {
      console.error('Failed to get top performing drivers:', error);
      throw error;
    }
  }

  /**
   * Get custom dashboard widgets data
   */
  async getCustomWidgetData(
    widgetId: string,
    params?: Record<string, any>
  ): Promise<ApiResponse<any>> {
    try {
      const queryParams = new URLSearchParams();
      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          queryParams.append(key, value.toString());
        });
      }

      return await apiService.get<any>(
        `${this.baseUrl}/widgets/${widgetId}?${queryParams.toString()}`
      );
    } catch (error) {
      console.error(`Failed to get custom widget data for ${widgetId}:`, error);
      throw error;
    }
  }

  /**
   * Export dashboard data
   */
  async exportDashboardData(
    format: 'csv' | 'xlsx' | 'pdf' = 'xlsx',
    sections?: string[]
  ): Promise<Blob> {
    try {
      const params = new URLSearchParams({ format });
      if (sections && sections.length > 0) {
        params.append('sections', sections.join(','));
      }

      const response = await apiService.get<Blob>(
        `${this.baseUrl}/export?${params.toString()}`,
        { responseType: 'blob' }
      );

      return response.data;
    } catch (error) {
      console.error('Failed to export dashboard data:', error);
      throw error;
    }
  }

  /**
   * Get real-time dashboard updates
   */
  async getRealTimeUpdates(): Promise<ApiResponse<any>> {
    try {
      return await apiService.get<any>(`${this.baseUrl}/real-time-updates`);
    } catch (error) {
      console.error('Failed to get real-time updates:', error);
      throw error;
    }
  }

  /**
   * Save dashboard layout/preferences
   */
  async saveDashboardPreferences(preferences: {
    layout: any;
    widgets: any[];
    refreshInterval: number;
    theme?: string;
  }): Promise<ApiResponse<any>> {
    try {
      return await apiService.post<any>(
        `${this.baseUrl}/preferences`,
        preferences
      );
    } catch (error) {
      console.error('Failed to save dashboard preferences:', error);
      throw error;
    }
  }

  /**
   * Get dashboard layout/preferences
   */
  async getDashboardPreferences(): Promise<ApiResponse<any>> {
    try {
      return await apiService.get<any>(`${this.baseUrl}/preferences`);
    } catch (error) {
      console.error('Failed to get dashboard preferences:', error);
      throw error;
    }
  }
}

// Create and export singleton instance
export const dashboardService = new DashboardService();
export default dashboardService;
