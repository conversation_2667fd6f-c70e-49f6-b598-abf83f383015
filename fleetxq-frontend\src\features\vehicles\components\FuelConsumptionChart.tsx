import React, { useState, useEffect } from 'react';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ReferenceLine
} from 'recharts';
import {
  FuelIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';
import type { Vehicle } from '../../../types';

interface FuelConsumptionChartProps {
  vehicle: Vehicle;
  timeRange: '7d' | '30d' | '90d' | '1y';
  className?: string;
}

interface FuelDataPoint {
  date: string;
  consumption: number;
  efficiency: number;
  distance: number;
  cost: number;
  target: number;
}

interface FuelAlert {
  type: 'warning' | 'info' | 'success';
  message: string;
}

const FuelConsumptionChart: React.FC<FuelConsumptionChartProps> = ({
  vehicle,
  timeRange,
  className = ''
}) => {
  const [chartType, setChartType] = useState<'consumption' | 'efficiency' | 'cost'>('consumption');
  const [showTarget, setShowTarget] = useState(true);
  const [fuelData, setFuelData] = useState<FuelDataPoint[]>([]);
  const [alerts, setAlerts] = useState<FuelAlert[]>([]);

  // Mock data generation based on time range
  useEffect(() => {
    const generateMockData = (): FuelDataPoint[] => {
      const dataPoints: FuelDataPoint[] = [];
      const now = new Date();
      let days: number;
      let interval: number;

      switch (timeRange) {
        case '7d':
          days = 7;
          interval = 1;
          break;
        case '30d':
          days = 30;
          interval = 1;
          break;
        case '90d':
          days = 90;
          interval = 3;
          break;
        case '1y':
          days = 365;
          interval = 7;
          break;
        default:
          days = 30;
          interval = 1;
      }

      for (let i = days; i >= 0; i -= interval) {
        const date = new Date(now);
        date.setDate(date.getDate() - i);
        
        // Generate realistic fuel consumption data with some variation
        const baseConsumption = 8.5; // L/100km
        const variation = (Math.random() - 0.5) * 2; // ±1 L/100km variation
        const consumption = Math.max(6, baseConsumption + variation);
        
        const distance = 50 + Math.random() * 100; // 50-150 km per day
        const efficiency = (consumption / distance) * 100;
        const cost = consumption * 1.45; // $1.45 per liter
        const target = 8.0; // Target efficiency

        dataPoints.push({
          date: timeRange === '1y' ? 
            date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }) :
            date.toLocaleDateString('en-US', { month: 'numeric', day: 'numeric' }),
          consumption: Math.round(consumption * 10) / 10,
          efficiency: Math.round(efficiency * 10) / 10,
          distance: Math.round(distance),
          cost: Math.round(cost * 100) / 100,
          target
        });
      }

      return dataPoints;
    };

    const data = generateMockData();
    setFuelData(data);

    // Generate alerts based on data
    const newAlerts: FuelAlert[] = [];
    const avgConsumption = data.reduce((sum, d) => sum + d.consumption, 0) / data.length;
    const target = 8.0;

    if (avgConsumption > target * 1.15) {
      newAlerts.push({
        type: 'warning',
        message: `Fuel consumption is ${((avgConsumption / target - 1) * 100).toFixed(1)}% above target. Consider driver training or vehicle maintenance.`
      });
    } else if (avgConsumption < target * 0.95) {
      newAlerts.push({
        type: 'success',
        message: `Excellent fuel efficiency! ${((1 - avgConsumption / target) * 100).toFixed(1)}% better than target.`
      });
    }

    // Check for recent spikes
    const recentData = data.slice(-7);
    const recentAvg = recentData.reduce((sum, d) => sum + d.consumption, 0) / recentData.length;
    const previousWeekData = data.slice(-14, -7);
    const previousAvg = previousWeekData.reduce((sum, d) => sum + d.consumption, 0) / previousWeekData.length;

    if (recentAvg > previousAvg * 1.1) {
      newAlerts.push({
        type: 'warning',
        message: 'Fuel consumption has increased by more than 10% in the last week. Investigation recommended.'
      });
    }

    setAlerts(newAlerts);
  }, [timeRange]);

  const formatTooltip = (value: any, name: string) => {
    switch (name) {
      case 'consumption':
        return [`${value} L/100km`, 'Fuel Consumption'];
      case 'efficiency':
        return [`${value} L/100km`, 'Efficiency'];
      case 'cost':
        return [`$${value}`, 'Daily Cost'];
      case 'distance':
        return [`${value} km`, 'Distance'];
      case 'target':
        return [`${value} L/100km`, 'Target'];
      default:
        return [value, name];
    }
  };

  const getChartData = () => {
    switch (chartType) {
      case 'efficiency':
        return {
          dataKey: 'efficiency',
          color: '#10B981',
          label: 'Fuel Efficiency (L/100km)',
          yAxisLabel: 'L/100km'
        };
      case 'cost':
        return {
          dataKey: 'cost',
          color: '#F59E0B',
          label: 'Daily Fuel Cost ($)',
          yAxisLabel: 'Cost ($)'
        };
      default:
        return {
          dataKey: 'consumption',
          color: '#3B82F6',
          label: 'Fuel Consumption (L/100km)',
          yAxisLabel: 'L/100km'
        };
    }
  };

  const chartConfig = getChartData();
  const avgValue = fuelData.reduce((sum, d) => sum + d[chartConfig.dataKey as keyof FuelDataPoint], 0) / fuelData.length;
  const minValue = Math.min(...fuelData.map(d => d[chartConfig.dataKey as keyof FuelDataPoint] as number));
  const maxValue = Math.max(...fuelData.map(d => d[chartConfig.dataKey as keyof FuelDataPoint] as number));

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            <FuelIcon className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Fuel Analytics</h3>
            <p className="text-sm text-gray-600">
              {vehicle.vehicleName || `${vehicle.year} ${vehicle.make} ${vehicle.model}`}
            </p>
          </div>
        </div>

        {/* Chart Type Selector */}
        <div className="flex items-center space-x-2">
          <select
            value={chartType}
            onChange={(e) => setChartType(e.target.value as any)}
            className="input-field text-sm py-1"
          >
            <option value="consumption">Consumption</option>
            <option value="efficiency">Efficiency</option>
            <option value="cost">Cost</option>
          </select>
          
          <label className="flex items-center space-x-2 text-sm">
            <input
              type="checkbox"
              checked={showTarget}
              onChange={(e) => setShowTarget(e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-gray-700">Show Target</span>
          </label>
        </div>
      </div>

      {/* Alerts */}
      {alerts.length > 0 && (
        <div className="mb-6 space-y-2">
          {alerts.map((alert, index) => (
            <div
              key={index}
              className={`flex items-start space-x-2 p-3 rounded-lg ${
                alert.type === 'warning' ? 'bg-yellow-50 border border-yellow-200' :
                alert.type === 'success' ? 'bg-green-50 border border-green-200' :
                'bg-blue-50 border border-blue-200'
              }`}
            >
              {alert.type === 'warning' ? (
                <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 mt-0.5" />
              ) : alert.type === 'success' ? (
                <ArrowTrendingDownIcon className="h-5 w-5 text-green-600 mt-0.5" />
              ) : (
                <InformationCircleIcon className="h-5 w-5 text-blue-600 mt-0.5" />
              )}
              <p className={`text-sm ${
                alert.type === 'warning' ? 'text-yellow-800' :
                alert.type === 'success' ? 'text-green-800' :
                'text-blue-800'
              }`}>
                {alert.message}
              </p>
            </div>
          ))}
        </div>
      )}

      {/* Summary Stats */}
      <div className="grid grid-cols-3 gap-4 mb-6">
        <div className="text-center">
          <p className="text-2xl font-bold text-gray-900">
            {chartType === 'cost' ? `$${avgValue.toFixed(2)}` : `${avgValue.toFixed(1)}`}
          </p>
          <p className="text-sm text-gray-600">Average {chartType}</p>
        </div>
        <div className="text-center">
          <p className="text-2xl font-bold text-green-600">
            {chartType === 'cost' ? `$${minValue.toFixed(2)}` : `${minValue.toFixed(1)}`}
          </p>
          <p className="text-sm text-gray-600">Best {chartType}</p>
        </div>
        <div className="text-center">
          <p className="text-2xl font-bold text-red-600">
            {chartType === 'cost' ? `$${maxValue.toFixed(2)}` : `${maxValue.toFixed(1)}`}
          </p>
          <p className="text-sm text-gray-600">Worst {chartType}</p>
        </div>
      </div>

      {/* Chart */}
      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart data={fuelData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey="date" 
              tick={{ fontSize: 12 }}
              interval={timeRange === '1y' ? 'preserveStartEnd' : 0}
            />
            <YAxis 
              tick={{ fontSize: 12 }}
              label={{ value: chartConfig.yAxisLabel, angle: -90, position: 'insideLeft' }}
            />
            <Tooltip 
              formatter={formatTooltip}
              labelStyle={{ color: '#374151' }}
              contentStyle={{ 
                backgroundColor: '#fff', 
                border: '1px solid #d1d5db',
                borderRadius: '0.5rem'
              }}
            />
            <Legend />
            
            <Area
              type="monotone"
              dataKey={chartConfig.dataKey}
              stroke={chartConfig.color}
              fill={chartConfig.color}
              fillOpacity={0.1}
              strokeWidth={2}
              name={chartConfig.label}
            />
            
            {showTarget && chartType !== 'cost' && (
              <ReferenceLine 
                y={8.0} 
                stroke="#EF4444" 
                strokeDasharray="5 5"
                label={{ value: "Target", position: "topRight" }}
              />
            )}
          </AreaChart>
        </ResponsiveContainer>
      </div>

      {/* Additional Insights */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">Fuel Efficiency Trends</h4>
            <div className="space-y-2 text-sm text-gray-600">
              <div className="flex justify-between">
                <span>Current Period Average:</span>
                <span className="font-medium">{avgValue.toFixed(1)} L/100km</span>
              </div>
              <div className="flex justify-between">
                <span>Target Efficiency:</span>
                <span className="font-medium">8.0 L/100km</span>
              </div>
              <div className="flex justify-between">
                <span>Variance from Target:</span>
                <span className={`font-medium ${avgValue > 8.0 ? 'text-red-600' : 'text-green-600'}`}>
                  {avgValue > 8.0 ? '+' : ''}{((avgValue - 8.0) / 8.0 * 100).toFixed(1)}%
                </span>
              </div>
            </div>
          </div>
          
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">Cost Analysis</h4>
            <div className="space-y-2 text-sm text-gray-600">
              <div className="flex justify-between">
                <span>Total Fuel Cost:</span>
                <span className="font-medium">
                  ${fuelData.reduce((sum, d) => sum + d.cost, 0).toFixed(2)}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Average Daily Cost:</span>
                <span className="font-medium">
                  ${(fuelData.reduce((sum, d) => sum + d.cost, 0) / fuelData.length).toFixed(2)}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Cost per 100km:</span>
                <span className="font-medium">
                  ${(avgValue * 1.45).toFixed(2)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FuelConsumptionChart;
