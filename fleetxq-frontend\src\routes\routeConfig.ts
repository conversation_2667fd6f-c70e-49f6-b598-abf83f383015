import type { Permission } from '../hooks/usePermissions';

export interface RouteConfig {
  path: string;
  name: string;
  icon?: string;
  requiredRole?: 'admin' | 'manager' | 'driver';
  allowedRoles?: ('admin' | 'manager' | 'driver')[];
  requiredPermissions?: Permission[];
  showInNavigation?: boolean;
  children?: RouteConfig[];
}

// Main navigation routes configuration
export const navigationRoutes: RouteConfig[] = [
  {
    path: '/dashboard',
    name: 'Dashboard',
    icon: '📊',
    showInNavigation: true,
    requiredPermissions: ['view_dashboard'],
  },
  {
    path: '/vehicles',
    name: 'Vehicles',
    icon: '🚗',
    showInNavigation: true,
    requiredPermissions: ['view_vehicles'],
  },
  {
    path: '/drivers',
    name: 'Drivers',
    icon: '👤',
    showInNavigation: true,
    requiredPermissions: ['view_drivers'],
  },
  {
    path: '/telemetry',
    name: 'Telemetry',
    icon: '📡',
    showInNavigation: true,
    requiredPermissions: ['view_telemetry'],
  },
  {
    path: '/alerts',
    name: 'Alerts',
    icon: '🚨',
    showInNavigation: true,
    requiredPermissions: ['view_alerts'],
  },
  {
    path: '/reports',
    name: 'Reports',
    icon: '📈',
    showInNavigation: true,
    requiredPermissions: ['view_reports'],
  },
];

// Admin-specific routes
export const adminRoutes: RouteConfig[] = [
  {
    path: '/admin',
    name: 'Admin Panel',
    icon: '⚙️',
    requiredRole: 'admin',
    showInNavigation: true,
    children: [
      {
        path: '/admin',
        name: 'Admin Dashboard',
        icon: '📊',
        requiredRole: 'admin',
        showInNavigation: false,
      },
      {
        path: '/admin/users',
        name: 'User Management',
        icon: '👥',
        requiredRole: 'admin',
        showInNavigation: true,
        requiredPermissions: ['manage_users'],
      },
      {
        path: '/admin/settings',
        name: 'System Settings',
        icon: '⚙️',
        requiredRole: 'admin',
        showInNavigation: true,
        requiredPermissions: ['manage_system_settings'],
      },
    ],
  },
];

// Manager-specific routes
export const managerRoutes: RouteConfig[] = [
  {
    path: '/manager',
    name: 'Manager Panel',
    icon: '👔',
    allowedRoles: ['admin', 'manager'],
    showInNavigation: true,
    children: [
      {
        path: '/manager',
        name: 'Manager Dashboard',
        icon: '📊',
        allowedRoles: ['admin', 'manager'],
        showInNavigation: false,
      },
      {
        path: '/manager/fleet',
        name: 'Fleet Overview',
        icon: '🚛',
        allowedRoles: ['admin', 'manager'],
        showInNavigation: true,
        requiredPermissions: ['view_vehicles', 'manage_vehicles'],
      },
    ],
  },
];

// Driver-specific routes
export const driverRoutes: RouteConfig[] = [
  {
    path: '/driver',
    name: 'Driver Panel',
    icon: '🚗',
    allowedRoles: ['admin', 'manager', 'driver'],
    showInNavigation: true,
    children: [
      {
        path: '/driver',
        name: 'Driver Dashboard',
        icon: '📊',
        allowedRoles: ['admin', 'manager', 'driver'],
        showInNavigation: false,
      },
      {
        path: '/driver/my-vehicle',
        name: 'My Vehicle',
        icon: '🚙',
        allowedRoles: ['admin', 'manager', 'driver'],
        showInNavigation: true,
        requiredPermissions: ['view_vehicles'],
      },
      {
        path: '/driver/trips',
        name: 'Trip History',
        icon: '🗺️',
        allowedRoles: ['admin', 'manager', 'driver'],
        showInNavigation: true,
        requiredPermissions: ['view_telemetry'],
      },
    ],
  },
];

// All routes combined
export const allRoutes: RouteConfig[] = [
  ...navigationRoutes,
  ...adminRoutes,
  ...managerRoutes,
  ...driverRoutes,
];

// Utility functions for route management
export const getRoutesForRole = (role: 'admin' | 'manager' | 'driver'): RouteConfig[] => {
  return allRoutes.filter(route => {
    if (route.requiredRole) {
      return route.requiredRole === role;
    }
    if (route.allowedRoles) {
      return route.allowedRoles.includes(role);
    }
    return true; // No role restriction
  });
};

export const getNavigationRoutes = (role: 'admin' | 'manager' | 'driver'): RouteConfig[] => {
  return getRoutesForRole(role).filter(route => route.showInNavigation);
};

export const findRouteByPath = (path: string): RouteConfig | undefined => {
  const findInRoutes = (routes: RouteConfig[]): RouteConfig | undefined => {
    for (const route of routes) {
      if (route.path === path) {
        return route;
      }
      if (route.children) {
        const found = findInRoutes(route.children);
        if (found) return found;
      }
    }
    return undefined;
  };
  
  return findInRoutes(allRoutes);
};

export const getDefaultRouteForRole = (role: 'admin' | 'manager' | 'driver'): string => {
  switch (role) {
    case 'admin':
      return '/admin';
    case 'manager':
      return '/manager';
    case 'driver':
      return '/driver';
    default:
      return '/dashboard';
  }
};

// Route breadcrumb generation
export const generateBreadcrumbs = (currentPath: string): { name: string; path: string }[] => {
  const breadcrumbs: { name: string; path: string }[] = [];
  const pathSegments = currentPath.split('/').filter(Boolean);
  
  let currentRoute = '';
  for (const segment of pathSegments) {
    currentRoute += `/${segment}`;
    const route = findRouteByPath(currentRoute);
    if (route) {
      breadcrumbs.push({
        name: route.name,
        path: route.path,
      });
    }
  }
  
  return breadcrumbs;
};
