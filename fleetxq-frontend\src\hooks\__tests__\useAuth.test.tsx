import { renderHook, act } from '@testing-library/react';
import { Provider } from 'react-redux';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { useAuth } from '../useAuth';
import { createTestStore, mockUsers, mockTokens, createAuthenticatedState, createUnauthenticatedState } from '../../test/utils';
import * as authSlice from '../../features/auth/authSlice';

// Mock the auth slice actions
vi.mock('../../features/auth/authSlice', async () => {
  const actual = await vi.importActual('../../features/auth/authSlice');
  return {
    ...actual,
    loginUser: vi.fn(),
    logoutUser: vi.fn(),
    initializeAuth: vi.fn(),
    clearError: vi.fn(),
  };
});

describe('useAuth', () => {
  let store: ReturnType<typeof createTestStore>;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  const renderUseAuth = (preloadedState?: any) => {
    store = createTestStore(preloadedState);
    return renderHook(() => useAuth(), {
      wrapper: ({ children }) => <Provider store={store}>{children}</Provider>,
    });
  };

  describe('when user is authenticated', () => {
    it('should return authenticated state', () => {
      const { result } = renderUseAuth(createAuthenticatedState(mockUsers.admin));

      expect(result.current.isAuthenticated).toBe(true);
      expect(result.current.user).toEqual(mockUsers.admin);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeNull();
    });

    it('should provide role checking utilities', () => {
      const { result } = renderUseAuth(createAuthenticatedState(mockUsers.admin));

      expect(result.current.hasRole('admin')).toBe(true);
      expect(result.current.hasRole('manager')).toBe(false);
      expect(result.current.hasRole('driver')).toBe(false);

      expect(result.current.hasAnyRole(['admin', 'manager'])).toBe(true);
      expect(result.current.hasAnyRole(['manager', 'driver'])).toBe(false);
    });
  });

  describe('when user is not authenticated', () => {
    it('should return unauthenticated state', () => {
      const { result } = renderUseAuth(createUnauthenticatedState());

      expect(result.current.isAuthenticated).toBe(false);
      expect(result.current.user).toBeNull();
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeNull();
    });

    it('should return false for role checks', () => {
      const { result } = renderUseAuth(createUnauthenticatedState());

      expect(result.current.hasRole('admin')).toBe(false);
      expect(result.current.hasAnyRole(['admin', 'manager'])).toBe(false);
    });
  });

  describe('login function', () => {
    it('should call loginUser action with credentials', async () => {
      const mockLoginUser = vi.mocked(authSlice.loginUser);
      mockLoginUser.mockReturnValue({
        unwrap: vi.fn().mockResolvedValue({ user: mockUsers.admin, token: mockTokens.accessToken }),
      } as any);

      const { result } = renderUseAuth(createUnauthenticatedState());

      const credentials = { email: '<EMAIL>', password: 'password' };

      await act(async () => {
        const loginResult = await result.current.login(credentials);
        expect(loginResult.success).toBe(true);
      });

      expect(mockLoginUser).toHaveBeenCalledWith(credentials);
    });

    it('should handle login failure', async () => {
      const mockLoginUser = vi.mocked(authSlice.loginUser);
      mockLoginUser.mockReturnValue({
        unwrap: vi.fn().mockRejectedValue('Login failed'),
      } as any);

      const { result } = renderUseAuth(createUnauthenticatedState());

      const credentials = { email: '<EMAIL>', password: 'password' };

      await act(async () => {
        const loginResult = await result.current.login(credentials);
        expect(loginResult.success).toBe(false);
        expect(loginResult.error).toBe('Login failed');
      });
    });
  });

  describe('logout function', () => {
    it('should call logoutUser action', async () => {
      const mockLogoutUser = vi.mocked(authSlice.logoutUser);
      mockLogoutUser.mockReturnValue({
        unwrap: vi.fn().mockResolvedValue(null),
      } as any);

      const { result } = renderUseAuth(createAuthenticatedState(mockUsers.admin));

      await act(async () => {
        const logoutResult = await result.current.logout();
        expect(logoutResult.success).toBe(true);
      });

      expect(mockLogoutUser).toHaveBeenCalled();
    });

    it('should handle logout failure', async () => {
      const mockLogoutUser = vi.mocked(authSlice.logoutUser);
      mockLogoutUser.mockReturnValue({
        unwrap: vi.fn().mockRejectedValue('Logout failed'),
      } as any);

      const { result } = renderUseAuth(createAuthenticatedState(mockUsers.admin));

      await act(async () => {
        const logoutResult = await result.current.logout();
        expect(logoutResult.success).toBe(false);
        expect(logoutResult.error).toBe('Logout failed');
      });
    });
  });

  describe('initialize function', () => {
    it('should call initializeAuth action', async () => {
      const mockInitializeAuth = vi.mocked(authSlice.initializeAuth);
      mockInitializeAuth.mockReturnValue({
        unwrap: vi.fn().mockResolvedValue({ user: mockUsers.admin, token: mockTokens.accessToken }),
      } as any);

      const { result } = renderUseAuth(createUnauthenticatedState());

      await act(async () => {
        const initResult = await result.current.initialize();
        expect(initResult.success).toBe(true);
      });

      expect(mockInitializeAuth).toHaveBeenCalled();
    });
  });

  describe('clearAuthError function', () => {
    it('should call clearError action', () => {
      const mockClearError = vi.mocked(authSlice.clearError);
      const { result } = renderUseAuth(createUnauthenticatedState());

      act(() => {
        result.current.clearAuthError();
      });

      expect(mockClearError).toHaveBeenCalled();
    });
  });

  describe('role checking with different user roles', () => {
    it('should work correctly for manager role', () => {
      const { result } = renderUseAuth(createAuthenticatedState(mockUsers.manager));

      expect(result.current.hasRole('manager')).toBe(true);
      expect(result.current.hasRole('admin')).toBe(false);
      expect(result.current.hasAnyRole(['admin', 'manager'])).toBe(true);
    });

    it('should work correctly for driver role', () => {
      const { result } = renderUseAuth(createAuthenticatedState(mockUsers.driver));

      expect(result.current.hasRole('driver')).toBe(true);
      expect(result.current.hasRole('admin')).toBe(false);
      expect(result.current.hasAnyRole(['driver'])).toBe(true);
      expect(result.current.hasAnyRole(['admin', 'manager'])).toBe(false);
    });
  });
});
