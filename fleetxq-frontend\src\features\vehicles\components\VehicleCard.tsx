import React from 'react';
import {
  MapPinIcon,
  FuelIcon,
  CogIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  UserIcon,
  TruckIcon
} from '@heroicons/react/24/outline';
import {
  CheckCircleIcon,
  XCircleIcon,
  PlayIcon,
  PauseIcon
} from '@heroicons/react/24/solid';
import { usePermissions } from '../../../hooks/usePermissions';
import type { Vehicle, VehicleRealTimeStatus, VehicleMaintenanceAlert } from '../../../types';

interface VehicleCardProps {
  vehicle: Vehicle;
  realTimeStatus?: VehicleRealTimeStatus;
  maintenanceAlerts?: VehicleMaintenanceAlert[];
  onSelect?: (vehicleId: string) => void;
  onViewDetails?: (vehicleId: string) => void;
  onAssignDriver?: (vehicleId: string) => void;
  isSelected?: boolean;
  showSelection?: boolean;
  className?: string;
}

const VehicleCard: React.FC<VehicleCardProps> = ({
  vehicle,
  realTimeStatus = 'offline',
  maintenanceAlerts = [],
  onSelect,
  onViewDetails,
  onAssignDriver,
  isSelected = false,
  showSelection = false,
  className = '',
}) => {
  const { getUIPermissions } = usePermissions();
  const permissions = getUIPermissions();

  const getRealTimeStatusColor = (status: VehicleRealTimeStatus) => {
    switch (status) {
      case 'online':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'moving':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'idle':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'offline':
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusColor = (status: Vehicle['status']) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'maintenance':
        return 'bg-yellow-100 text-yellow-800';
      case 'offline':
        return 'bg-red-100 text-red-800';
      case 'retired':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getRealTimeStatusIcon = (status: VehicleRealTimeStatus) => {
    switch (status) {
      case 'online':
        return <CheckCircleIcon className="h-4 w-4" />;
      case 'moving':
        return <PlayIcon className="h-4 w-4" />;
      case 'idle':
        return <PauseIcon className="h-4 w-4" />;
      case 'offline':
      default:
        return <XCircleIcon className="h-4 w-4" />;
    }
  };

  const getFuelLevelColor = (fuelLevel?: number) => {
    if (!fuelLevel) return 'bg-gray-200';
    if (fuelLevel < 20) return 'bg-red-500';
    if (fuelLevel < 50) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const criticalAlerts = maintenanceAlerts.filter(alert => alert.priority === 'critical');
  const urgentAlerts = maintenanceAlerts.filter(alert => alert.priority === 'high');

  const formatLastUpdate = (timestamp?: string) => {
    if (!timestamp) return 'Never';
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;
    return `${Math.floor(diffMins / 1440)}d ago`;
  };

  return (
    <div 
      className={`card relative transition-all duration-200 hover:shadow-md ${
        isSelected ? 'ring-2 ring-blue-500 border-blue-200' : ''
      } ${className}`}
    >
      {/* Selection checkbox */}
      {showSelection && (
        <div className="absolute top-3 left-3">
          <input
            type="checkbox"
            checked={isSelected}
            onChange={() => onSelect?.(vehicle.id)}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
        </div>
      )}

      {/* Alert indicators */}
      {(criticalAlerts.length > 0 || urgentAlerts.length > 0) && (
        <div className="absolute top-3 right-3 flex space-x-1">
          {criticalAlerts.length > 0 && (
            <div className="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-medium flex items-center">
              <ExclamationTriangleIcon className="h-3 w-3 mr-1" />
              {criticalAlerts.length}
            </div>
          )}
          {urgentAlerts.length > 0 && (
            <div className="bg-orange-100 text-orange-800 px-2 py-1 rounded-full text-xs font-medium flex items-center">
              <ExclamationTriangleIcon className="h-3 w-3 mr-1" />
              {urgentAlerts.length}
            </div>
          )}
        </div>
      )}

      {/* Vehicle header */}
      <div className={`${showSelection ? 'ml-6' : ''} mb-4`}>
        <div className="flex items-start justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-1">
              {vehicle.vehicleName || `${vehicle.year} ${vehicle.make} ${vehicle.model}`}
            </h3>
            <p className="text-sm text-gray-600 mb-2">{vehicle.licensePlate}</p>
            
            {/* Status badges */}
            <div className="flex items-center space-x-2 mb-3">
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(vehicle.status)}`}>
                {vehicle.status}
              </span>
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getRealTimeStatusColor(realTimeStatus)}`}>
                {getRealTimeStatusIcon(realTimeStatus)}
                <span className="ml-1 capitalize">{realTimeStatus}</span>
              </span>
            </div>
          </div>
          
          <div className="flex items-center text-gray-400">
            <TruckIcon className="h-8 w-8" />
          </div>
        </div>
      </div>

      {/* Vehicle metrics */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        {/* Location */}
        <div className="flex items-center space-x-2">
          <MapPinIcon className="h-4 w-4 text-gray-400" />
          <div>
            <p className="text-xs text-gray-500">Location</p>
            <p className="text-sm font-medium text-gray-900">
              {vehicle.currentLocation?.address || 'Unknown'}
            </p>
          </div>
        </div>

        {/* Driver */}
        <div className="flex items-center space-x-2">
          <UserIcon className="h-4 w-4 text-gray-400" />
          <div>
            <p className="text-xs text-gray-500">Driver</p>
            <p className="text-sm font-medium text-gray-900">
              {vehicle.driverId ? 'Assigned' : 'Unassigned'}
            </p>
          </div>
        </div>

        {/* Fuel Level */}
        <div className="flex items-center space-x-2">
          <FuelIcon className="h-4 w-4 text-gray-400" />
          <div className="flex-1">
            <p className="text-xs text-gray-500">Fuel Level</p>
            <div className="flex items-center space-x-2">
              <div className="flex-1 bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full ${getFuelLevelColor(vehicle.currentFuelLevel)}`}
                  style={{ width: `${vehicle.currentFuelLevel || 0}%` }}
                />
              </div>
              <span className="text-sm font-medium text-gray-900">
                {vehicle.currentFuelLevel ? `${Math.round(vehicle.currentFuelLevel)}%` : 'N/A'}
              </span>
            </div>
          </div>
        </div>

        {/* Engine Status */}
        <div className="flex items-center space-x-2">
          <CogIcon className="h-4 w-4 text-gray-400" />
          <div>
            <p className="text-xs text-gray-500">Engine</p>
            <p className="text-sm font-medium text-gray-900 capitalize">
              {vehicle.engineStatus || 'Unknown'}
            </p>
          </div>
        </div>
      </div>

      {/* Additional info */}
      <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
        <div className="flex items-center space-x-1">
          <ClockIcon className="h-3 w-3" />
          <span>Updated {formatLastUpdate(vehicle.lastTelemetryUpdate)}</span>
        </div>
        <div>
          Mileage: {vehicle.currentMileage.toLocaleString()} mi
        </div>
      </div>

      {/* Action buttons */}
      <div className="flex space-x-2">
        <button
          onClick={() => onViewDetails?.(vehicle.id)}
          className="flex-1 btn-primary text-sm py-2"
        >
          View Details
        </button>
        {!vehicle.driverId && permissions.canAssignDrivers && (
          <button
            onClick={() => onAssignDriver?.(vehicle.id)}
            className="flex-1 btn-secondary text-sm py-2"
          >
            Assign Driver
          </button>
        )}
      </div>
    </div>
  );
};

export default VehicleCard;
