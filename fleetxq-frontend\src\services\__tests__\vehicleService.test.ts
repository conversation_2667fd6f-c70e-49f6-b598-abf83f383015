import { vehicleService } from '../vehicleService';
import { apiService } from '../api';
import type { Vehicle, VehicleFormData, VehicleSearchFilters, VehicleBulkOperation } from '../../types';

// Mock the API service
jest.mock('../api', () => ({
  apiService: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    patch: jest.fn(),
    delete: jest.fn(),
  },
}));

const mockApiService = apiService as jest.Mocked<typeof apiService>;

const mockVehicle: Vehicle = {
  id: 'vehicle-1',
  vehicleName: 'Fleet Vehicle 001',
  make: 'Ford',
  model: 'Transit',
  year: 2022,
  licensePlate: 'ABC-123',
  vin: '1HGBH41JXMN109186',
  vehicleType: 'Van',
  brand: 'Ford',
  color: 'White',
  fuelType: 'Diesel',
  fuelTankCapacity: 80,
  status: 'active',
  driverId: 'driver-1',
  currentMileage: 15000,
  lastMaintenanceDate: '2024-01-15T10:00:00Z',
  nextMaintenanceDate: '2024-04-15T10:00:00Z',
  currentLocation: {
    latitude: 40.7128,
    longitude: -74.0060,
    address: '123 Main St, New York, NY',
    timestamp: '2024-01-20T14:30:00Z'
  },
  currentSpeed: 45,
  currentFuelLevel: 75,
  engineStatus: 'on',
  lastTelemetryUpdate: '2024-01-20T14:30:00Z',
  isMoving: true,
  needsMaintenance: false,
  isAvailable: true,
  requiresImmediateAttention: false,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-20T14:30:00Z'
};

const mockVehicleFormData: VehicleFormData = {
  vehicleName: 'Test Vehicle',
  make: 'Toyota',
  model: 'Camry',
  year: 2023,
  licensePlate: 'XYZ-789',
  vin: '1HGBH41JXMN109187',
  vehicleType: 'Car',
  brand: 'Toyota',
  color: 'Blue',
  fuelType: 'Gasoline',
  fuelTankCapacity: 60,
  status: 'active',
  driverId: 'driver-2'
};

describe('VehicleService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getVehicles', () => {
    it('should fetch vehicles with default parameters', async () => {
      const mockResponse = {
        data: {
          data: [mockVehicle],
          pagination: { page: 1, pageSize: 10, total: 1, totalPages: 1 }
        }
      };

      mockApiService.get.mockResolvedValue(mockResponse);

      const result = await vehicleService.getVehicles();

      expect(mockApiService.get).toHaveBeenCalledWith('/vehicles?page=1&pageSize=10');
      expect(result).toEqual(mockResponse.data);
    });

    it('should fetch vehicles with filters', async () => {
      const filters: VehicleSearchFilters = {
        status: 'active',
        search: 'Ford',
        vehicleType: 'Van',
        fuelType: 'Diesel',
        needsMaintenance: true,
        sortBy: 'vehicleName',
        sortOrder: 'asc'
      };

      const mockResponse = {
        data: {
          data: [mockVehicle],
          pagination: { page: 1, pageSize: 10, total: 1, totalPages: 1 }
        }
      };

      mockApiService.get.mockResolvedValue(mockResponse);

      await vehicleService.getVehicles(filters, 2, 20);

      expect(mockApiService.get).toHaveBeenCalledWith(
        expect.stringContaining('/vehicles?page=2&pageSize=20&status=active&search=Ford&vehicleType=Van&fuelType=Diesel&needsMaintenance=true&sortBy=vehicleName&sortOrder=asc')
      );
    });

    it('should handle date range filters', async () => {
      const filters: VehicleSearchFilters = {
        dateRange: {
          startDate: '2024-01-01',
          endDate: '2024-01-31'
        }
      };

      const mockResponse = {
        data: {
          data: [mockVehicle],
          pagination: { page: 1, pageSize: 10, total: 1, totalPages: 1 }
        }
      };

      mockApiService.get.mockResolvedValue(mockResponse);

      await vehicleService.getVehicles(filters);

      expect(mockApiService.get).toHaveBeenCalledWith(
        expect.stringContaining('startDate=2024-01-01&endDate=2024-01-31')
      );
    });

    it('should handle API errors', async () => {
      const error = new Error('Network error');
      mockApiService.get.mockRejectedValue(error);

      await expect(vehicleService.getVehicles()).rejects.toThrow('Network error');
    });
  });

  describe('getVehicleById', () => {
    it('should fetch vehicle by ID', async () => {
      const mockResponse = { data: mockVehicle };
      mockApiService.get.mockResolvedValue(mockResponse);

      const result = await vehicleService.getVehicleById('vehicle-1');

      expect(mockApiService.get).toHaveBeenCalledWith('/vehicles/vehicle-1');
      expect(result).toEqual(mockResponse);
    });

    it('should handle API errors', async () => {
      const error = new Error('Vehicle not found');
      mockApiService.get.mockRejectedValue(error);

      await expect(vehicleService.getVehicleById('invalid-id')).rejects.toThrow('Vehicle not found');
    });
  });

  describe('createVehicle', () => {
    it('should create a new vehicle', async () => {
      const mockResponse = { data: mockVehicle };
      mockApiService.post.mockResolvedValue(mockResponse);

      const result = await vehicleService.createVehicle(mockVehicleFormData);

      expect(mockApiService.post).toHaveBeenCalledWith('/vehicles', mockVehicleFormData);
      expect(result).toEqual(mockResponse);
    });

    it('should handle validation errors', async () => {
      const error = new Error('Validation failed');
      mockApiService.post.mockRejectedValue(error);

      await expect(vehicleService.createVehicle(mockVehicleFormData)).rejects.toThrow('Validation failed');
    });
  });

  describe('updateVehicle', () => {
    it('should update an existing vehicle', async () => {
      const updateData = { vehicleName: 'Updated Vehicle' };
      const mockResponse = { data: { ...mockVehicle, ...updateData } };
      mockApiService.put.mockResolvedValue(mockResponse);

      const result = await vehicleService.updateVehicle('vehicle-1', updateData);

      expect(mockApiService.put).toHaveBeenCalledWith('/vehicles/vehicle-1', updateData);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('deleteVehicle', () => {
    it('should delete a vehicle', async () => {
      const mockResponse = { data: undefined };
      mockApiService.delete.mockResolvedValue(mockResponse);

      const result = await vehicleService.deleteVehicle('vehicle-1');

      expect(mockApiService.delete).toHaveBeenCalledWith('/vehicles/vehicle-1');
      expect(result).toEqual(mockResponse);
    });
  });

  describe('updateVehicleStatus', () => {
    it('should update vehicle status', async () => {
      const mockResponse = { data: { ...mockVehicle, status: 'maintenance' } };
      mockApiService.patch.mockResolvedValue(mockResponse);

      const result = await vehicleService.updateVehicleStatus('vehicle-1', 'maintenance', 'Scheduled maintenance');

      expect(mockApiService.patch).toHaveBeenCalledWith('/vehicles/vehicle-1/status', {
        status: 'maintenance',
        reason: 'Scheduled maintenance'
      });
      expect(result).toEqual(mockResponse);
    });
  });

  describe('assignDriver', () => {
    it('should assign driver to vehicle', async () => {
      const mockResponse = { data: { ...mockVehicle, driverId: 'new-driver' } };
      mockApiService.post.mockResolvedValue(mockResponse);

      const result = await vehicleService.assignDriver('vehicle-1', 'new-driver', 'Route assignment');

      expect(mockApiService.post).toHaveBeenCalledWith('/vehicles/vehicle-1/assign-driver', {
        driverId: 'new-driver',
        reason: 'Route assignment'
      });
      expect(result).toEqual(mockResponse);
    });
  });

  describe('unassignDriver', () => {
    it('should unassign driver from vehicle', async () => {
      const mockResponse = { data: { ...mockVehicle, driverId: undefined } };
      mockApiService.post.mockResolvedValue(mockResponse);

      const result = await vehicleService.unassignDriver('vehicle-1', 'End of shift');

      expect(mockApiService.post).toHaveBeenCalledWith('/vehicles/vehicle-1/unassign-driver', {
        reason: 'End of shift'
      });
      expect(result).toEqual(mockResponse);
    });
  });

  describe('getVehicleAnalytics', () => {
    it('should fetch vehicle analytics', async () => {
      const mockAnalytics = {
        vehicleId: 'vehicle-1',
        utilizationData: { labels: [], values: [] },
        fuelConsumptionTrend: { labels: [], values: [] },
        performanceMetrics: {
          totalDistance: 1000,
          averageSpeed: 50,
          fuelEfficiency: 8.5,
          utilizationRate: 85,
          maintenanceCosts: 500
        },
        maintenanceHistory: [],
        assignmentHistory: []
      };

      const mockResponse = { data: mockAnalytics };
      mockApiService.get.mockResolvedValue(mockResponse);

      const result = await vehicleService.getVehicleAnalytics('vehicle-1', '30d');

      expect(mockApiService.get).toHaveBeenCalledWith('/vehicles/vehicle-1/analytics?timeRange=30d');
      expect(result).toEqual(mockResponse);
    });

    it('should fetch analytics without time range', async () => {
      const mockResponse = { data: {} };
      mockApiService.get.mockResolvedValue(mockResponse);

      await vehicleService.getVehicleAnalytics('vehicle-1');

      expect(mockApiService.get).toHaveBeenCalledWith('/vehicles/vehicle-1/analytics');
    });
  });

  describe('getMaintenanceAlerts', () => {
    it('should fetch maintenance alerts for specific vehicle', async () => {
      const mockAlerts = [
        {
          id: 'alert-1',
          vehicleId: 'vehicle-1',
          type: 'scheduled',
          title: 'Oil Change',
          description: 'Regular maintenance',
          dueDate: '2024-02-01T00:00:00Z',
          priority: 'medium',
          createdAt: '2024-01-15T00:00:00Z'
        }
      ];

      const mockResponse = { data: mockAlerts };
      mockApiService.get.mockResolvedValue(mockResponse);

      const result = await vehicleService.getMaintenanceAlerts('vehicle-1');

      expect(mockApiService.get).toHaveBeenCalledWith('/vehicles/vehicle-1/maintenance-alerts');
      expect(result).toEqual(mockResponse);
    });

    it('should fetch maintenance alerts for all vehicles', async () => {
      const mockResponse = { data: [] };
      mockApiService.get.mockResolvedValue(mockResponse);

      await vehicleService.getMaintenanceAlerts();

      expect(mockApiService.get).toHaveBeenCalledWith('/vehicles/maintenance-alerts');
    });
  });

  describe('performBulkOperation', () => {
    it('should perform bulk operation', async () => {
      const bulkOperation: VehicleBulkOperation = {
        vehicleIds: ['vehicle-1', 'vehicle-2'],
        operation: 'updateStatus',
        data: { status: 'maintenance' }
      };

      const mockResponse = { data: [mockVehicle] };
      mockApiService.post.mockResolvedValue(mockResponse);

      const result = await vehicleService.performBulkOperation(bulkOperation);

      expect(mockApiService.post).toHaveBeenCalledWith('/vehicles/bulk-operation', bulkOperation);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('getRealTimeUpdates', () => {
    it('should fetch real-time updates', async () => {
      const mockUpdates = [
        {
          vehicleId: 'vehicle-1',
          updateType: 'location',
          data: { location: { latitude: 40.7128, longitude: -74.0060, timestamp: '2024-01-20T14:30:00Z' } },
          timestamp: '2024-01-20T14:30:00Z'
        }
      ];

      const mockResponse = { data: mockUpdates };
      mockApiService.get.mockResolvedValue(mockResponse);

      const result = await vehicleService.getRealTimeUpdates(['vehicle-1'], 50);

      expect(mockApiService.get).toHaveBeenCalledWith(
        expect.stringContaining('/vehicles/real-time-updates?limit=50&vehicleIds=vehicle-1')
      );
      expect(result).toEqual(mockResponse);
    });

    it('should fetch updates without vehicle filter', async () => {
      const mockResponse = { data: [] };
      mockApiService.get.mockResolvedValue(mockResponse);

      await vehicleService.getRealTimeUpdates(undefined, 100);

      expect(mockApiService.get).toHaveBeenCalledWith('/vehicles/real-time-updates?limit=100');
    });
  });

  describe('getTelemetryData', () => {
    it('should fetch telemetry data with date range', async () => {
      const mockTelemetry = [
        {
          vehicleId: 'vehicle-1',
          timestamp: '2024-01-20T14:30:00Z',
          speed: 45,
          fuelLevel: 75,
          engineStatus: 'on',
          location: { latitude: 40.7128, longitude: -74.0060 }
        }
      ];

      const mockResponse = { data: mockTelemetry };
      mockApiService.get.mockResolvedValue(mockResponse);

      const result = await vehicleService.getTelemetryData(
        'vehicle-1',
        '2024-01-01',
        '2024-01-31',
        500
      );

      expect(mockApiService.get).toHaveBeenCalledWith(
        expect.stringContaining('/vehicles/vehicle-1/telemetry?limit=500&startDate=2024-01-01&endDate=2024-01-31')
      );
      expect(result).toEqual(mockResponse);
    });
  });

  describe('exportVehicles', () => {
    it('should export vehicles with filters', async () => {
      const mockBlob = new Blob(['csv data'], { type: 'text/csv' });
      const mockResponse = { data: mockBlob };
      mockApiService.get.mockResolvedValue(mockResponse);

      const filters: VehicleSearchFilters = {
        status: 'active',
        vehicleType: 'Van'
      };

      const result = await vehicleService.exportVehicles('csv', filters);

      expect(mockApiService.get).toHaveBeenCalledWith(
        expect.stringContaining('/vehicles/export?format=csv&status=active&vehicleType=Van'),
        { responseType: 'blob' }
      );
      expect(result).toEqual(mockBlob);
    });
  });
});
