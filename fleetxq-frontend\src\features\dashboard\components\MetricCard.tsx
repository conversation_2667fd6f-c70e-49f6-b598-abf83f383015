import React, { ReactNode } from 'react';
import { ArrowUpIcon, ArrowDownIcon } from '@heroicons/react/24/outline';

export interface MetricCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: ReactNode;
  trend?: {
    value: number;
    label: string;
    direction: 'up' | 'down' | 'neutral';
  };
  status?: 'success' | 'warning' | 'danger' | 'info' | 'neutral';
  isLoading?: boolean;
  onClick?: () => void;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  trend,
  status = 'neutral',
  isLoading = false,
  onClick,
  className = '',
  size = 'md',
}) => {
  const getStatusClasses = () => {
    switch (status) {
      case 'success':
        return 'border-green-200 bg-green-50';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50';
      case 'danger':
        return 'border-red-200 bg-red-50';
      case 'info':
        return 'border-blue-200 bg-blue-50';
      default:
        return 'border-gray-200 bg-white';
    }
  };

  const getValueClasses = () => {
    switch (status) {
      case 'success':
        return 'text-green-600';
      case 'warning':
        return 'text-yellow-600';
      case 'danger':
        return 'text-red-600';
      case 'info':
        return 'text-blue-600';
      default:
        return 'text-gray-900';
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'p-4';
      case 'lg':
        return 'p-8';
      default:
        return 'p-6';
    }
  };

  const getValueSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'text-xl';
      case 'lg':
        return 'text-4xl';
      default:
        return 'text-2xl';
    }
  };

  const getTrendClasses = () => {
    switch (trend?.direction) {
      case 'up':
        return 'text-green-600';
      case 'down':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const cardClasses = `
    ${getStatusClasses()}
    ${getSizeClasses()}
    rounded-lg border shadow-sm transition-all duration-200
    ${onClick ? 'cursor-pointer hover:shadow-md hover:border-gray-300' : ''}
    ${className}
  `.trim();

  if (isLoading) {
    return (
      <div className={cardClasses}>
        <div className="animate-pulse">
          <div className="flex items-center justify-between">
            <div className="space-y-2 flex-1">
              <div className="h-4 bg-gray-300 rounded w-3/4"></div>
              <div className="h-8 bg-gray-300 rounded w-1/2"></div>
              {subtitle && <div className="h-3 bg-gray-300 rounded w-2/3"></div>}
            </div>
            {icon && (
              <div className="h-8 w-8 bg-gray-300 rounded"></div>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cardClasses} onClick={onClick}>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <h3 className="text-sm font-medium text-gray-500 mb-2">{title}</h3>
          <p className={`${getValueSizeClasses()} font-bold ${getValueClasses()}`}>
            {value}
          </p>
          {subtitle && (
            <p className="text-sm text-gray-600 mt-1">{subtitle}</p>
          )}
          {trend && (
            <div className={`flex items-center mt-2 text-sm ${getTrendClasses()}`}>
              {trend.direction === 'up' && (
                <ArrowUpIcon className="h-4 w-4 mr-1" />
              )}
              {trend.direction === 'down' && (
                <ArrowDownIcon className="h-4 w-4 mr-1" />
              )}
              <span className="font-medium">{trend.value}%</span>
              <span className="ml-1 text-gray-600">{trend.label}</span>
            </div>
          )}
        </div>
        {icon && (
          <div className="flex-shrink-0 ml-4">
            <div className="h-8 w-8 text-gray-400">
              {icon}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MetricCard;
