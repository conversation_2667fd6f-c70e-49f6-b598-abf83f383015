import React from 'react';

interface RoleIndicatorBadgeProps {
  role: 'admin' | 'manager' | 'driver';
  size?: 'sm' | 'md' | 'lg';
  variant?: 'solid' | 'outline' | 'subtle';
  showIcon?: boolean;
  className?: string;
}

const RoleIndicatorBadge: React.FC<RoleIndicatorBadgeProps> = ({
  role,
  size = 'md',
  variant = 'solid',
  showIcon = true,
  className = '',
}) => {
  // Role configuration
  const roleConfig = {
    admin: {
      label: 'Administrator',
      shortLabel: 'Admin',
      icon: '👑',
      colors: {
        solid: 'bg-purple-600 text-white',
        outline: 'border-purple-600 text-purple-600 bg-transparent',
        subtle: 'bg-purple-100 text-purple-800',
      },
    },
    manager: {
      label: 'Manager',
      shortLabel: 'Manager',
      icon: '👔',
      colors: {
        solid: 'bg-blue-600 text-white',
        outline: 'border-blue-600 text-blue-600 bg-transparent',
        subtle: 'bg-blue-100 text-blue-800',
      },
    },
    driver: {
      label: 'Driver',
      shortLabel: 'Driver',
      icon: '🚗',
      colors: {
        solid: 'bg-green-600 text-white',
        outline: 'border-green-600 text-green-600 bg-transparent',
        subtle: 'bg-green-100 text-green-800',
      },
    },
  };

  // Size configuration
  const sizeConfig = {
    sm: {
      container: 'px-2 py-1 text-xs',
      icon: 'text-xs',
      spacing: 'space-x-1',
    },
    md: {
      container: 'px-3 py-1 text-sm',
      icon: 'text-sm',
      spacing: 'space-x-1.5',
    },
    lg: {
      container: 'px-4 py-2 text-base',
      icon: 'text-base',
      spacing: 'space-x-2',
    },
  };

  const config = roleConfig[role];
  const sizeClasses = sizeConfig[size];
  const colorClasses = config.colors[variant];

  // Base classes
  const baseClasses = 'inline-flex items-center font-medium rounded-full';
  const borderClass = variant === 'outline' ? 'border' : '';

  // Determine label based on size
  const getLabel = () => {
    if (size === 'sm') {
      return config.shortLabel;
    }
    return config.label;
  };

  return (
    <span
      className={`
        ${baseClasses}
        ${sizeClasses.container}
        ${colorClasses}
        ${borderClass}
        ${className}
      `.trim()}
      title={`Role: ${config.label}`}
    >
      <span className={`flex items-center ${sizeClasses.spacing}`}>
        {showIcon && (
          <span className={sizeClasses.icon} role="img" aria-label={`${role} icon`}>
            {config.icon}
          </span>
        )}
        <span>{getLabel()}</span>
      </span>
    </span>
  );
};

export default RoleIndicatorBadge;
