import { apiService } from './api';
import type {
  ApiResponse,
  PaginatedResponse,
  Driver,
  DriverFormData,
  DriverFilters,
  DriverAssignment,
} from '../types';

export class DriverService {
  private readonly baseUrl = '/drivers';

  /**
   * Get all drivers with optional filters and pagination
   */
  async getDrivers(
    filters?: DriverFilters,
    page: number = 1,
    pageSize: number = 10
  ): Promise<PaginatedResponse<Driver>> {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
      });

      if (filters?.status) {
        params.append('status', filters.status);
      }
      if (filters?.search) {
        params.append('search', filters.search);
      }
      if (filters?.vehicleId) {
        params.append('vehicleId', filters.vehicleId);
      }

      const response = await apiService.get<PaginatedResponse<Driver>>(
        `${this.baseUrl}?${params.toString()}`
      );

      return response.data;
    } catch (error) {
      console.error('Failed to get drivers:', error);
      throw error;
    }
  }

  /**
   * Get driver by ID
   */
  async getDriverById(id: string): Promise<ApiResponse<Driver>> {
    try {
      return await apiService.get<Driver>(`${this.baseUrl}/${id}`);
    } catch (error) {
      console.error(`Failed to get driver ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create new driver
   */
  async createDriver(driverData: DriverFormData): Promise<ApiResponse<Driver>> {
    try {
      return await apiService.post<Driver>(`${this.baseUrl}`, driverData);
    } catch (error) {
      console.error('Failed to create driver:', error);
      throw error;
    }
  }

  /**
   * Update existing driver
   */
  async updateDriver(
    id: string,
    driverData: Partial<DriverFormData>
  ): Promise<ApiResponse<Driver>> {
    try {
      return await apiService.put<Driver>(`${this.baseUrl}/${id}`, driverData);
    } catch (error) {
      console.error(`Failed to update driver ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete driver
   */
  async deleteDriver(id: string): Promise<ApiResponse<void>> {
    try {
      return await apiService.delete<void>(`${this.baseUrl}/${id}`);
    } catch (error) {
      console.error(`Failed to delete driver ${id}:`, error);
      throw error;
    }
  }

  /**
   * Update driver status
   */
  async updateDriverStatus(
    id: string,
    status: 'active' | 'inactive'
  ): Promise<ApiResponse<Driver>> {
    try {
      return await apiService.patch<Driver>(`${this.baseUrl}/${id}/status`, {
        status,
      });
    } catch (error) {
      console.error(`Failed to update driver ${id} status:`, error);
      throw error;
    }
  }

  /**
   * Assign vehicle to driver
   */
  async assignVehicle(
    driverId: string,
    vehicleId: string
  ): Promise<ApiResponse<DriverAssignment>> {
    try {
      return await apiService.post<DriverAssignment>(
        `${this.baseUrl}/${driverId}/assign-vehicle`,
        {
          vehicleId,
        }
      );
    } catch (error) {
      console.error(`Failed to assign vehicle to driver ${driverId}:`, error);
      throw error;
    }
  }

  /**
   * Unassign vehicle from driver
   */
  async unassignVehicle(driverId: string): Promise<ApiResponse<void>> {
    try {
      return await apiService.post<void>(
        `${this.baseUrl}/${driverId}/unassign-vehicle`
      );
    } catch (error) {
      console.error(
        `Failed to unassign vehicle from driver ${driverId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Get driver's driving history
   */
  async getDrivingHistory(
    driverId: string,
    startDate?: string,
    endDate?: string,
    page: number = 1,
    pageSize: number = 10
  ): Promise<PaginatedResponse<any>> {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
      });

      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      const response = await apiService.get<PaginatedResponse<any>>(
        `${this.baseUrl}/${driverId}/driving-history?${params.toString()}`
      );
      return response.data;
    } catch (error) {
      console.error(
        `Failed to get driving history for driver ${driverId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Get driver performance metrics
   */
  async getDriverPerformance(
    driverId: string,
    period: 'day' | 'week' | 'month' = 'week'
  ): Promise<ApiResponse<any>> {
    try {
      return await apiService.get<any>(
        `${this.baseUrl}/${driverId}/performance?period=${period}`
      );
    } catch (error) {
      console.error(`Failed to get performance for driver ${driverId}:`, error);
      throw error;
    }
  }

  /**
   * Get driver's current location
   */
  async getDriverLocation(driverId: string): Promise<ApiResponse<any>> {
    try {
      return await apiService.get<any>(`${this.baseUrl}/${driverId}/location`);
    } catch (error) {
      console.error(`Failed to get location for driver ${driverId}:`, error);
      throw error;
    }
  }

  /**
   * Get all available drivers (not assigned to vehicles)
   */
  async getAvailableDrivers(): Promise<ApiResponse<Driver[]>> {
    try {
      return await apiService.get<Driver[]>(`${this.baseUrl}/available`);
    } catch (error) {
      console.error('Failed to get available drivers:', error);
      throw error;
    }
  }

  /**
   * Get driver's vehicle assignment history
   */
  async getAssignmentHistory(
    driverId: string,
    page: number = 1,
    pageSize: number = 10
  ): Promise<PaginatedResponse<any>> {
    try {
      const response = await apiService.get<PaginatedResponse<any>>(
        `${this.baseUrl}/${driverId}/assignment-history?page=${page}&pageSize=${pageSize}`
      );
      return response.data;
    } catch (error) {
      console.error(
        `Failed to get assignment history for driver ${driverId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Update driver license information
   */
  async updateLicense(
    driverId: string,
    licenseData: {
      licenseNumber: string;
      expiryDate: string;
      licenseClass: string;
      restrictions?: string;
    }
  ): Promise<ApiResponse<Driver>> {
    try {
      return await apiService.patch<Driver>(
        `${this.baseUrl}/${driverId}/license`,
        licenseData
      );
    } catch (error) {
      console.error(`Failed to update license for driver ${driverId}:`, error);
      throw error;
    }
  }

  /**
   * Get driver violations/incidents
   */
  async getViolations(
    driverId: string,
    page: number = 1,
    pageSize: number = 10
  ): Promise<PaginatedResponse<any>> {
    try {
      const response = await apiService.get<PaginatedResponse<any>>(
        `${this.baseUrl}/${driverId}/violations?page=${page}&pageSize=${pageSize}`
      );
      return response.data;
    } catch (error) {
      console.error(`Failed to get violations for driver ${driverId}:`, error);
      throw error;
    }
  }

  /**
   * Add driver violation/incident
   */
  async addViolation(
    driverId: string,
    violationData: {
      type: string;
      description: string;
      date: string;
      severity: 'low' | 'medium' | 'high';
      fineAmount?: number;
      notes?: string;
    }
  ): Promise<ApiResponse<any>> {
    try {
      return await apiService.post<any>(
        `${this.baseUrl}/${driverId}/violations`,
        violationData
      );
    } catch (error) {
      console.error(`Failed to add violation for driver ${driverId}:`, error);
      throw error;
    }
  }

  /**
   * Bulk update driver statuses
   */
  async bulkUpdateStatus(
    driverIds: string[],
    status: 'active' | 'inactive'
  ): Promise<ApiResponse<Driver[]>> {
    try {
      return await apiService.post<Driver[]>(
        `${this.baseUrl}/bulk-update-status`,
        {
          driverIds,
          status,
        }
      );
    } catch (error) {
      console.error('Failed to bulk update driver statuses:', error);
      throw error;
    }
  }

  /**
   * Export drivers data
   */
  async exportDrivers(
    format: 'csv' | 'xlsx' = 'csv',
    filters?: DriverFilters
  ): Promise<Blob> {
    try {
      const params = new URLSearchParams({ format });

      if (filters?.status) params.append('status', filters.status);
      if (filters?.search) params.append('search', filters.search);
      if (filters?.vehicleId) params.append('vehicleId', filters.vehicleId);

      const response = await apiService.get<Blob>(
        `${this.baseUrl}/export?${params.toString()}`,
        { responseType: 'blob' }
      );

      return response.data;
    } catch (error) {
      console.error('Failed to export drivers:', error);
      throw error;
    }
  }
}

// Create and export singleton instance
export const driverService = new DriverService();
export default driverService;
