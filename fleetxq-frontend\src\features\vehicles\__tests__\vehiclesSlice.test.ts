import { configureStore } from '@reduxjs/toolkit';
import {
  vehiclesSlice,
  fetchVehicles,
  createVehicle,
  updateVehicle,
  deleteVehicle,
  fetchVehicleAnalytics,
  performBulkOperation,
  assignDriverToVehicle,
  clearError,
  setFilters,
  setPagination,
  setSelectedVehicles,
  toggleVehicleSelection,
  addRealTimeUpdate,
} from '../vehiclesSlice';
import type { Vehicle, VehicleRealTimeUpdate, VehicleBulkOperation } from '../../../types';

// Mock API service
jest.mock('../../../services/api', () => ({
  apiService: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    patch: jest.fn(),
    delete: jest.fn(),
  },
}));

const mockVehicle: Vehicle = {
  id: 'vehicle-1',
  vehicleName: 'Fleet Vehicle 001',
  make: 'Ford',
  model: 'Transit',
  year: 2022,
  licensePlate: 'ABC-123',
  vin: '1HGBH41JXMN109186',
  vehicleType: 'Van',
  brand: 'Ford',
  color: 'White',
  fuelType: 'Diesel',
  fuelTankCapacity: 80,
  status: 'active',
  driverId: 'driver-1',
  currentMileage: 15000,
  lastMaintenanceDate: '2024-01-15T10:00:00Z',
  nextMaintenanceDate: '2024-04-15T10:00:00Z',
  currentLocation: {
    latitude: 40.7128,
    longitude: -74.0060,
    address: '123 Main St, New York, NY',
    timestamp: '2024-01-20T14:30:00Z'
  },
  currentSpeed: 45,
  currentFuelLevel: 75,
  engineStatus: 'on',
  lastTelemetryUpdate: '2024-01-20T14:30:00Z',
  isMoving: true,
  needsMaintenance: false,
  isAvailable: true,
  requiresImmediateAttention: false,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-20T14:30:00Z'
};

const createTestStore = () => {
  return configureStore({
    reducer: {
      vehicles: vehiclesSlice.reducer,
    },
  });
};

describe('vehiclesSlice', () => {
  let store: ReturnType<typeof createTestStore>;

  beforeEach(() => {
    store = createTestStore();
    jest.clearAllMocks();
  });

  describe('initial state', () => {
    it('should have correct initial state', () => {
      const state = store.getState().vehicles;

      expect(state).toEqual({
        vehicles: [],
        currentVehicle: null,
        selectedVehicles: [],
        vehicleAnalytics: {},
        maintenanceAlerts: [],
        assignmentHistory: [],
        maintenanceHistory: [],
        realTimeUpdates: [],
        isLoading: false,
        isCreating: false,
        isUpdating: false,
        isDeleting: false,
        isBulkOperating: false,
        isLoadingAnalytics: false,
        isLoadingMaintenanceAlerts: false,
        error: null,
        bulkOperationError: null,
        analyticsError: null,
        pagination: {
          page: 1,
          pageSize: 10,
          total: 0,
          totalPages: 0,
        },
        filters: {},
        assignmentConflicts: [],
      });
    });
  });

  describe('synchronous actions', () => {
    it('should clear error', () => {
      // Set initial error state
      store.dispatch({ type: 'vehicles/fetchVehicles/rejected', payload: 'Test error' });
      expect(store.getState().vehicles.error).toBe('Test error');

      // Clear error
      store.dispatch(clearError());
      expect(store.getState().vehicles.error).toBeNull();
      expect(store.getState().vehicles.bulkOperationError).toBeNull();
      expect(store.getState().vehicles.analyticsError).toBeNull();
    });

    it('should set filters', () => {
      const filters = { status: 'active' as const, search: 'test' };
      store.dispatch(setFilters(filters));

      expect(store.getState().vehicles.filters).toEqual(filters);
    });

    it('should merge filters when setting new ones', () => {
      store.dispatch(setFilters({ status: 'active' as const }));
      store.dispatch(setFilters({ search: 'test' }));

      expect(store.getState().vehicles.filters).toEqual({
        status: 'active',
        search: 'test',
      });
    });

    it('should set pagination', () => {
      const pagination = { page: 2, pageSize: 20 };
      store.dispatch(setPagination(pagination));

      expect(store.getState().vehicles.pagination).toEqual({
        page: 2,
        pageSize: 20,
        total: 0,
        totalPages: 0,
      });
    });

    it('should set selected vehicles', () => {
      const vehicleIds = ['vehicle-1', 'vehicle-2'];
      store.dispatch(setSelectedVehicles(vehicleIds));

      expect(store.getState().vehicles.selectedVehicles).toEqual(vehicleIds);
    });

    it('should toggle vehicle selection', () => {
      // Add vehicle to selection
      store.dispatch(toggleVehicleSelection('vehicle-1'));
      expect(store.getState().vehicles.selectedVehicles).toEqual(['vehicle-1']);

      // Remove vehicle from selection
      store.dispatch(toggleVehicleSelection('vehicle-1'));
      expect(store.getState().vehicles.selectedVehicles).toEqual([]);

      // Add multiple vehicles
      store.dispatch(toggleVehicleSelection('vehicle-1'));
      store.dispatch(toggleVehicleSelection('vehicle-2'));
      expect(store.getState().vehicles.selectedVehicles).toEqual(['vehicle-1', 'vehicle-2']);

      // Remove one vehicle
      store.dispatch(toggleVehicleSelection('vehicle-1'));
      expect(store.getState().vehicles.selectedVehicles).toEqual(['vehicle-2']);
    });

    it('should add real-time update', () => {
      const realTimeUpdate: VehicleRealTimeUpdate = {
        vehicleId: 'vehicle-1',
        updateType: 'location',
        data: {
          location: {
            latitude: 40.7128,
            longitude: -74.0060,
            address: '123 Main St, New York, NY',
            timestamp: '2024-01-20T14:30:00Z'
          }
        },
        timestamp: '2024-01-20T14:30:00Z'
      };

      store.dispatch(addRealTimeUpdate(realTimeUpdate));

      expect(store.getState().vehicles.realTimeUpdates).toHaveLength(1);
      expect(store.getState().vehicles.realTimeUpdates[0]).toEqual(realTimeUpdate);
    });

    it('should limit real-time updates to 100 entries', () => {
      // Add 101 updates
      for (let i = 0; i < 101; i++) {
        const update: VehicleRealTimeUpdate = {
          vehicleId: `vehicle-${i}`,
          updateType: 'location',
          data: {},
          timestamp: `2024-01-20T14:${i.toString().padStart(2, '0')}:00Z`
        };
        store.dispatch(addRealTimeUpdate(update));
      }

      // Should only keep the latest 100
      expect(store.getState().vehicles.realTimeUpdates).toHaveLength(100);
      expect(store.getState().vehicles.realTimeUpdates[0].vehicleId).toBe('vehicle-100');
    });
  });

  describe('async thunks', () => {
    describe('fetchVehicles', () => {
      it('should handle fetchVehicles.pending', () => {
        store.dispatch({ type: fetchVehicles.pending.type });

        const state = store.getState().vehicles;
        expect(state.isLoading).toBe(true);
        expect(state.error).toBeNull();
      });

      it('should handle fetchVehicles.fulfilled', () => {
        const mockResponse = {
          data: [mockVehicle],
          pagination: {
            page: 1,
            pageSize: 10,
            total: 1,
            totalPages: 1,
          },
        };

        store.dispatch({
          type: fetchVehicles.fulfilled.type,
          payload: mockResponse,
        });

        const state = store.getState().vehicles;
        expect(state.isLoading).toBe(false);
        expect(state.vehicles).toEqual([mockVehicle]);
        expect(state.pagination).toEqual(mockResponse.pagination);
        expect(state.error).toBeNull();
      });

      it('should handle fetchVehicles.rejected', () => {
        const errorMessage = 'Failed to fetch vehicles';
        store.dispatch({
          type: fetchVehicles.rejected.type,
          payload: errorMessage,
        });

        const state = store.getState().vehicles;
        expect(state.isLoading).toBe(false);
        expect(state.error).toBe(errorMessage);
      });
    });

    describe('createVehicle', () => {
      it('should handle createVehicle.pending', () => {
        store.dispatch({ type: createVehicle.pending.type });

        const state = store.getState().vehicles;
        expect(state.isCreating).toBe(true);
        expect(state.error).toBeNull();
      });

      it('should handle createVehicle.fulfilled', () => {
        store.dispatch({
          type: createVehicle.fulfilled.type,
          payload: mockVehicle,
        });

        const state = store.getState().vehicles;
        expect(state.isCreating).toBe(false);
        expect(state.vehicles).toContain(mockVehicle);
        expect(state.error).toBeNull();
      });

      it('should handle createVehicle.rejected', () => {
        const errorMessage = 'Failed to create vehicle';
        store.dispatch({
          type: createVehicle.rejected.type,
          payload: errorMessage,
        });

        const state = store.getState().vehicles;
        expect(state.isCreating).toBe(false);
        expect(state.error).toBe(errorMessage);
      });
    });

    describe('updateVehicle', () => {
      beforeEach(() => {
        // Add initial vehicle to state
        store.dispatch({
          type: fetchVehicles.fulfilled.type,
          payload: {
            data: [mockVehicle],
            pagination: { page: 1, pageSize: 10, total: 1, totalPages: 1 },
          },
        });
      });

      it('should handle updateVehicle.fulfilled', () => {
        const updatedVehicle = { ...mockVehicle, vehicleName: 'Updated Vehicle' };
        store.dispatch({
          type: updateVehicle.fulfilled.type,
          payload: updatedVehicle,
        });

        const state = store.getState().vehicles;
        expect(state.vehicles[0]).toEqual(updatedVehicle);
        expect(state.isUpdating).toBe(false);
      });
    });

    describe('deleteVehicle', () => {
      beforeEach(() => {
        // Add initial vehicle to state
        store.dispatch({
          type: fetchVehicles.fulfilled.type,
          payload: {
            data: [mockVehicle],
            pagination: { page: 1, pageSize: 10, total: 1, totalPages: 1 },
          },
        });
      });

      it('should handle deleteVehicle.fulfilled', () => {
        store.dispatch({
          type: deleteVehicle.fulfilled.type,
          payload: mockVehicle.id,
        });

        const state = store.getState().vehicles;
        expect(state.vehicles).toHaveLength(0);
        expect(state.isDeleting).toBe(false);
      });
    });

    describe('performBulkOperation', () => {
      beforeEach(() => {
        // Add initial vehicles to state
        const vehicles = [mockVehicle, { ...mockVehicle, id: 'vehicle-2' }];
        store.dispatch({
          type: fetchVehicles.fulfilled.type,
          payload: {
            data: vehicles,
            pagination: { page: 1, pageSize: 10, total: 2, totalPages: 1 },
          },
        });
        store.dispatch(setSelectedVehicles(['vehicle-1', 'vehicle-2']));
      });

      it('should handle performBulkOperation.pending', () => {
        store.dispatch({ type: performBulkOperation.pending.type });

        const state = store.getState().vehicles;
        expect(state.isBulkOperating).toBe(true);
        expect(state.bulkOperationError).toBeNull();
      });

      it('should handle performBulkOperation.fulfilled', () => {
        const updatedVehicles = [
          { ...mockVehicle, status: 'maintenance' as const },
          { ...mockVehicle, id: 'vehicle-2', status: 'maintenance' as const },
        ];

        store.dispatch({
          type: performBulkOperation.fulfilled.type,
          payload: updatedVehicles,
        });

        const state = store.getState().vehicles;
        expect(state.isBulkOperating).toBe(false);
        expect(state.selectedVehicles).toEqual([]);
        expect(state.vehicles[0].status).toBe('maintenance');
        expect(state.vehicles[1].status).toBe('maintenance');
      });

      it('should handle performBulkOperation.rejected', () => {
        const errorMessage = 'Bulk operation failed';
        store.dispatch({
          type: performBulkOperation.rejected.type,
          payload: errorMessage,
        });

        const state = store.getState().vehicles;
        expect(state.isBulkOperating).toBe(false);
        expect(state.bulkOperationError).toBe(errorMessage);
      });
    });

    describe('assignDriverToVehicle', () => {
      beforeEach(() => {
        store.dispatch({
          type: fetchVehicles.fulfilled.type,
          payload: {
            data: [mockVehicle],
            pagination: { page: 1, pageSize: 10, total: 1, totalPages: 1 },
          },
        });
      });

      it('should handle assignDriverToVehicle.fulfilled', () => {
        const updatedVehicle = { ...mockVehicle, driverId: 'new-driver' };
        store.dispatch({
          type: assignDriverToVehicle.fulfilled.type,
          payload: updatedVehicle,
        });

        const state = store.getState().vehicles;
        expect(state.vehicles[0].driverId).toBe('new-driver');
      });
    });

    describe('fetchVehicleAnalytics', () => {
      it('should handle fetchVehicleAnalytics.pending', () => {
        store.dispatch({ type: fetchVehicleAnalytics.pending.type });

        const state = store.getState().vehicles;
        expect(state.isLoadingAnalytics).toBe(true);
        expect(state.analyticsError).toBeNull();
      });

      it('should handle fetchVehicleAnalytics.fulfilled', () => {
        const mockAnalytics = {
          vehicleId: 'vehicle-1',
          utilizationData: { labels: [], values: [] },
          fuelConsumptionTrend: { labels: [], values: [] },
          performanceMetrics: {
            totalDistance: 1000,
            averageSpeed: 50,
            fuelEfficiency: 8.5,
            utilizationRate: 85,
            maintenanceCosts: 500,
          },
          maintenanceHistory: [],
          assignmentHistory: [],
        };

        store.dispatch({
          type: fetchVehicleAnalytics.fulfilled.type,
          payload: { vehicleId: 'vehicle-1', analytics: mockAnalytics },
        });

        const state = store.getState().vehicles;
        expect(state.isLoadingAnalytics).toBe(false);
        expect(state.vehicleAnalytics['vehicle-1']).toEqual(mockAnalytics);
        expect(state.analyticsError).toBeNull();
      });

      it('should handle fetchVehicleAnalytics.rejected', () => {
        const errorMessage = 'Failed to fetch analytics';
        store.dispatch({
          type: fetchVehicleAnalytics.rejected.type,
          payload: errorMessage,
        });

        const state = store.getState().vehicles;
        expect(state.isLoadingAnalytics).toBe(false);
        expect(state.analyticsError).toBe(errorMessage);
      });
    });
  });
});
