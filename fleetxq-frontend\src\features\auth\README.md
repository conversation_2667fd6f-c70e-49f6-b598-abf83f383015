# FleetXQ Authentication System

A comprehensive authentication system built with React, Redux Toolkit, and TypeScript, featuring JWT token management, role-based access control, and automatic token refresh.

## 🚀 Features

- **JWT Authentication** - Secure token-based authentication
- **Role-Based Access Control** - Admin, Manager, and Driver roles
- **Permission System** - Granular permission-based UI rendering
- **Automatic Token Refresh** - Seamless token renewal
- **Secure Token Storage** - Encrypted token storage with fallback options
- **Route Protection** - Comprehensive route guarding
- **Form Validation** - Enhanced login form with validation
- **Loading States** - Proper loading and error handling
- **TypeScript Support** - Full type safety

## 📁 File Structure

```
src/features/auth/
├── AuthProvider.tsx          # Authentication initialization
├── LoginPage.tsx            # Enhanced login form
├── LogoutButton.tsx         # Configurable logout button
├── UserProfileDropdown.tsx  # User profile with role indicator
├── RoleIndicatorBadge.tsx   # Visual role indicator
├── authSlice.ts            # Redux authentication slice
└── __tests__/              # Comprehensive test suite
    └── LoginPage.test.tsx

src/hooks/
├── useAuth.ts              # Primary authentication hook
├── usePermissions.ts       # Permission management hook
└── useTokenRefresh.ts      # Automatic token refresh

src/components/
├── RoleGuard.tsx           # Role-based component guarding
└── PermissionGuard.tsx     # Permission-based UI guarding

src/routes/
├── ProtectedRoute.tsx      # Route protection component
└── routeConfig.ts          # Route configuration with permissions

src/utils/
└── tokenStorage.ts         # Secure token storage utility

src/services/
└── authInterceptor.ts      # API request/response interceptors
```

## 🔧 Quick Start

### 1. Basic Setup

```typescript
// App.tsx
import { Provider } from 'react-redux';
import { RouterProvider } from 'react-router-dom';
import { store } from './store';
import { router } from './routes';
import AuthProvider from './features/auth/AuthProvider';

function App() {
  return (
    <Provider store={store}>
      <AuthProvider>
        <RouterProvider router={router} />
      </AuthProvider>
    </Provider>
  );
}
```

### 2. Using Authentication Hook

```typescript
import { useAuth } from '../hooks/useAuth';

function MyComponent() {
  const { user, isAuthenticated, login, logout } = useAuth();

  const handleLogin = async () => {
    const result = await login({ email, password });
    if (result.success) {
      console.log('Login successful');
    }
  };

  return (
    <div>
      {isAuthenticated ? (
        <div>
          <p>Welcome, {user?.name}!</p>
          <button onClick={logout}>Logout</button>
        </div>
      ) : (
        <button onClick={handleLogin}>Login</button>
      )}
    </div>
  );
}
```

### 3. Route Protection

```typescript
import ProtectedRoute from '../routes/ProtectedRoute';

// Basic protection
<ProtectedRoute>
  <DashboardPage />
</ProtectedRoute>

// Role-based protection
<ProtectedRoute requiredRole="admin">
  <AdminPanel />
</ProtectedRoute>

// Permission-based protection
<ProtectedRoute requiredPermissions={['manage_users']}>
  <UserManagement />
</ProtectedRoute>
```

### 4. Permission-Based UI

```typescript
import { usePermissions } from '../hooks/usePermissions';
import PermissionGuard from '../components/PermissionGuard';

function MyComponent() {
  const { hasPermission } = usePermissions();

  return (
    <div>
      {hasPermission('manage_vehicles') && (
        <button>Add Vehicle</button>
      )}
      
      <PermissionGuard permissions={['view_reports']}>
        <ReportsSection />
      </PermissionGuard>
    </div>
  );
}
```

## 🔐 Role & Permission System

### Roles

- **Admin**: Full system access
- **Manager**: Fleet and driver management
- **Driver**: Limited vehicle and telemetry access

### Permissions

```typescript
type Permission = 
  | 'view_dashboard'
  | 'view_vehicles' | 'manage_vehicles'
  | 'view_drivers' | 'manage_drivers'
  | 'view_telemetry'
  | 'view_alerts' | 'manage_alerts'
  | 'view_admin_panel'
  | 'manage_users'
  | 'manage_system_settings'
  | 'view_reports' | 'export_data';
```

## 🧪 Testing

### Running Tests

```bash
npm run test                # Run all tests
npm run test:coverage      # Run with coverage
npm run test:ui           # Run with UI
```

### Test Structure

```typescript
// Example test
import { renderWithProviders, mockUsers } from '../../test/utils';

describe('LoginPage', () => {
  it('should render login form', () => {
    renderWithProviders(<LoginPage />, {
      preloadedState: createUnauthenticatedState(),
    });
    
    expect(screen.getByText('Sign in to FleetXQ')).toBeInTheDocument();
  });
});
```

## 📚 API Reference

### useAuth Hook

```typescript
const {
  user,              // Current user object
  isAuthenticated,   // Authentication status
  isLoading,         // Loading state
  error,             // Error message
  login,             // Login function
  logout,            // Logout function
  initialize,        // Initialize auth
  clearAuthError,    // Clear error
  hasRole,           // Check user role
  hasAnyRole,        // Check multiple roles
} = useAuth();
```

### usePermissions Hook

```typescript
const {
  permissions,       // User permissions array
  hasPermission,     // Check single permission
  hasAnyPermission,  // Check multiple permissions (OR)
  hasAllPermissions, // Check multiple permissions (AND)
  canAccessRoute,    // Route access check
  getUIPermissions,  // UI permission object
} = usePermissions();
```

### useTokenRefresh Hook

```typescript
const {
  refreshTokenNow,   // Manual token refresh
  isTokenExpired,    // Check if token expired
  needsRefresh,      // Check if refresh needed
  getTimeUntilExpiry,// Time until expiry
  isRefreshing,      // Refresh in progress
} = useTokenRefresh();
```

## 🔧 Configuration

### Environment Variables

```env
VITE_API_URL=http://localhost:5000/api
```

### Token Storage Options

```typescript
tokenStorage.setTokens(accessToken, refreshToken, {
  useSessionStorage: false,  // Use localStorage by default
  encrypt: true             // Encrypt tokens
});
```

## 🚨 Error Handling

The system includes comprehensive error handling:

- Network errors
- Token expiry
- Invalid credentials
- Permission denied
- Server errors

## 🔄 Token Refresh

Automatic token refresh occurs:

- 5 minutes before token expiry
- On 401 API responses
- During app initialization
- Manual refresh available

## 📖 Documentation

For complete documentation, see:
- [Authentication Guide](../../docs/AUTHENTICATION.md)
- [API Documentation](../../docs/API.md)
- [Testing Guide](../../docs/TESTING.md)

## 🤝 Contributing

1. Follow TypeScript best practices
2. Write comprehensive tests
3. Update documentation
4. Follow existing code patterns
5. Test authentication flows thoroughly

## 📄 License

This project is part of the FleetXQ application.
