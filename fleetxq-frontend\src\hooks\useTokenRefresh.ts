import { useEffect, useCallback, useRef } from 'react';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { refreshToken, logoutUser, selectAuth } from '../features/auth/authSlice';

// Token refresh configuration
const TOKEN_REFRESH_BUFFER = 5 * 60 * 1000; // 5 minutes before expiry
const TOKEN_CHECK_INTERVAL = 60 * 1000; // Check every minute

// Utility function to decode JWT token and get expiry
const getTokenExpiry = (token: string): number | null => {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload.exp * 1000; // Convert to milliseconds
  } catch (error) {
    console.error('Error decoding token:', error);
    return null;
  }
};

// Utility function to check if token needs refresh
const shouldRefreshToken = (token: string): boolean => {
  const expiry = getTokenExpiry(token);
  if (!expiry) return false;
  
  const now = Date.now();
  return (expiry - now) <= TOKEN_REFRESH_BUFFER;
};

export const useTokenRefresh = () => {
  const dispatch = useAppDispatch();
  const { token, isAuthenticated } = useAppSelector(selectAuth);
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isRefreshingRef = useRef(false);

  // Clear any existing timeout
  const clearRefreshTimeout = useCallback(() => {
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current);
      refreshTimeoutRef.current = null;
    }
  }, []);

  // Schedule token refresh
  const scheduleTokenRefresh = useCallback((token: string) => {
    const expiry = getTokenExpiry(token);
    if (!expiry) return;

    const now = Date.now();
    const timeUntilRefresh = Math.max(0, expiry - now - TOKEN_REFRESH_BUFFER);

    clearRefreshTimeout();
    
    refreshTimeoutRef.current = setTimeout(() => {
      if (isAuthenticated && !isRefreshingRef.current) {
        refreshTokenNow();
      }
    }, timeUntilRefresh);
  }, [isAuthenticated, clearRefreshTimeout]);

  // Manually refresh token
  const refreshTokenNow = useCallback(async () => {
    if (isRefreshingRef.current || !isAuthenticated) {
      return { success: false, error: 'Already refreshing or not authenticated' };
    }

    isRefreshingRef.current = true;

    try {
      const result = await dispatch(refreshToken()).unwrap();
      
      // Schedule next refresh
      if (result.token) {
        scheduleTokenRefresh(result.token);
      }
      
      return { success: true, data: result };
    } catch (error) {
      console.error('Token refresh failed:', error);
      
      // If refresh fails, logout user
      await dispatch(logoutUser());
      
      return { success: false, error: error as string };
    } finally {
      isRefreshingRef.current = false;
    }
  }, [dispatch, isAuthenticated, scheduleTokenRefresh]);

  // Check if token is expired
  const isTokenExpired = useCallback((tokenToCheck?: string) => {
    const tokenToUse = tokenToCheck || token;
    if (!tokenToUse) return true;
    
    const expiry = getTokenExpiry(tokenToUse);
    if (!expiry) return true;
    
    return Date.now() >= expiry;
  }, [token]);

  // Check if token needs refresh
  const needsRefresh = useCallback((tokenToCheck?: string) => {
    const tokenToUse = tokenToCheck || token;
    if (!tokenToUse) return false;
    
    return shouldRefreshToken(tokenToUse);
  }, [token]);

  // Get time until token expires
  const getTimeUntilExpiry = useCallback((tokenToCheck?: string) => {
    const tokenToUse = tokenToCheck || token;
    if (!tokenToUse) return 0;
    
    const expiry = getTokenExpiry(tokenToUse);
    if (!expiry) return 0;
    
    return Math.max(0, expiry - Date.now());
  }, [token]);

  // Set up automatic token refresh
  useEffect(() => {
    if (isAuthenticated && token && !isTokenExpired(token)) {
      scheduleTokenRefresh(token);
    } else {
      clearRefreshTimeout();
    }

    return () => {
      clearRefreshTimeout();
    };
  }, [isAuthenticated, token, scheduleTokenRefresh, clearRefreshTimeout, isTokenExpired]);

  // Periodic token check (fallback mechanism)
  useEffect(() => {
    if (!isAuthenticated) return;

    const interval = setInterval(() => {
      if (token && needsRefresh(token) && !isRefreshingRef.current) {
        refreshTokenNow();
      }
    }, TOKEN_CHECK_INTERVAL);

    return () => clearInterval(interval);
  }, [isAuthenticated, token, needsRefresh, refreshTokenNow]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearRefreshTimeout();
      isRefreshingRef.current = false;
    };
  }, [clearRefreshTimeout]);

  return {
    refreshTokenNow,
    isTokenExpired,
    needsRefresh,
    getTimeUntilExpiry,
    isRefreshing: isRefreshingRef.current,
  };
};
