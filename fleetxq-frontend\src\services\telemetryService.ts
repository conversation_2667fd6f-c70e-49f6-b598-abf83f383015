import { apiService } from './api';
import type {
  ApiResponse,
  PaginatedResponse,
  TelemetryData,
  TelemetryQueryParams,
  TelemetryFilters,
  TelemetryUpdate,
} from '../types';

export class TelemetryService {
  private readonly baseUrl = '/telemetry';

  /**
   * Get telemetry data with optional filters and pagination
   */
  async getTelemetryData(
    params?: TelemetryQueryParams
  ): Promise<PaginatedResponse<TelemetryData>> {
    try {
      const queryParams = new URLSearchParams();

      if (params?.vehicleId) queryParams.append('vehicleId', params.vehicleId);
      if (params?.startDate) queryParams.append('startDate', params.startDate);
      if (params?.endDate) queryParams.append('endDate', params.endDate);
      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.pageSize)
        queryParams.append('pageSize', params.pageSize.toString());

      const response = await apiService.get<PaginatedResponse<TelemetryData>>(
        `${this.baseUrl}?${queryParams.toString()}`
      );

      return response.data;
    } catch (error) {
      console.error('Failed to get telemetry data:', error);
      throw error;
    }
  }

  /**
   * Get real-time telemetry data for a specific vehicle
   */
  async getRealTimeTelemetry(
    vehicleId: string
  ): Promise<ApiResponse<TelemetryData>> {
    try {
      return await apiService.get<TelemetryData>(
        `${this.baseUrl}/real-time/${vehicleId}`
      );
    } catch (error) {
      console.error(
        `Failed to get real-time telemetry for vehicle ${vehicleId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Get historical telemetry data for a specific vehicle
   */
  async getVehicleHistory(
    vehicleId: string,
    startDate?: string,
    endDate?: string,
    page: number = 1,
    pageSize: number = 100
  ): Promise<PaginatedResponse<TelemetryData>> {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
      });

      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      const response = await apiService.get<PaginatedResponse<TelemetryData>>(
        `${this.baseUrl}/vehicle/${vehicleId}/history?${params.toString()}`
      );

      return response.data;
    } catch (error) {
      console.error(
        `Failed to get telemetry history for vehicle ${vehicleId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Get telemetry summary/statistics
   */
  async getTelemetrySummary(
    filters?: TelemetryFilters,
    period: 'hour' | 'day' | 'week' | 'month' = 'day'
  ): Promise<ApiResponse<any>> {
    try {
      const params = new URLSearchParams({ period });

      if (filters?.vehicleId) params.append('vehicleId', filters.vehicleId);
      if (filters?.startDate) params.append('startDate', filters.startDate);
      if (filters?.endDate) params.append('endDate', filters.endDate);

      return await apiService.get<any>(
        `${this.baseUrl}/summary?${params.toString()}`
      );
    } catch (error) {
      console.error('Failed to get telemetry summary:', error);
      throw error;
    }
  }

  /**
   * Get fleet-wide telemetry metrics
   */
  async getFleetMetrics(
    startDate?: string,
    endDate?: string
  ): Promise<ApiResponse<any>> {
    try {
      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      return await apiService.get<any>(
        `${this.baseUrl}/fleet-metrics?${params.toString()}`
      );
    } catch (error) {
      console.error('Failed to get fleet metrics:', error);
      throw error;
    }
  }

  /**
   * Get vehicle location tracking data
   */
  async getLocationTracking(
    vehicleId: string,
    startDate?: string,
    endDate?: string,
    interval: 'minute' | 'hour' | 'day' = 'hour'
  ): Promise<ApiResponse<any[]>> {
    try {
      const params = new URLSearchParams({ interval });
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      return await apiService.get<any[]>(
        `${this.baseUrl}/vehicle/${vehicleId}/location-tracking?${params.toString()}`
      );
    } catch (error) {
      console.error(
        `Failed to get location tracking for vehicle ${vehicleId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Get speed analysis data
   */
  async getSpeedAnalysis(
    vehicleId: string,
    startDate?: string,
    endDate?: string
  ): Promise<ApiResponse<any>> {
    try {
      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      return await apiService.get<any>(
        `${this.baseUrl}/vehicle/${vehicleId}/speed-analysis?${params.toString()}`
      );
    } catch (error) {
      console.error(
        `Failed to get speed analysis for vehicle ${vehicleId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Get fuel consumption data
   */
  async getFuelConsumption(
    vehicleId: string,
    startDate?: string,
    endDate?: string,
    groupBy: 'hour' | 'day' | 'week' = 'day'
  ): Promise<ApiResponse<any[]>> {
    try {
      const params = new URLSearchParams({ groupBy });
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      return await apiService.get<any[]>(
        `${this.baseUrl}/vehicle/${vehicleId}/fuel-consumption?${params.toString()}`
      );
    } catch (error) {
      console.error(
        `Failed to get fuel consumption for vehicle ${vehicleId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Get engine diagnostics data
   */
  async getEngineDiagnostics(
    vehicleId: string,
    startDate?: string,
    endDate?: string
  ): Promise<ApiResponse<any[]>> {
    try {
      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      return await apiService.get<any[]>(
        `${this.baseUrl}/vehicle/${vehicleId}/engine-diagnostics?${params.toString()}`
      );
    } catch (error) {
      console.error(
        `Failed to get engine diagnostics for vehicle ${vehicleId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Get trip data for a vehicle
   */
  async getTripData(
    vehicleId: string,
    startDate?: string,
    endDate?: string,
    page: number = 1,
    pageSize: number = 10
  ): Promise<PaginatedResponse<any>> {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
      });

      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      const response = await apiService.get<PaginatedResponse<any>>(
        `${this.baseUrl}/vehicle/${vehicleId}/trips?${params.toString()}`
      );

      return response.data;
    } catch (error) {
      console.error(`Failed to get trip data for vehicle ${vehicleId}:`, error);
      throw error;
    }
  }

  /**
   * Get geofence violations
   */
  async getGeofenceViolations(
    vehicleId?: string,
    startDate?: string,
    endDate?: string,
    page: number = 1,
    pageSize: number = 10
  ): Promise<PaginatedResponse<any>> {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
      });

      if (vehicleId) params.append('vehicleId', vehicleId);
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      const response = await apiService.get<PaginatedResponse<any>>(
        `${this.baseUrl}/geofence-violations?${params.toString()}`
      );

      return response.data;
    } catch (error) {
      console.error('Failed to get geofence violations:', error);
      throw error;
    }
  }

  /**
   * Export telemetry data
   */
  async exportTelemetryData(
    format: 'csv' | 'xlsx' = 'csv',
    filters?: TelemetryFilters
  ): Promise<Blob> {
    try {
      const params = new URLSearchParams({ format });

      if (filters?.vehicleId) params.append('vehicleId', filters.vehicleId);
      if (filters?.startDate) params.append('startDate', filters.startDate);
      if (filters?.endDate) params.append('endDate', filters.endDate);

      const response = await apiService.get<Blob>(
        `${this.baseUrl}/export?${params.toString()}`,
        { responseType: 'blob' }
      );

      return response.data;
    } catch (error) {
      console.error('Failed to export telemetry data:', error);
      throw error;
    }
  }

  /**
   * Get live vehicle positions for map display
   */
  async getLiveVehiclePositions(): Promise<ApiResponse<any[]>> {
    try {
      return await apiService.get<any[]>(`${this.baseUrl}/live-positions`);
    } catch (error) {
      console.error('Failed to get live vehicle positions:', error);
      throw error;
    }
  }

  /**
   * Get vehicle idle time analysis
   */
  async getIdleTimeAnalysis(
    vehicleId: string,
    startDate?: string,
    endDate?: string
  ): Promise<ApiResponse<any>> {
    try {
      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      return await apiService.get<any>(
        `${this.baseUrl}/vehicle/${vehicleId}/idle-time?${params.toString()}`
      );
    } catch (error) {
      console.error(
        `Failed to get idle time analysis for vehicle ${vehicleId}:`,
        error
      );
      throw error;
    }
  }
}

// Create and export singleton instance
export const telemetryService = new TelemetryService();
export default telemetryService;
