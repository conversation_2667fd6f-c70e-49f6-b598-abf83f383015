import React from 'react';
import { screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import RoleGuard from '../RoleGuard';
import { renderWithProviders, mockUsers, createAuthenticatedState, createUnauthenticatedState } from '../../test/utils';

describe('RoleGuard', () => {
  const TestComponent = () => <div data-testid="guarded-content">Guarded Content</div>;
  const FallbackComponent = () => <div data-testid="fallback-content">Access Denied</div>;

  describe('when user is not authenticated', () => {
    it('should redirect to login page', () => {
      renderWithProviders(
        <RoleGuard allowedRoles={['admin']}>
          <TestComponent />
        </RoleGuard>,
        {
          preloadedState: createUnauthenticatedState(),
          initialEntries: ['/dashboard'],
        }
      );

      expect(screen.queryByTestId('guarded-content')).not.toBeInTheDocument();
    });
  });

  describe('when user is authenticated', () => {
    it('should render content when no role restrictions', () => {
      renderWithProviders(
        <RoleGuard>
          <TestComponent />
        </RoleGuard>,
        {
          preloadedState: createAuthenticatedState(mockUsers.driver),
        }
      );

      expect(screen.getByTestId('guarded-content')).toBeInTheDocument();
    });

    it('should render content when user has allowed role', () => {
      renderWithProviders(
        <RoleGuard allowedRoles={['admin', 'manager']}>
          <TestComponent />
        </RoleGuard>,
        {
          preloadedState: createAuthenticatedState(mockUsers.admin),
        }
      );

      expect(screen.getByTestId('guarded-content')).toBeInTheDocument();
    });

    it('should not render content when user does not have allowed role', () => {
      renderWithProviders(
        <RoleGuard allowedRoles={['admin']}>
          <TestComponent />
        </RoleGuard>,
        {
          preloadedState: createAuthenticatedState(mockUsers.driver),
        }
      );

      expect(screen.queryByTestId('guarded-content')).not.toBeInTheDocument();
    });

    it('should render content when user has required permissions', () => {
      renderWithProviders(
        <RoleGuard requiredPermissions={['view_dashboard']}>
          <TestComponent />
        </RoleGuard>,
        {
          preloadedState: createAuthenticatedState(mockUsers.driver),
        }
      );

      expect(screen.getByTestId('guarded-content')).toBeInTheDocument();
    });

    it('should not render content when user does not have required permissions', () => {
      renderWithProviders(
        <RoleGuard requiredPermissions={['manage_users']}>
          <TestComponent />
        </RoleGuard>,
        {
          preloadedState: createAuthenticatedState(mockUsers.driver),
        }
      );

      expect(screen.queryByTestId('guarded-content')).not.toBeInTheDocument();
    });

    it('should render fallback component when access denied and showFallback is true', () => {
      renderWithProviders(
        <RoleGuard 
          allowedRoles={['admin']} 
          showFallback={true}
          fallbackComponent={FallbackComponent}
        >
          <TestComponent />
        </RoleGuard>,
        {
          preloadedState: createAuthenticatedState(mockUsers.driver),
        }
      );

      expect(screen.queryByTestId('guarded-content')).not.toBeInTheDocument();
      expect(screen.getByTestId('fallback-content')).toBeInTheDocument();
    });

    it('should redirect to custom fallback path when access denied', () => {
      renderWithProviders(
        <RoleGuard 
          allowedRoles={['admin']} 
          fallbackPath="/custom-fallback"
        >
          <TestComponent />
        </RoleGuard>,
        {
          preloadedState: createAuthenticatedState(mockUsers.driver),
          initialEntries: ['/admin'],
        }
      );

      expect(screen.queryByTestId('guarded-content')).not.toBeInTheDocument();
    });
  });

  describe('role and permission combinations', () => {
    it('should require both role and permissions when both are specified', () => {
      // Admin has the role but let's test with a permission they should have
      renderWithProviders(
        <RoleGuard 
          allowedRoles={['admin']} 
          requiredPermissions={['manage_users']}
        >
          <TestComponent />
        </RoleGuard>,
        {
          preloadedState: createAuthenticatedState(mockUsers.admin),
        }
      );

      expect(screen.getByTestId('guarded-content')).toBeInTheDocument();
    });

    it('should deny access when user has role but not permissions', () => {
      // Manager has the role but not the admin-only permission
      renderWithProviders(
        <RoleGuard 
          allowedRoles={['manager']} 
          requiredPermissions={['manage_users']}
        >
          <TestComponent />
        </RoleGuard>,
        {
          preloadedState: createAuthenticatedState(mockUsers.manager),
        }
      );

      expect(screen.queryByTestId('guarded-content')).not.toBeInTheDocument();
    });

    it('should deny access when user has permissions but not role', () => {
      // Driver has view_dashboard permission but not admin role
      renderWithProviders(
        <RoleGuard 
          allowedRoles={['admin']} 
          requiredPermissions={['view_dashboard']}
        >
          <TestComponent />
        </RoleGuard>,
        {
          preloadedState: createAuthenticatedState(mockUsers.driver),
        }
      );

      expect(screen.queryByTestId('guarded-content')).not.toBeInTheDocument();
    });
  });

  describe('edge cases', () => {
    it('should handle empty allowedRoles array', () => {
      renderWithProviders(
        <RoleGuard allowedRoles={[]}>
          <TestComponent />
        </RoleGuard>,
        {
          preloadedState: createAuthenticatedState(mockUsers.admin),
        }
      );

      expect(screen.getByTestId('guarded-content')).toBeInTheDocument();
    });

    it('should handle empty requiredPermissions array', () => {
      renderWithProviders(
        <RoleGuard requiredPermissions={[]}>
          <TestComponent />
        </RoleGuard>,
        {
          preloadedState: createAuthenticatedState(mockUsers.driver),
        }
      );

      expect(screen.getByTestId('guarded-content')).toBeInTheDocument();
    });

    it('should handle null user data', () => {
      const stateWithNullUser = {
        auth: {
          user: null,
          token: 'some-token',
          refreshToken: 'some-refresh-token',
          isAuthenticated: true, // This is an edge case - authenticated but no user
          isLoading: false,
          error: null,
        },
      };

      renderWithProviders(
        <RoleGuard allowedRoles={['admin']}>
          <TestComponent />
        </RoleGuard>,
        {
          preloadedState: stateWithNullUser,
        }
      );

      expect(screen.queryByTestId('guarded-content')).not.toBeInTheDocument();
    });
  });
});
