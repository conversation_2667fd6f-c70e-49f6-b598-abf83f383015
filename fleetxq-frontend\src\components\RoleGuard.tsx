import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { usePermissions, type Permission } from '../hooks/usePermissions';

interface RoleGuardProps {
  children: React.ReactNode;
  allowedRoles?: ('admin' | 'manager' | 'driver')[];
  requiredPermissions?: Permission[];
  fallbackPath?: string;
  showFallback?: boolean;
  fallbackComponent?: React.ComponentType;
}

const RoleGuard: React.FC<RoleGuardProps> = ({
  children,
  allowedRoles = [],
  requiredPermissions = [],
  fallbackPath = '/dashboard',
  showFallback = false,
  fallbackComponent: FallbackComponent,
}) => {
  const location = useLocation();
  const { user, isAuthenticated } = useAuth();
  const { hasAnyPermission, canAccessRoute } = usePermissions();

  // If not authenticated, redirect to login
  if (!isAuthenticated) {
    return <Navigate to="/auth/login" state={{ from: location }} replace />;
  }

  // If no user data, show loading or redirect
  if (!user) {
    return <Navigate to="/auth/login" replace />;
  }

  // Check role-based access
  const hasRequiredRole = allowedRoles.length === 0 || allowedRoles.includes(user.role);

  // Check permission-based access
  const hasRequiredPermissions = requiredPermissions.length === 0 || 
    hasAnyPermission(requiredPermissions) || 
    canAccessRoute(requiredPermissions);

  // If user doesn't have required role or permissions
  if (!hasRequiredRole || !hasRequiredPermissions) {
    if (showFallback && FallbackComponent) {
      return <FallbackComponent />;
    }
    
    return <Navigate to={fallbackPath} replace />;
  }

  return <>{children}</>;
};

export default RoleGuard;
