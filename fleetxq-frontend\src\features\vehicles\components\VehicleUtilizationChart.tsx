import React, { useState, useEffect } from 'react';
import {
  AreaChart,
  Area,
  BarChart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ReferenceLine
} from 'recharts';
import {
  ClockIcon,
  TruckIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import type { Vehicle } from '../../../types';

interface VehicleUtilizationChartProps {
  vehicle: Vehicle;
  timeRange: '7d' | '30d' | '90d';
  className?: string;
}

interface UtilizationData {
  period: string;
  utilization: number;
  activeHours: number;
  idleHours: number;
  offlineHours: number;
  target: number;
  efficiency: number;
}

interface UtilizationMetrics {
  averageUtilization: number;
  peakUtilization: number;
  lowUtilization: number;
  totalActiveHours: number;
  utilizationTrend: number;
  efficiencyScore: number;
}

const VehicleUtilizationChart: React.FC<VehicleUtilizationChartProps> = ({
  vehicle,
  timeRange,
  className = ''
}) => {
  const [chartType, setChartType] = useState<'utilization' | 'hours' | 'efficiency'>('utilization');
  const [utilizationData, setUtilizationData] = useState<UtilizationData[]>([]);
  const [metrics, setMetrics] = useState<UtilizationMetrics>({
    averageUtilization: 0,
    peakUtilization: 0,
    lowUtilization: 0,
    totalActiveHours: 0,
    utilizationTrend: 0,
    efficiencyScore: 0
  });

  useEffect(() => {
    // Generate mock utilization data
    const generateUtilizationData = (): UtilizationData[] => {
      const data: UtilizationData[] = [];
      const now = new Date();
      let days: number;

      switch (timeRange) {
        case '7d':
          days = 7;
          break;
        case '30d':
          days = 30;
          break;
        case '90d':
          days = 90;
          break;
        default:
          days = 30;
      }

      for (let i = days - 1; i >= 0; i--) {
        const date = new Date(now);
        date.setDate(date.getDate() - i);
        
        // Generate realistic utilization patterns
        const dayOfWeek = date.getDay();
        const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
        
        // Base utilization varies by day type
        const baseUtilization = isWeekend ? 30 + Math.random() * 20 : 70 + Math.random() * 25;
        const utilization = Math.min(100, Math.max(0, baseUtilization));
        
        // Calculate hours based on utilization
        const totalHours = 24;
        const activeHours = (utilization / 100) * totalHours;
        const idleHours = Math.random() * 3; // Random idle time
        const offlineHours = totalHours - activeHours - idleHours;
        
        const target = 80; // Target utilization
        const efficiency = Math.min(100, utilization + Math.random() * 10 - 5);

        data.push({
          period: timeRange === '7d' ? 
            date.toLocaleDateString('en-US', { weekday: 'short' }) :
            timeRange === '30d' ?
            date.toLocaleDateString('en-US', { month: 'numeric', day: 'numeric' }) :
            date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
          utilization: Math.round(utilization * 10) / 10,
          activeHours: Math.round(activeHours * 10) / 10,
          idleHours: Math.round(idleHours * 10) / 10,
          offlineHours: Math.round(offlineHours * 10) / 10,
          target,
          efficiency: Math.round(efficiency * 10) / 10
        });
      }

      return data;
    };

    const data = generateUtilizationData();
    setUtilizationData(data);

    // Calculate metrics
    const avgUtilization = data.reduce((sum, d) => sum + d.utilization, 0) / data.length;
    const peakUtilization = Math.max(...data.map(d => d.utilization));
    const lowUtilization = Math.min(...data.map(d => d.utilization));
    const totalActiveHours = data.reduce((sum, d) => sum + d.activeHours, 0);
    
    // Calculate trend (simple linear regression slope)
    const n = data.length;
    const sumX = data.reduce((sum, _, i) => sum + i, 0);
    const sumY = data.reduce((sum, d) => sum + d.utilization, 0);
    const sumXY = data.reduce((sum, d, i) => sum + i * d.utilization, 0);
    const sumXX = data.reduce((sum, _, i) => sum + i * i, 0);
    const trend = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    
    const avgEfficiency = data.reduce((sum, d) => sum + d.efficiency, 0) / data.length;

    setMetrics({
      averageUtilization: Math.round(avgUtilization * 10) / 10,
      peakUtilization: Math.round(peakUtilization * 10) / 10,
      lowUtilization: Math.round(lowUtilization * 10) / 10,
      totalActiveHours: Math.round(totalActiveHours * 10) / 10,
      utilizationTrend: Math.round(trend * 100) / 100,
      efficiencyScore: Math.round(avgEfficiency * 10) / 10
    });
  }, [timeRange]);

  const getUtilizationStatus = (utilization: number) => {
    if (utilization >= 80) return { status: 'excellent', color: 'text-green-600', bg: 'bg-green-100' };
    if (utilization >= 60) return { status: 'good', color: 'text-blue-600', bg: 'bg-blue-100' };
    if (utilization >= 40) return { status: 'fair', color: 'text-yellow-600', bg: 'bg-yellow-100' };
    return { status: 'poor', color: 'text-red-600', bg: 'bg-red-100' };
  };

  const utilizationStatus = getUtilizationStatus(metrics.averageUtilization);

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900 mb-2">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {entry.name}: {entry.value}
              {entry.dataKey === 'utilization' || entry.dataKey === 'efficiency' ? '%' : 
               entry.dataKey.includes('Hours') ? 'h' : ''}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className={`p-2 rounded-lg ${utilizationStatus.bg}`}>
            <ClockIcon className={`h-6 w-6 ${utilizationStatus.color}`} />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Vehicle Utilization</h3>
            <p className="text-sm text-gray-600">
              Usage patterns for {vehicle.vehicleName || `${vehicle.year} ${vehicle.make} ${vehicle.model}`}
            </p>
          </div>
        </div>

        {/* Chart Type Selector */}
        <div className="flex items-center space-x-2">
          <select
            value={chartType}
            onChange={(e) => setChartType(e.target.value as any)}
            className="input-field text-sm py-1"
          >
            <option value="utilization">Utilization %</option>
            <option value="hours">Hours Breakdown</option>
            <option value="efficiency">Efficiency</option>
          </select>
        </div>
      </div>

      {/* Metrics Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className={`rounded-lg p-4 ${utilizationStatus.bg}`}>
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm font-medium ${utilizationStatus.color}`}>Avg Utilization</p>
              <p className={`text-2xl font-bold ${utilizationStatus.color}`}>
                {metrics.averageUtilization}%
              </p>
            </div>
            {metrics.averageUtilization >= 80 ? (
              <CheckCircleIcon className={`h-6 w-6 ${utilizationStatus.color}`} />
            ) : (
              <ExclamationTriangleIcon className={`h-6 w-6 ${utilizationStatus.color}`} />
            )}
          </div>
        </div>

        <div className="bg-blue-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-800">Peak Usage</p>
              <p className="text-2xl font-bold text-blue-900">{metrics.peakUtilization}%</p>
            </div>
            <TruckIcon className="h-6 w-6 text-blue-600" />
          </div>
        </div>

        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-800">Active Hours</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.totalActiveHours}h</p>
            </div>
            <ClockIcon className="h-6 w-6 text-gray-600" />
          </div>
        </div>

        <div className="bg-purple-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-purple-800">Efficiency</p>
              <p className="text-2xl font-bold text-purple-900">{metrics.efficiencyScore}%</p>
            </div>
            <ChartBarIcon className="h-6 w-6 text-purple-600" />
          </div>
        </div>
      </div>

      {/* Utilization Status Alert */}
      {metrics.averageUtilization < 60 && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <div className="flex items-start space-x-2">
            <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 mt-0.5" />
            <div>
              <h4 className="text-sm font-medium text-yellow-800">Low Utilization Alert</h4>
              <p className="text-sm text-yellow-700 mt-1">
                Vehicle utilization is below optimal levels. Consider reassigning routes or scheduling maintenance during low-usage periods.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Chart */}
      <div className="h-80 mb-6">
        <ResponsiveContainer width="100%" height="100%">
          {chartType === 'utilization' && (
            <AreaChart data={utilizationData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="period" tick={{ fontSize: 12 }} />
              <YAxis tick={{ fontSize: 12 }} domain={[0, 100]} />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <Area
                type="monotone"
                dataKey="utilization"
                stroke="#3B82F6"
                fill="#3B82F6"
                fillOpacity={0.2}
                name="Utilization %"
              />
              <ReferenceLine 
                y={80} 
                stroke="#10B981" 
                strokeDasharray="5 5"
                label={{ value: "Target 80%", position: "topRight" }}
              />
            </AreaChart>
          )}

          {chartType === 'hours' && (
            <BarChart data={utilizationData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="period" tick={{ fontSize: 12 }} />
              <YAxis tick={{ fontSize: 12 }} />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <Bar dataKey="activeHours" stackId="a" fill="#10B981" name="Active Hours" />
              <Bar dataKey="idleHours" stackId="a" fill="#F59E0B" name="Idle Hours" />
              <Bar dataKey="offlineHours" stackId="a" fill="#EF4444" name="Offline Hours" />
            </BarChart>
          )}

          {chartType === 'efficiency' && (
            <LineChart data={utilizationData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="period" tick={{ fontSize: 12 }} />
              <YAxis tick={{ fontSize: 12 }} domain={[0, 100]} />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <Line
                type="monotone"
                dataKey="efficiency"
                stroke="#8B5CF6"
                strokeWidth={2}
                name="Efficiency %"
              />
              <Line
                type="monotone"
                dataKey="utilization"
                stroke="#3B82F6"
                strokeWidth={2}
                strokeDasharray="5 5"
                name="Utilization %"
              />
            </LineChart>
          )}
        </ResponsiveContainer>
      </div>

      {/* Utilization Analysis */}
      <div className="border-t border-gray-200 pt-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="text-lg font-semibold text-gray-900 mb-3">Utilization Analysis</h4>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Average Utilization:</span>
                <span className={`font-medium ${utilizationStatus.color}`}>
                  {metrics.averageUtilization}%
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Peak Utilization:</span>
                <span className="font-medium text-gray-900">{metrics.peakUtilization}%</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Lowest Utilization:</span>
                <span className="font-medium text-gray-900">{metrics.lowUtilization}%</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Utilization Trend:</span>
                <span className={`font-medium ${
                  metrics.utilizationTrend > 0 ? 'text-green-600' : 
                  metrics.utilizationTrend < 0 ? 'text-red-600' : 'text-gray-600'
                }`}>
                  {metrics.utilizationTrend > 0 ? '+' : ''}{metrics.utilizationTrend.toFixed(2)}%/day
                </span>
              </div>
            </div>
          </div>

          <div>
            <h4 className="text-lg font-semibold text-gray-900 mb-3">Recommendations</h4>
            <ul className="text-sm text-gray-600 space-y-2">
              {metrics.averageUtilization < 60 && (
                <li>• Consider reassigning this vehicle to higher-demand routes</li>
              )}
              {metrics.averageUtilization > 90 && (
                <li>• Vehicle is heavily utilized - monitor for maintenance needs</li>
              )}
              {metrics.utilizationTrend < -1 && (
                <li>• Declining utilization trend - investigate potential issues</li>
              )}
              {metrics.peakUtilization - metrics.lowUtilization > 50 && (
                <li>• High utilization variance - consider load balancing</li>
              )}
              <li>• Schedule maintenance during low-utilization periods</li>
              <li>• Monitor driver efficiency during peak usage times</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Time-based Insights */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <h4 className="text-lg font-semibold text-gray-900 mb-3">Usage Patterns</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-gray-50 rounded-lg p-4">
            <h5 className="text-sm font-medium text-gray-700 mb-2">Best Performance Day</h5>
            <p className="text-lg font-bold text-green-600">
              {utilizationData.reduce((max, current) => 
                current.utilization > max.utilization ? current : max, 
                utilizationData[0] || { period: 'N/A', utilization: 0 }
              ).period}
            </p>
            <p className="text-sm text-gray-600">
              {utilizationData.reduce((max, current) => 
                current.utilization > max.utilization ? current : max, 
                utilizationData[0] || { utilization: 0 }
              ).utilization}% utilization
            </p>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <h5 className="text-sm font-medium text-gray-700 mb-2">Total Active Time</h5>
            <p className="text-lg font-bold text-blue-600">{metrics.totalActiveHours}h</p>
            <p className="text-sm text-gray-600">
              {Math.round((metrics.totalActiveHours / (utilizationData.length * 24)) * 100)}% of total time
            </p>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <h5 className="text-sm font-medium text-gray-700 mb-2">Efficiency Score</h5>
            <p className="text-lg font-bold text-purple-600">{metrics.efficiencyScore}%</p>
            <p className="text-sm text-gray-600">
              {metrics.efficiencyScore >= 85 ? 'Excellent' : 
               metrics.efficiencyScore >= 70 ? 'Good' : 
               metrics.efficiencyScore >= 55 ? 'Fair' : 'Needs Improvement'}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VehicleUtilizationChart;
