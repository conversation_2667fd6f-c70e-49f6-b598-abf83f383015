import React, { useState, useEffect } from 'react';
import {
  ExclamationTriangleIcon,
  XMarkIcon,
  CheckIcon,
  BellIcon,
  BellSlashIcon,
} from '@heroicons/react/24/outline';
import { useAppSelector, useAppDispatch } from '../../../store/hooks';
import { selectRecentActivity } from '../dashboardSlice';
import type { RecentActivity } from '../../../types';

export interface AlertPanelProps {
  maxAlerts?: number;
  showNotifications?: boolean;
  onAlertClick?: (alert: RecentActivity) => void;
  onAlertDismiss?: (alertId: string) => void;
  className?: string;
}

const AlertPanel: React.FC<AlertPanelProps> = ({
  maxAlerts = 5,
  showNotifications = true,
  onAlertClick,
  onAlertDismiss,
  className = '',
}) => {
  const dispatch = useAppDispatch();
  const recentActivity = useAppSelector(selectRecentActivity);
  const [dismissedAlerts, setDismissedAlerts] = useState<Set<string>>(new Set());
  const [soundEnabled, setSoundEnabled] = useState(true);

  // Filter for alerts only and exclude dismissed ones
  const alerts = recentActivity
    .filter(activity => activity.type === 'alert' && !dismissedAlerts.has(activity.id))
    .slice(0, maxAlerts);

  const getSeverityClasses = (severity?: string) => {
    switch (severity) {
      case 'critical':
        return 'border-red-500 bg-red-50 text-red-800';
      case 'high':
        return 'border-orange-500 bg-orange-50 text-orange-800';
      case 'medium':
        return 'border-yellow-500 bg-yellow-50 text-yellow-800';
      case 'low':
        return 'border-blue-500 bg-blue-50 text-blue-800';
      default:
        return 'border-gray-500 bg-gray-50 text-gray-800';
    }
  };

  const getSeverityIcon = (severity?: string) => {
    const iconClasses = 'h-5 w-5';
    switch (severity) {
      case 'critical':
      case 'high':
        return <ExclamationTriangleIcon className={`${iconClasses} text-red-500`} />;
      case 'medium':
        return <ExclamationTriangleIcon className={`${iconClasses} text-yellow-500`} />;
      case 'low':
        return <ExclamationTriangleIcon className={`${iconClasses} text-blue-500`} />;
      default:
        return <ExclamationTriangleIcon className={`${iconClasses} text-gray-500`} />;
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return date.toLocaleDateString();
  };

  const handleDismiss = (alertId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    setDismissedAlerts(prev => new Set([...prev, alertId]));
    onAlertDismiss?.(alertId);
  };

  const handleAlertClick = (alert: RecentActivity) => {
    onAlertClick?.(alert);
  };

  // Play notification sound for new critical alerts
  useEffect(() => {
    if (soundEnabled && showNotifications) {
      const criticalAlerts = alerts.filter(alert => alert.severity === 'critical');
      if (criticalAlerts.length > 0) {
        // In a real app, you would play a notification sound here
        console.log('Critical alert notification sound would play');
      }
    }
  }, [alerts, soundEnabled, showNotifications]);

  if (alerts.length === 0) {
    return (
      <div className={`card ${className}`}>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Active Alerts</h3>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setSoundEnabled(!soundEnabled)}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
              title={soundEnabled ? 'Disable notifications' : 'Enable notifications'}
            >
              {soundEnabled ? (
                <BellIcon className="h-5 w-5" />
              ) : (
                <BellSlashIcon className="h-5 w-5" />
              )}
            </button>
          </div>
        </div>
        <div className="text-center py-8">
          <CheckIcon className="mx-auto h-12 w-12 text-green-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No Active Alerts</h3>
          <p className="mt-1 text-sm text-gray-500">
            All systems are operating normally
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`card ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Active Alerts ({alerts.length})
        </h3>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setSoundEnabled(!soundEnabled)}
            className="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
            title={soundEnabled ? 'Disable notifications' : 'Enable notifications'}
          >
            {soundEnabled ? (
              <BellIcon className="h-5 w-5" />
            ) : (
              <BellSlashIcon className="h-5 w-5" />
            )}
          </button>
        </div>
      </div>

      <div className="space-y-3">
        {alerts.map((alert) => (
          <div
            key={alert.id}
            className={`
              relative p-3 rounded-lg border-l-4 cursor-pointer transition-all duration-200
              hover:shadow-sm ${getSeverityClasses(alert.severity)}
            `}
            onClick={() => handleAlertClick(alert)}
          >
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-3 flex-1">
                <div className="flex-shrink-0 mt-0.5">
                  {getSeverityIcon(alert.severity)}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium">{alert.message}</p>
                  <div className="mt-1 flex items-center space-x-2 text-xs opacity-75">
                    <span>{formatTimestamp(alert.timestamp)}</span>
                    {alert.vehicleId && (
                      <span>• Vehicle: {alert.vehicleId}</span>
                    )}
                    {alert.severity && (
                      <span className="capitalize">• {alert.severity}</span>
                    )}
                  </div>
                </div>
              </div>
              <button
                onClick={(e) => handleDismiss(alert.id, e)}
                className="flex-shrink-0 ml-2 p-1 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                title="Dismiss alert"
              >
                <XMarkIcon className="h-4 w-4" />
              </button>
            </div>
          </div>
        ))}
      </div>

      {recentActivity.filter(a => a.type === 'alert').length > maxAlerts && (
        <div className="mt-4 text-center">
          <button className="text-sm text-blue-600 hover:text-blue-800 font-medium">
            View All Alerts ({recentActivity.filter(a => a.type === 'alert').length})
          </button>
        </div>
      )}
    </div>
  );
};

export default AlertPanel;
