import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';

interface LogoutButtonProps {
  className?: string;
  variant?: 'button' | 'link' | 'icon';
  showConfirmation?: boolean;
  onLogoutStart?: () => void;
  onLogoutComplete?: () => void;
  onLogoutError?: (error: string) => void;
}

const LogoutButton: React.FC<LogoutButtonProps> = ({
  className = '',
  variant = 'button',
  showConfirmation = false,
  onLogoutStart,
  onLogoutComplete,
  onLogoutError,
}) => {
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const { logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = async () => {
    if (showConfirmation && !showConfirmDialog) {
      setShowConfirmDialog(true);
      return;
    }

    setIsLoggingOut(true);
    setShowConfirmDialog(false);
    onLogoutStart?.();

    try {
      const result = await logout();
      
      if (result.success) {
        onLogoutComplete?.();
        navigate('/auth/login', { replace: true });
      } else {
        onLogoutError?.(result.error || 'Logout failed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Logout failed';
      onLogoutError?.(errorMessage);
    } finally {
      setIsLoggingOut(false);
    }
  };

  const handleCancel = () => {
    setShowConfirmDialog(false);
  };

  const getButtonContent = () => {
    if (isLoggingOut) {
      return variant === 'icon' ? '⏳' : 'Signing out...';
    }
    
    switch (variant) {
      case 'icon':
        return '🚪';
      case 'link':
        return 'Sign out';
      default:
        return 'Sign out';
    }
  };

  const getButtonClassName = () => {
    const baseClasses = 'transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';
    
    switch (variant) {
      case 'icon':
        return `${baseClasses} p-2 rounded-full hover:bg-gray-100 focus:ring-gray-500 ${className}`;
      case 'link':
        return `${baseClasses} text-gray-600 hover:text-gray-900 focus:ring-gray-500 ${className}`;
      default:
        return `${baseClasses} px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed ${className}`;
    }
  };

  if (showConfirmDialog) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-sm mx-4">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Confirm Sign Out
          </h3>
          <p className="text-gray-600 mb-6">
            Are you sure you want to sign out?
          </p>
          <div className="flex space-x-3">
            <button
              onClick={handleLogout}
              disabled={isLoggingOut}
              className="flex-1 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50"
            >
              {isLoggingOut ? 'Signing out...' : 'Sign out'}
            </button>
            <button
              onClick={handleCancel}
              disabled={isLoggingOut}
              className="flex-1 px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 disabled:opacity-50"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <button
      onClick={handleLogout}
      disabled={isLoggingOut}
      className={getButtonClassName()}
      title={variant === 'icon' ? 'Sign out' : undefined}
    >
      {getButtonContent()}
    </button>
  );
};

export default LogoutButton;
