import React from 'react';
import { usePermissions, type Permission } from '../hooks/usePermissions';

interface PermissionGuardProps {
  children: React.ReactNode;
  permissions: Permission[];
  requireAll?: boolean;
  fallback?: React.ReactNode;
  fallbackComponent?: React.ComponentType;
}

/**
 * PermissionGuard component for conditional rendering based on user permissions
 * This is different from RoleGuard as it's meant for UI elements, not route protection
 */
const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  permissions,
  requireAll = false,
  fallback = null,
  fallbackComponent: FallbackComponent,
}) => {
  const { hasPermission, hasAnyPermission, hasAllPermissions } = usePermissions();

  // Handle single permission
  if (permissions.length === 1) {
    const hasAccess = hasPermission(permissions[0]);
    
    if (!hasAccess) {
      if (FallbackComponent) {
        return <FallbackComponent />;
      }
      return <>{fallback}</>;
    }
    
    return <>{children}</>;
  }

  // Handle multiple permissions
  const hasAccess = requireAll 
    ? hasAllPermissions(permissions)
    : hasAnyPermission(permissions);

  if (!hasAccess) {
    if (FallbackComponent) {
      return <FallbackComponent />;
    }
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

export default PermissionGuard;
