import { useCallback, useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import {
  loginUser,
  logoutUser,
  refreshToken,
  initializeAuth,
  clearError,
  selectAuth,
  selectUser,
  selectIsAuthenticated,
  selectIsLoading,
  selectAuthError
} from '../features/auth/authSlice';
import type { LoginCredentials } from '../types';

export const useAuth = () => {
  const dispatch = useAppDispatch();
  const auth = useAppSelector(selectAuth);
  const user = useAppSelector(selectUser);
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const isLoading = useAppSelector(selectIsLoading);
  const error = useAppSelector(selectAuthError);

  // Login function
  const login = useCallback(async (credentials: LoginCredentials) => {
    try {
      const result = await dispatch(loginUser(credentials)).unwrap();
      return { success: true, data: result };
    } catch (error) {
      return { success: false, error: error as string };
    }
  }, [dispatch]);

  // Logout function
  const logout = useCallback(async () => {
    try {
      await dispatch(logoutUser()).unwrap();
      return { success: true };
    } catch (error) {
      return { success: false, error: error as string };
    }
  }, [dispatch]);

  // Initialize authentication on app start
  const initialize = useCallback(async () => {
    try {
      const result = await dispatch(initializeAuth()).unwrap();
      return { success: true, data: result };
    } catch (error) {
      return { success: false, error: error as string };
    }
  }, [dispatch]);

  // Clear authentication error
  const clearAuthError = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  // Check if user has specific role
  const hasRole = useCallback((role: 'admin' | 'manager' | 'driver') => {
    return user?.role === role;
  }, [user]);

  // Check if user has any of the specified roles
  const hasAnyRole = useCallback((roles: ('admin' | 'manager' | 'driver')[]) => {
    return user ? roles.includes(user.role) : false;
  }, [user]);

  return {
    // State
    user,
    isAuthenticated,
    isLoading,
    error,
    auth,

    // Actions
    login,
    logout,
    initialize,
    clearAuthError,

    // Role checking utilities
    hasRole,
    hasAnyRole,
  };
};
