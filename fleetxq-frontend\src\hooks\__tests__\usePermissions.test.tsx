import { renderHook } from '@testing-library/react';
import { Provider } from 'react-redux';
import { describe, it, expect, beforeEach } from 'vitest';
import { usePermissions } from '../usePermissions';
import { createTestStore, mockUsers, createAuthenticatedState, createUnauthenticatedState } from '../../test/utils';

describe('usePermissions', () => {
  let store: ReturnType<typeof createTestStore>;

  beforeEach(() => {
    // Reset any mocks if needed
  });

  const renderUsePermissions = (preloadedState?: any) => {
    store = createTestStore(preloadedState);
    return renderHook(() => usePermissions(), {
      wrapper: ({ children }) => <Provider store={store}>{children}</Provider>,
    });
  };

  describe('when user is not authenticated', () => {
    it('should return empty permissions array', () => {
      const { result } = renderUsePermissions(createUnauthenticatedState());

      expect(result.current.permissions).toEqual([]);
      expect(result.current.hasPermission('view_dashboard')).toBe(false);
      expect(result.current.hasAnyPermission(['view_dashboard', 'view_vehicles'])).toBe(false);
      expect(result.current.hasAllPermissions(['view_dashboard'])).toBe(false);
    });
  });

  describe('when user is admin', () => {
    it('should return all permissions', () => {
      const { result } = renderUsePermissions(createAuthenticatedState(mockUsers.admin));

      const permissions = result.current.permissions;
      
      // Admin should have all permissions
      expect(permissions).toContain('view_dashboard');
      expect(permissions).toContain('view_vehicles');
      expect(permissions).toContain('manage_vehicles');
      expect(permissions).toContain('view_drivers');
      expect(permissions).toContain('manage_drivers');
      expect(permissions).toContain('view_telemetry');
      expect(permissions).toContain('view_alerts');
      expect(permissions).toContain('manage_alerts');
      expect(permissions).toContain('view_admin_panel');
      expect(permissions).toContain('manage_users');
      expect(permissions).toContain('manage_system_settings');
      expect(permissions).toContain('view_reports');
      expect(permissions).toContain('export_data');
    });

    it('should return true for all permission checks', () => {
      const { result } = renderUsePermissions(createAuthenticatedState(mockUsers.admin));

      expect(result.current.hasPermission('view_admin_panel')).toBe(true);
      expect(result.current.hasPermission('manage_users')).toBe(true);
      expect(result.current.hasAnyPermission(['view_admin_panel', 'manage_users'])).toBe(true);
      expect(result.current.hasAllPermissions(['view_dashboard', 'view_vehicles'])).toBe(true);
    });

    it('should allow access to all routes', () => {
      const { result } = renderUsePermissions(createAuthenticatedState(mockUsers.admin));

      expect(result.current.canAccessRoute(['view_admin_panel'])).toBe(true);
      expect(result.current.canAccessRoute(['manage_users'])).toBe(true);
      expect(result.current.canAccessRoute([])).toBe(true); // No specific permissions required
    });
  });

  describe('when user is manager', () => {
    it('should return manager permissions', () => {
      const { result } = renderUsePermissions(createAuthenticatedState(mockUsers.manager));

      const permissions = result.current.permissions;
      
      // Manager should have these permissions
      expect(permissions).toContain('view_dashboard');
      expect(permissions).toContain('view_vehicles');
      expect(permissions).toContain('manage_vehicles');
      expect(permissions).toContain('view_drivers');
      expect(permissions).toContain('manage_drivers');
      expect(permissions).toContain('view_telemetry');
      expect(permissions).toContain('view_alerts');
      expect(permissions).toContain('manage_alerts');
      expect(permissions).toContain('view_reports');
      expect(permissions).toContain('export_data');

      // Manager should NOT have these permissions
      expect(permissions).not.toContain('view_admin_panel');
      expect(permissions).not.toContain('manage_users');
      expect(permissions).not.toContain('manage_system_settings');
    });

    it('should return correct permission checks', () => {
      const { result } = renderUsePermissions(createAuthenticatedState(mockUsers.manager));

      expect(result.current.hasPermission('view_dashboard')).toBe(true);
      expect(result.current.hasPermission('manage_vehicles')).toBe(true);
      expect(result.current.hasPermission('view_admin_panel')).toBe(false);
      expect(result.current.hasPermission('manage_users')).toBe(false);

      expect(result.current.hasAnyPermission(['view_dashboard', 'manage_users'])).toBe(true);
      expect(result.current.hasAnyPermission(['view_admin_panel', 'manage_users'])).toBe(false);

      expect(result.current.hasAllPermissions(['view_dashboard', 'view_vehicles'])).toBe(true);
      expect(result.current.hasAllPermissions(['view_dashboard', 'manage_users'])).toBe(false);
    });
  });

  describe('when user is driver', () => {
    it('should return driver permissions', () => {
      const { result } = renderUsePermissions(createAuthenticatedState(mockUsers.driver));

      const permissions = result.current.permissions;
      
      // Driver should have these permissions
      expect(permissions).toContain('view_dashboard');
      expect(permissions).toContain('view_vehicles');
      expect(permissions).toContain('view_telemetry');
      expect(permissions).toContain('view_alerts');

      // Driver should NOT have these permissions
      expect(permissions).not.toContain('manage_vehicles');
      expect(permissions).not.toContain('view_drivers');
      expect(permissions).not.toContain('manage_drivers');
      expect(permissions).not.toContain('manage_alerts');
      expect(permissions).not.toContain('view_admin_panel');
      expect(permissions).not.toContain('manage_users');
      expect(permissions).not.toContain('manage_system_settings');
      expect(permissions).not.toContain('view_reports');
      expect(permissions).not.toContain('export_data');
    });

    it('should return correct permission checks', () => {
      const { result } = renderUsePermissions(createAuthenticatedState(mockUsers.driver));

      expect(result.current.hasPermission('view_dashboard')).toBe(true);
      expect(result.current.hasPermission('view_vehicles')).toBe(true);
      expect(result.current.hasPermission('manage_vehicles')).toBe(false);
      expect(result.current.hasPermission('view_admin_panel')).toBe(false);

      expect(result.current.hasAnyPermission(['view_dashboard', 'manage_vehicles'])).toBe(true);
      expect(result.current.hasAnyPermission(['manage_vehicles', 'view_admin_panel'])).toBe(false);

      expect(result.current.hasAllPermissions(['view_dashboard', 'view_vehicles'])).toBe(true);
      expect(result.current.hasAllPermissions(['view_dashboard', 'manage_vehicles'])).toBe(false);
    });
  });

  describe('getUIPermissions', () => {
    it('should return correct UI permissions for admin', () => {
      const { result } = renderUsePermissions(createAuthenticatedState(mockUsers.admin));

      const uiPermissions = result.current.getUIPermissions();

      expect(uiPermissions.canViewDashboard).toBe(true);
      expect(uiPermissions.canViewVehicles).toBe(true);
      expect(uiPermissions.canManageVehicles).toBe(true);
      expect(uiPermissions.canViewAdminPanel).toBe(true);
      expect(uiPermissions.canManageUsers).toBe(true);
      expect(uiPermissions.canExportData).toBe(true);
    });

    it('should return correct UI permissions for manager', () => {
      const { result } = renderUsePermissions(createAuthenticatedState(mockUsers.manager));

      const uiPermissions = result.current.getUIPermissions();

      expect(uiPermissions.canViewDashboard).toBe(true);
      expect(uiPermissions.canViewVehicles).toBe(true);
      expect(uiPermissions.canManageVehicles).toBe(true);
      expect(uiPermissions.canViewAdminPanel).toBe(false);
      expect(uiPermissions.canManageUsers).toBe(false);
      expect(uiPermissions.canExportData).toBe(true);
    });

    it('should return correct UI permissions for driver', () => {
      const { result } = renderUsePermissions(createAuthenticatedState(mockUsers.driver));

      const uiPermissions = result.current.getUIPermissions();

      expect(uiPermissions.canViewDashboard).toBe(true);
      expect(uiPermissions.canViewVehicles).toBe(true);
      expect(uiPermissions.canManageVehicles).toBe(false);
      expect(uiPermissions.canViewAdminPanel).toBe(false);
      expect(uiPermissions.canManageUsers).toBe(false);
      expect(uiPermissions.canExportData).toBe(false);
    });
  });

  describe('canAccessRoute', () => {
    it('should allow access when no permissions required', () => {
      const { result } = renderUsePermissions(createAuthenticatedState(mockUsers.driver));

      expect(result.current.canAccessRoute([])).toBe(true);
    });

    it('should deny access when not authenticated', () => {
      const { result } = renderUsePermissions(createUnauthenticatedState());

      expect(result.current.canAccessRoute(['view_dashboard'])).toBe(false);
    });
  });
});
