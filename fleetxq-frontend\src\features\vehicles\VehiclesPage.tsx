import React from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import VehiclesList from './VehiclesList';

const VehiclesPage = () => {
  const location = useLocation();

  // Check if we're on a sub-route (like vehicle details)
  const isSubRoute = location.pathname !== '/vehicles';

  if (isSubRoute) {
    // Render the sub-route component
    return <Outlet />;
  }

  // Render the main vehicles list
  return <VehiclesList />;
};

export default VehiclesPage;
