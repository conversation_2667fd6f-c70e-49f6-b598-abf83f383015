import React from 'react';
import {
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  ResponsiveContainer,
  Legend,
} from 'recharts';

export interface DriverPerformanceData {
  metric: string;
  score: number;
  maxScore: number;
  average: number;
}

export interface DriverPerformanceChartProps {
  data: DriverPerformanceData[];
  driverName?: string;
  height?: number;
  showLegend?: boolean;
  showAverage?: boolean;
}

const DriverPerformanceChart: React.FC<DriverPerformanceChartProps> = ({
  data,
  driverName = 'Driver',
  height = 300,
  showLegend = true,
  showAverage = true,
}) => {
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="text-sm font-medium text-gray-900 mb-2">
            {label}
          </p>
          {payload.map((entry: any, index: number) => (
            <div key={index} className="flex items-center space-x-2 text-sm">
              <div 
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: entry.color }}
              />
              <span className="text-gray-600">{entry.name}:</span>
              <span className="font-medium text-gray-900">
                {entry.value}/100
              </span>
            </div>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <ResponsiveContainer width="100%" height={height}>
      <RadarChart data={data} margin={{ top: 20, right: 30, bottom: 20, left: 30 }}>
        <PolarGrid stroke="#e5e7eb" />
        <PolarAngleAxis 
          dataKey="metric" 
          tick={{ fontSize: 12, fill: '#6b7280' }}
        />
        <PolarRadiusAxis
          angle={90}
          domain={[0, 100]}
          tick={{ fontSize: 10, fill: '#6b7280' }}
          tickCount={6}
        />
        <Radar
          name={driverName}
          dataKey="score"
          stroke="#3b82f6"
          fill="#3b82f6"
          fillOpacity={0.3}
          strokeWidth={2}
        />
        {showAverage && (
          <Radar
            name="Fleet Average"
            dataKey="average"
            stroke="#6b7280"
            fill="transparent"
            strokeWidth={2}
            strokeDasharray="5 5"
          />
        )}
        {showLegend && (
          <Legend 
            wrapperStyle={{ paddingTop: '20px' }}
            iconType="line"
          />
        )}
      </RadarChart>
    </ResponsiveContainer>
  );
};

export default DriverPerformanceChart;
