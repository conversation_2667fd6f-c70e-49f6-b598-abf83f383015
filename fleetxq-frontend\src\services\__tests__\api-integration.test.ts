/**
 * API Service Integration Tests
 *
 * This file contains basic integration tests to validate the API service layer
 * implementation according to Prompt 1.3 specifications.
 */

import {
  apiService,
  authService,
  vehicleService,
  driverService,
  telemetryService,
  alertService,
  dashboardService,
} from '../index';

// Mock environment variables
Object.defineProperty(import.meta, 'env', {
  value: {
    VITE_API_URL: 'http://localhost:5000/api',
    DEV: true,
  },
});

describe('API Service Layer Integration', () => {
  describe('Base API Service', () => {
    test('should be properly instantiated', () => {
      expect(apiService).toBeDefined();
      expect(typeof apiService.get).toBe('function');
      expect(typeof apiService.post).toBe('function');
      expect(typeof apiService.put).toBe('function');
      expect(typeof apiService.patch).toBe('function');
      expect(typeof apiService.delete).toBe('function');
      expect(typeof apiService.getPaginated).toBe('function');
    });

    test('should have utility methods', () => {
      expect(typeof apiService.setAuthToken).toBe('function');
      expect(typeof apiService.clearAuthToken).toBe('function');
      expect(typeof apiService.getBaseURL).toBe('function');
      expect(typeof apiService.updateConfig).toBe('function');
    });

    test('should return correct base URL', () => {
      expect(apiService.getBaseURL()).toBe('http://localhost:5000/api');
    });
  });

  describe('Auth Service', () => {
    test('should be properly instantiated', () => {
      expect(authService).toBeDefined();
      expect(typeof authService.login).toBe('function');
      expect(typeof authService.logout).toBe('function');
      expect(typeof authService.refreshToken).toBe('function');
      expect(typeof authService.getProfile).toBe('function');
      expect(typeof authService.updateProfile).toBe('function');
    });

    test('should have authentication utility methods', () => {
      expect(typeof authService.isAuthenticated).toBe('function');
      expect(typeof authService.getCurrentUser).toBe('function');
      expect(typeof authService.getAuthToken).toBe('function');
      expect(typeof authService.getRefreshToken).toBe('function');
    });
  });

  describe('Vehicle Service', () => {
    test('should be properly instantiated', () => {
      expect(vehicleService).toBeDefined();
      expect(typeof vehicleService.getVehicles).toBe('function');
      expect(typeof vehicleService.getVehicleById).toBe('function');
      expect(typeof vehicleService.createVehicle).toBe('function');
      expect(typeof vehicleService.updateVehicle).toBe('function');
      expect(typeof vehicleService.deleteVehicle).toBe('function');
    });

    test('should have vehicle-specific methods', () => {
      expect(typeof vehicleService.updateVehicleStatus).toBe('function');
      expect(typeof vehicleService.assignDriver).toBe('function');
      expect(typeof vehicleService.unassignDriver).toBe('function');
      expect(typeof vehicleService.getMaintenanceHistory).toBe('function');
      expect(typeof vehicleService.getCurrentLocation).toBe('function');
    });
  });

  describe('Driver Service', () => {
    test('should be properly instantiated', () => {
      expect(driverService).toBeDefined();
      expect(typeof driverService.getDrivers).toBe('function');
      expect(typeof driverService.getDriverById).toBe('function');
      expect(typeof driverService.createDriver).toBe('function');
      expect(typeof driverService.updateDriver).toBe('function');
      expect(typeof driverService.deleteDriver).toBe('function');
    });

    test('should have driver-specific methods', () => {
      expect(typeof driverService.updateDriverStatus).toBe('function');
      expect(typeof driverService.assignVehicle).toBe('function');
      expect(typeof driverService.unassignVehicle).toBe('function');
      expect(typeof driverService.getDrivingHistory).toBe('function');
      expect(typeof driverService.getDriverPerformance).toBe('function');
    });
  });

  describe('Telemetry Service', () => {
    test('should be properly instantiated', () => {
      expect(telemetryService).toBeDefined();
      expect(typeof telemetryService.getTelemetryData).toBe('function');
      expect(typeof telemetryService.getRealTimeTelemetry).toBe('function');
      expect(typeof telemetryService.getVehicleHistory).toBe('function');
      expect(typeof telemetryService.getTelemetrySummary).toBe('function');
    });

    test('should have telemetry-specific methods', () => {
      expect(typeof telemetryService.getFleetMetrics).toBe('function');
      expect(typeof telemetryService.getLocationTracking).toBe('function');
      expect(typeof telemetryService.getSpeedAnalysis).toBe('function');
      expect(typeof telemetryService.getFuelConsumption).toBe('function');
      expect(typeof telemetryService.getLiveVehiclePositions).toBe('function');
    });
  });

  describe('Alert Service', () => {
    test('should be properly instantiated', () => {
      expect(alertService).toBeDefined();
      expect(typeof alertService.getAlerts).toBe('function');
      expect(typeof alertService.getAlertById).toBe('function');
      expect(typeof alertService.getActiveAlerts).toBe('function');
      expect(typeof alertService.getCriticalAlerts).toBe('function');
    });

    test('should have alert management methods', () => {
      expect(typeof alertService.acknowledgeAlert).toBe('function');
      expect(typeof alertService.bulkAcknowledgeAlerts).toBe('function');
      expect(typeof alertService.resolveAlert).toBe('function');
      expect(typeof alertService.dismissAlert).toBe('function');
      expect(typeof alertService.getAlertStatistics).toBe('function');
    });
  });

  describe('Dashboard Service', () => {
    test('should be properly instantiated', () => {
      expect(dashboardService).toBeDefined();
      expect(typeof dashboardService.getDashboardSummary).toBe('function');
      expect(typeof dashboardService.getFleetMetrics).toBe('function');
      expect(typeof dashboardService.getAlertMetrics).toBe('function');
      expect(typeof dashboardService.getTelemetryMetrics).toBe('function');
    });

    test('should have dashboard-specific methods', () => {
      expect(typeof dashboardService.getPerformanceMetrics).toBe('function');
      expect(typeof dashboardService.getRecentActivity).toBe('function');
      expect(typeof dashboardService.getVehicleUtilization).toBe('function');
      expect(typeof dashboardService.getDriverPerformanceSummary).toBe(
        'function'
      );
      expect(typeof dashboardService.getRealTimeUpdates).toBe('function');
    });
  });

  describe('Service Integration', () => {
    test('all services should be accessible from index', () => {
      expect(apiService).toBeDefined();
      expect(authService).toBeDefined();
      expect(vehicleService).toBeDefined();
      expect(driverService).toBeDefined();
      expect(telemetryService).toBeDefined();
      expect(alertService).toBeDefined();
      expect(dashboardService).toBeDefined();
    });

    test('services should have consistent error handling', () => {
      // All service methods should be async and return promises
      const services = [
        authService,
        vehicleService,
        driverService,
        telemetryService,
        alertService,
        dashboardService,
      ];

      services.forEach(service => {
        const methods = Object.getOwnPropertyNames(
          Object.getPrototypeOf(service)
        ).filter(
          name => name !== 'constructor' && typeof service[name] === 'function'
        );

        methods.forEach(methodName => {
          const method = service[methodName];
          // Check if method returns a promise (async methods)
          if (
            methodName.startsWith('get') ||
            methodName.startsWith('create') ||
            methodName.startsWith('update') ||
            methodName.startsWith('delete')
          ) {
            expect(typeof method).toBe('function');
          }
        });
      });
    });
  });

  describe('TypeScript Type Safety', () => {
    test('should have proper TypeScript interfaces', () => {
      // This test ensures TypeScript compilation passes
      // If types are incorrect, the compilation would fail
      expect(true).toBe(true);
    });
  });
});

// Export for potential use in other test files
export {
  apiService,
  authService,
  vehicleService,
  driverService,
  telemetryService,
  alertService,
  dashboardService,
};
