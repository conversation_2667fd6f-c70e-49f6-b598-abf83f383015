import axios from 'axios';
import type {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  AxiosError,
} from 'axios';
import type {
  ApiResponse,
  PaginatedResponse,
  ApiConfig,
  ApiError,
  ApiErrorResponse,
  RefreshTokenRequest,
  RefreshTokenResponse,
  RetryConfig,
} from '../types';
import { setupAuthInterceptors } from './authInterceptor';

class ApiService {
  private api: AxiosInstance;
  private config: ApiConfig;
  private retryConfig: RetryConfig;
  private isRefreshing = false;
  private failedQueue: Array<{
    resolve: (value?: any) => void;
    reject: (reason?: any) => void;
  }> = [];

  constructor() {
    // API Configuration
    this.config = {
      baseURL: import.meta.env.VITE_API_URL || 'http://localhost:5000/api',
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000,
      headers: {
        'Content-Type': 'application/json',
      },
    };

    // Retry Configuration
    this.retryConfig = {
      retries: this.config.retryAttempts,
      retryDelay: this.config.retryDelay,
      retryCondition: (error: ApiError) => {
        return (
          !error.response ||
          error.response.status >= 500 ||
          error.response.status === 408 ||
          error.code === 'ECONNABORTED' ||
          error.code === 'NETWORK_ERROR'
        );
      },
      shouldResetTimeout: true,
    };

    // Create axios instance
    this.api = axios.create(this.config);

    this.setupInterceptors();

    // Setup authentication interceptors
    setupAuthInterceptors(this.api);
  }

  private setupInterceptors(): void {
    // Request interceptor
    this.api.interceptors.request.use(
      config => {
        // Add JWT token to requests
        const token = this.getStoredToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        // Add request timestamp for logging
        config.metadata = { startTime: new Date() };

        // Log request in development
        if (import.meta.env.DEV) {
          console.log(
            `🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`,
            {
              data: config.data,
              params: config.params,
            }
          );
        }

        return config;
      },
      error => {
        console.error('❌ Request Error:', error);
        return Promise.reject(this.handleError(error));
      }
    );

    // Response interceptor
    this.api.interceptors.response.use(
      response => {
        // Log response in development
        if (import.meta.env.DEV) {
          const duration =
            new Date().getTime() -
            response.config.metadata?.startTime?.getTime();
          console.log(
            `✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url}`,
            {
              status: response.status,
              duration: `${duration}ms`,
              data: response.data,
            }
          );
        }

        return response;
      },
      async (error: AxiosError) => {
        const originalRequest = error.config as any;

        // Handle 401 Unauthorized - Token refresh
        if (error.response?.status === 401 && !originalRequest._retry) {
          if (this.isRefreshing) {
            // If already refreshing, queue the request
            return new Promise((resolve, reject) => {
              this.failedQueue.push({ resolve, reject });
            })
              .then(token => {
                originalRequest.headers.Authorization = `Bearer ${token}`;
                return this.api(originalRequest);
              })
              .catch(err => {
                return Promise.reject(err);
              });
          }

          originalRequest._retry = true;
          this.isRefreshing = true;

          try {
            const newToken = await this.refreshToken();
            this.processQueue(null, newToken);
            originalRequest.headers.Authorization = `Bearer ${newToken}`;
            return this.api(originalRequest);
          } catch (refreshError) {
            this.processQueue(refreshError, null);
            this.handleAuthFailure();
            return Promise.reject(refreshError);
          } finally {
            this.isRefreshing = false;
          }
        }

        // Handle retry logic for network errors and server errors
        if (this.shouldRetry(error, originalRequest)) {
          return this.retryRequest(originalRequest, error);
        }

        return Promise.reject(this.handleError(error));
      }
    );
  }

  // Token Management Methods
  private getStoredToken(): string | null {
    return localStorage.getItem('authToken');
  }

  private getStoredRefreshToken(): string | null {
    return localStorage.getItem('refreshToken');
  }

  private setTokens(token: string, refreshToken: string): void {
    localStorage.setItem('authToken', token);
    localStorage.setItem('refreshToken', refreshToken);
  }

  private clearTokens(): void {
    localStorage.removeItem('authToken');
    localStorage.removeItem('refreshToken');
  }

  private async refreshToken(): Promise<string> {
    const refreshToken = this.getStoredRefreshToken();
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    try {
      const response = await axios.post<ApiResponse<RefreshTokenResponse>>(
        `${this.config.baseURL}/auth/refresh`,
        { refreshToken } as RefreshTokenRequest,
        { timeout: 10000 }
      );

      const { token, refreshToken: newRefreshToken } = response.data.data;
      this.setTokens(token, newRefreshToken);
      return token;
    } catch (error) {
      this.clearTokens();
      throw error;
    }
  }

  private processQueue(error: any, token: string | null): void {
    this.failedQueue.forEach(({ resolve, reject }) => {
      if (error) {
        reject(error);
      } else {
        resolve(token);
      }
    });

    this.failedQueue = [];
  }

  private handleAuthFailure(): void {
    this.clearTokens();
    // Redirect to login page
    if (typeof window !== 'undefined') {
      window.location.href = '/auth/login';
    }
  }

  // Retry Logic Methods
  private shouldRetry(error: AxiosError, config: any): boolean {
    if (!config || config.__retryCount >= this.retryConfig.retries) {
      return false;
    }

    if (this.retryConfig.retryCondition) {
      return this.retryConfig.retryCondition(error as ApiError);
    }

    return false;
  }

  private async retryRequest(config: any, error: AxiosError): Promise<any> {
    config.__retryCount = config.__retryCount || 0;
    config.__retryCount += 1;

    const delay =
      this.retryConfig.retryDelay * Math.pow(2, config.__retryCount - 1);

    console.warn(
      `🔄 Retrying request (${config.__retryCount}/${this.retryConfig.retries}) after ${delay}ms:`,
      {
        url: config.url,
        method: config.method,
        error: error.message,
      }
    );

    await new Promise(resolve => setTimeout(resolve, delay));

    if (this.retryConfig.shouldResetTimeout) {
      config.timeout = this.config.timeout;
    }

    return this.api(config);
  }

  // Error Handling
  private handleError(error: any): ApiError {
    const apiError: ApiError = {
      name: 'ApiError',
      message: 'An error occurred',
      isAxiosError: error.isAxiosError || false,
    };

    if (error.response) {
      // Server responded with error status
      apiError.response = {
        status: error.response.status,
        data: error.response.data,
      };
      apiError.message =
        error.response.data?.message || `HTTP ${error.response.status} Error`;
    } else if (error.request) {
      // Request was made but no response received
      apiError.request = error.request;
      apiError.message = 'Network error - no response received';
    } else {
      // Something else happened
      apiError.message = error.message || 'Unknown error occurred';
    }

    apiError.config = error.config;
    apiError.code = error.code;

    // Log error in development
    if (import.meta.env.DEV) {
      console.error('❌ API Error:', apiError);
    }

    return apiError;
  }

  // Generic HTTP Methods
  async get<T>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<ApiResponse<T>> = await this.api.get(
        url,
        config
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async post<T>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<ApiResponse<T>> = await this.api.post(
        url,
        data,
        config
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async put<T>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<ApiResponse<T>> = await this.api.put(
        url,
        data,
        config
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async patch<T>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<ApiResponse<T>> = await this.api.patch(
        url,
        data,
        config
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async delete<T>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<ApiResponse<T>> = await this.api.delete(
        url,
        config
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Paginated GET request
  async getPaginated<T>(
    url: string,
    page: number = 1,
    pageSize: number = 10,
    config?: AxiosRequestConfig
  ): Promise<PaginatedResponse<T>> {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
      });

      const response: AxiosResponse<PaginatedResponse<T>> = await this.api.get(
        `${url}?${params.toString()}`,
        config
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Utility Methods
  setAuthToken(token: string): void {
    localStorage.setItem('authToken', token);
  }

  clearAuthToken(): void {
    this.clearTokens();
  }

  getBaseURL(): string {
    return this.config.baseURL;
  }

  updateConfig(newConfig: Partial<ApiConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.api.defaults.baseURL = this.config.baseURL;
    this.api.defaults.timeout = this.config.timeout;
    this.api.defaults.headers = {
      ...this.api.defaults.headers,
      ...this.config.headers,
    };
  }
}

// Create and export a singleton instance
export const apiService = new ApiService();
export default apiService;
