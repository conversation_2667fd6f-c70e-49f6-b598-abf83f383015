import React, { useState, useEffect } from 'react';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import {
  ChartBarIcon,
  TruckIcon,
  FuelIcon,
  WrenchScrewdriverIcon,
  ClockIcon,
  CurrencyDollarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon
} from '@heroicons/react/24/outline';
import { useAppDispatch, useAppSelector } from '../../../store/hooks';
import {
  fetchVehicleAnalytics,
  selectVehicleAnalyticsById,
  selectVehiclesLoadingAnalytics,
  selectVehiclesAnalyticsError
} from '../vehiclesSlice';
import type { Vehicle, VehicleAnalytics } from '../../../types';
import LoadingSpinner from '../../../components/LoadingSpinner';

interface VehicleAnalyticsDashboardProps {
  vehicle: Vehicle;
  className?: string;
}

interface MetricCardProps {
  title: string;
  value: string | number;
  change?: number;
  changeLabel?: string;
  icon: React.ComponentType<{ className?: string }>;
  color: 'blue' | 'green' | 'yellow' | 'red' | 'purple';
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  change,
  changeLabel,
  icon: Icon,
  color
}) => {
  const colorClasses = {
    blue: 'bg-blue-100 text-blue-600',
    green: 'bg-green-100 text-green-600',
    yellow: 'bg-yellow-100 text-yellow-600',
    red: 'bg-red-100 text-red-600',
    purple: 'bg-purple-100 text-purple-600'
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900 mt-1">{value}</p>
          {change !== undefined && (
            <div className="flex items-center mt-2">
              {change >= 0 ? (
                <ArrowTrendingUpIcon className="h-4 w-4 text-green-600 mr-1" />
              ) : (
                <ArrowTrendingDownIcon className="h-4 w-4 text-red-600 mr-1" />
              )}
              <span className={`text-sm font-medium ${change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {Math.abs(change)}%
              </span>
              {changeLabel && (
                <span className="text-sm text-gray-500 ml-1">{changeLabel}</span>
              )}
            </div>
          )}
        </div>
        <div className={`p-3 rounded-lg ${colorClasses[color]}`}>
          <Icon className="h-6 w-6" />
        </div>
      </div>
    </div>
  );
};

const VehicleAnalyticsDashboard: React.FC<VehicleAnalyticsDashboardProps> = ({
  vehicle,
  className = ''
}) => {
  const dispatch = useAppDispatch();
  const analytics = useAppSelector(selectVehicleAnalyticsById(vehicle.id));
  const isLoading = useAppSelector(selectVehiclesLoadingAnalytics);
  const error = useAppSelector(selectVehiclesAnalyticsError);

  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d');

  // Mock analytics data for demonstration
  const mockAnalytics: VehicleAnalytics = {
    vehicleId: vehicle.id,
    utilizationData: {
      labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
      values: [85, 92, 78, 88, 95, 45, 32]
    },
    fuelConsumptionTrend: {
      labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
      values: [120, 115, 125, 110]
    },
    performanceMetrics: {
      totalDistance: 2450,
      averageSpeed: 45.2,
      fuelEfficiency: 8.5,
      utilizationRate: 78.5,
      maintenanceCosts: 1250
    },
    maintenanceHistory: [],
    assignmentHistory: []
  };

  useEffect(() => {
    dispatch(fetchVehicleAnalytics(vehicle.id));
  }, [dispatch, vehicle.id, timeRange]);

  // Mock data for charts
  const utilizationChartData = [
    { name: 'Mon', utilization: 85, target: 80 },
    { name: 'Tue', utilization: 92, target: 80 },
    { name: 'Wed', utilization: 78, target: 80 },
    { name: 'Thu', utilization: 88, target: 80 },
    { name: 'Fri', utilization: 95, target: 80 },
    { name: 'Sat', utilization: 45, target: 80 },
    { name: 'Sun', utilization: 32, target: 80 }
  ];

  const fuelConsumptionData = [
    { month: 'Jan', consumption: 120, efficiency: 8.2 },
    { month: 'Feb', consumption: 115, efficiency: 8.5 },
    { month: 'Mar', consumption: 125, efficiency: 8.1 },
    { month: 'Apr', consumption: 110, efficiency: 8.8 },
    { month: 'May', consumption: 118, efficiency: 8.4 },
    { month: 'Jun', consumption: 122, efficiency: 8.3 }
  ];

  const maintenanceCostData = [
    { category: 'Routine', cost: 450, color: '#3B82F6' },
    { category: 'Repairs', cost: 320, color: '#EF4444' },
    { category: 'Parts', cost: 280, color: '#10B981' },
    { category: 'Labor', cost: 200, color: '#F59E0B' }
  ];

  const performanceData = [
    { metric: 'Speed', current: 45.2, target: 50, unit: 'km/h' },
    { metric: 'Efficiency', current: 8.5, target: 9.0, unit: 'L/100km' },
    { metric: 'Uptime', current: 94.5, target: 95, unit: '%' },
    { metric: 'Utilization', current: 78.5, target: 80, unit: '%' }
  ];

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <p className="text-sm text-red-700">Failed to load analytics: {error}</p>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Vehicle Analytics</h3>
          <p className="text-sm text-gray-600">
            Performance insights and trends for {vehicle.vehicleName || `${vehicle.year} ${vehicle.make} ${vehicle.model}`}
          </p>
        </div>
        
        {/* Time Range Selector */}
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500">Time Range:</span>
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as any)}
            className="input-field text-sm py-1"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Total Distance"
          value={`${mockAnalytics.performanceMetrics.totalDistance.toLocaleString()} km`}
          change={12.5}
          changeLabel="vs last month"
          icon={TruckIcon}
          color="blue"
        />
        <MetricCard
          title="Fuel Efficiency"
          value={`${mockAnalytics.performanceMetrics.fuelEfficiency} L/100km`}
          change={-5.2}
          changeLabel="vs last month"
          icon={FuelIcon}
          color="green"
        />
        <MetricCard
          title="Utilization Rate"
          value={`${mockAnalytics.performanceMetrics.utilizationRate}%`}
          change={3.1}
          changeLabel="vs last month"
          icon={ClockIcon}
          color="purple"
        />
        <MetricCard
          title="Maintenance Costs"
          value={`$${mockAnalytics.performanceMetrics.maintenanceCosts.toLocaleString()}`}
          change={-8.7}
          changeLabel="vs last month"
          icon={CurrencyDollarIcon}
          color="red"
        />
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Utilization Chart */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-lg font-semibold text-gray-900">Daily Utilization</h4>
            <div className="flex items-center space-x-4 text-sm">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <span className="text-gray-600">Actual</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <span className="text-gray-600">Target</span>
              </div>
            </div>
          </div>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={utilizationChartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Area
                type="monotone"
                dataKey="utilization"
                stroke="#3B82F6"
                fill="#3B82F6"
                fillOpacity={0.1}
              />
              <Line
                type="monotone"
                dataKey="target"
                stroke="#EF4444"
                strokeDasharray="5 5"
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>

        {/* Fuel Consumption Trend */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h4 className="text-lg font-semibold text-gray-900 mb-4">Fuel Consumption & Efficiency</h4>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={fuelConsumptionData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis yAxisId="left" />
              <YAxis yAxisId="right" orientation="right" />
              <Tooltip />
              <Legend />
              <Bar yAxisId="left" dataKey="consumption" fill="#3B82F6" name="Consumption (L)" />
              <Line
                yAxisId="right"
                type="monotone"
                dataKey="efficiency"
                stroke="#10B981"
                strokeWidth={2}
                name="Efficiency (L/100km)"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Maintenance Cost Breakdown */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h4 className="text-lg font-semibold text-gray-900 mb-4">Maintenance Cost Breakdown</h4>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={maintenanceCostData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="cost"
              >
                {maintenanceCostData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip formatter={(value) => [`$${value}`, 'Cost']} />
            </PieChart>
          </ResponsiveContainer>
        </div>

        {/* Performance Metrics */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h4 className="text-lg font-semibold text-gray-900 mb-4">Performance vs Targets</h4>
          <div className="space-y-4">
            {performanceData.map((item, index) => {
              const percentage = (item.current / item.target) * 100;
              const isGood = percentage >= 95;
              
              return (
                <div key={index}>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700">{item.metric}</span>
                    <span className="text-sm text-gray-600">
                      {item.current} / {item.target} {item.unit}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${
                        isGood ? 'bg-green-500' : percentage >= 80 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${Math.min(percentage, 100)}%` }}
                    />
                  </div>
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>0</span>
                    <span className={`font-medium ${
                      isGood ? 'text-green-600' : percentage >= 80 ? 'text-yellow-600' : 'text-red-600'
                    }`}>
                      {percentage.toFixed(1)}%
                    </span>
                    <span>{item.target} {item.unit}</span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Summary Insights */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h4 className="text-lg font-semibold text-gray-900 mb-4">Key Insights</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h5 className="text-sm font-medium text-gray-700 mb-2">Performance Highlights</h5>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Utilization rate is 78.5%, slightly below target of 80%</li>
              <li>• Fuel efficiency improved by 5.2% compared to last month</li>
              <li>• Total distance increased by 12.5% indicating higher usage</li>
              <li>• Weekend utilization is significantly lower than weekdays</li>
            </ul>
          </div>
          <div>
            <h5 className="text-sm font-medium text-gray-700 mb-2">Recommendations</h5>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Consider weekend assignments to improve overall utilization</li>
              <li>• Schedule routine maintenance to maintain fuel efficiency gains</li>
              <li>• Monitor driver behavior during peak usage periods</li>
              <li>• Review maintenance costs for potential optimization</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VehicleAnalyticsDashboard;
