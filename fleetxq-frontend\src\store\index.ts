import { configureStore } from '@reduxjs/toolkit';
import authSlice from '../features/auth/authSlice';
import vehiclesSlice from '../features/vehicles/vehiclesSlice';
import driversSlice from '../features/drivers/driversSlice';
import telemetrySlice from '../features/telemetry/telemetrySlice';
import alertsSlice from '../features/alerts/alertsSlice';
import dashboardSlice from '../features/dashboard/dashboardSlice';

export const store = configureStore({
  reducer: {
    auth: authSlice,
    vehicles: vehiclesSlice,
    drivers: driversSlice,
    telemetry: telemetrySlice,
    alerts: alertsSlice,
    dashboard: dashboardSlice,
  },
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
