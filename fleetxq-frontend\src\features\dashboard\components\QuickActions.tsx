import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  PlusIcon,
  TruckIcon,
  UserPlusIcon,
  DocumentArrowDownIcon,
  Cog6ToothIcon,
  ChartBarIcon,
} from '@heroicons/react/24/outline';
import { usePermissions } from '../../../hooks/usePermissions';

export interface QuickAction {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  onClick: () => void;
  disabled?: boolean;
  permission?: string;
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
}

export interface QuickActionsProps {
  actions?: QuickAction[];
  className?: string;
  layout?: 'horizontal' | 'vertical' | 'grid';
  showDefaultActions?: boolean;
}

const QuickActions: React.FC<QuickActionsProps> = ({
  actions: customActions = [],
  className = '',
  layout = 'grid',
  showDefaultActions = true,
}) => {
  const navigate = useNavigate();
  const permissions = usePermissions();

  const defaultActions: QuickAction[] = [
    {
      id: 'add-vehicle',
      label: 'Add Vehicle',
      icon: TruckIcon,
      onClick: () => navigate('/vehicles/new'),
      permission: 'canCreateVehicle',
      variant: 'primary',
    },
    {
      id: 'add-driver',
      label: 'Add Driver',
      icon: UserPlusIcon,
      onClick: () => navigate('/drivers/new'),
      permission: 'canCreateDriver',
      variant: 'primary',
    },
    {
      id: 'view-reports',
      label: 'View Reports',
      icon: ChartBarIcon,
      onClick: () => navigate('/reports'),
      permission: 'canViewReports',
      variant: 'secondary',
    },
    {
      id: 'export-data',
      label: 'Export Data',
      icon: DocumentArrowDownIcon,
      onClick: () => {
        // In a real app, this would trigger an export
        console.log('Export data functionality would be implemented here');
      },
      permission: 'canExportData',
      variant: 'secondary',
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: Cog6ToothIcon,
      onClick: () => navigate('/settings'),
      permission: 'canAccessSettings',
      variant: 'secondary',
    },
  ];

  const allActions = showDefaultActions 
    ? [...defaultActions, ...customActions]
    : customActions;

  // Filter actions based on permissions
  const availableActions = allActions.filter(action => {
    if (!action.permission) return true;
    return permissions[action.permission as keyof typeof permissions];
  });

  const getVariantClasses = (variant: string = 'secondary') => {
    switch (variant) {
      case 'primary':
        return 'bg-blue-600 text-white hover:bg-blue-700 border-blue-600';
      case 'success':
        return 'bg-green-600 text-white hover:bg-green-700 border-green-600';
      case 'warning':
        return 'bg-yellow-600 text-white hover:bg-yellow-700 border-yellow-600';
      case 'danger':
        return 'bg-red-600 text-white hover:bg-red-700 border-red-600';
      default:
        return 'bg-white text-gray-700 hover:bg-gray-50 border-gray-300';
    }
  };

  const getLayoutClasses = () => {
    switch (layout) {
      case 'horizontal':
        return 'flex flex-wrap gap-3';
      case 'vertical':
        return 'flex flex-col space-y-3';
      case 'grid':
      default:
        return 'grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3';
    }
  };

  if (availableActions.length === 0) {
    return null;
  }

  return (
    <div className={`card ${className}`}>
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Quick Actions</h3>
        <p className="text-sm text-gray-600 mt-1">
          Common tasks and shortcuts
        </p>
      </div>

      <div className={getLayoutClasses()}>
        {availableActions.map((action) => (
          <button
            key={action.id}
            onClick={action.onClick}
            disabled={action.disabled}
            className={`
              flex flex-col items-center justify-center p-4 rounded-lg border
              transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed
              ${getVariantClasses(action.variant)}
            `}
            title={action.label}
          >
            <action.icon className="h-6 w-6 mb-2" />
            <span className="text-sm font-medium text-center">
              {action.label}
            </span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default QuickActions;
