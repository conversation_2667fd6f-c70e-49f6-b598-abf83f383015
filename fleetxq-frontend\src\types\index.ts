// Common types for FleetXQ application

export interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'manager' | 'driver';
  isAuthenticated: boolean;
}

export interface Vehicle {
  id: string;
  vehicleName: string;
  make: string;
  model: string;
  year: number;
  licensePlate: string;
  vin: string;
  vehicleType: string;
  brand?: string;
  color?: string;
  fuelType: string;
  fuelTankCapacity?: number;
  status: VehicleStatus;
  driverId?: string;
  currentMileage: number;
  lastMaintenanceDate?: string;
  nextMaintenanceDate?: string;
  currentLocation?: VehicleLocation;
  currentSpeed?: number;
  currentFuelLevel?: number;
  engineStatus?: 'on' | 'off';
  lastTelemetryUpdate?: string;
  isMoving: boolean;
  needsMaintenance: boolean;
  isAvailable: boolean;
  requiresImmediateAttention: boolean;
  createdAt: string;
  updatedAt: string;
}

export type VehicleStatus = 'active' | 'maintenance' | 'offline' | 'retired';

export type VehicleRealTimeStatus = 'online' | 'offline' | 'moving' | 'idle';

export interface VehicleLocation {
  latitude: number;
  longitude: number;
  address?: string;
  timestamp: string;
}

export interface VehicleMaintenanceAlert {
  id: string;
  vehicleId: string;
  type: 'scheduled' | 'overdue' | 'urgent';
  title: string;
  description: string;
  dueDate: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  estimatedCost?: number;
  createdAt: string;
}

export interface VehiclePerformanceMetrics {
  vehicleId: string;
  period: 'day' | 'week' | 'month';
  totalDistance: number;
  averageSpeed: number;
  fuelConsumption: number;
  fuelEfficiency: number;
  idleTime: number;
  utilizationRate: number;
  maintenanceCosts: number;
  lastUpdated: string;
}

export interface Driver {
  id: string;
  name: string;
  email: string;
  phone: string;
  licenseNumber: string;
  status: 'active' | 'inactive';
  vehicleId?: string;
}

export interface TelemetryData {
  vehicleId: string;
  timestamp: string;
  location: {
    latitude: number;
    longitude: number;
  };
  speed: number;
  fuelLevel: number;
  engineStatus: 'on' | 'off';
  temperature: number;
}

export interface Alert {
  id: string;
  vehicleId: string;
  type: 'maintenance' | 'speed' | 'fuel' | 'location' | 'engine';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: string;
  acknowledged: boolean;
}

export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  errors?: string[];
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// Redux State Types
export interface LoadingState {
  isLoading: boolean;
  error: string | null;
}

export interface PaginationState {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

// Auth Types
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken: string;
}

// Vehicle Types
export interface VehicleFormData {
  vehicleName: string;
  make: string;
  model: string;
  year: number;
  licensePlate: string;
  vin: string;
  vehicleType: string;
  brand?: string;
  color?: string;
  fuelType: string;
  fuelTankCapacity?: number;
  status: VehicleStatus;
  driverId?: string;
}

export interface VehicleFilters {
  status?: VehicleStatus;
  vehicleType?: string;
  fuelType?: string;
  search?: string;
  driverId?: string;
  needsMaintenance?: boolean;
  isAvailable?: boolean;
  realTimeStatus?: VehicleRealTimeStatus;
}

export interface VehicleSearchFilters extends VehicleFilters {
  sortBy?: 'vehicleName' | 'licensePlate' | 'status' | 'lastUpdate' | 'mileage';
  sortOrder?: 'asc' | 'desc';
  dateRange?: {
    startDate: string;
    endDate: string;
  };
}

export interface VehicleBulkOperation {
  vehicleIds: string[];
  operation: 'updateStatus' | 'assignDriver' | 'scheduleMaintenance' | 'export';
  data?: {
    status?: VehicleStatus;
    driverId?: string;
    maintenanceDate?: string;
    reason?: string;
  };
}

export interface VehicleAssignmentHistory {
  id: string;
  vehicleId: string;
  driverId: string;
  driverName: string;
  assignedAt: string;
  unassignedAt?: string;
  assignedBy: string;
  reason?: string;
}

export interface VehicleMaintenanceRecord {
  id: string;
  vehicleId: string;
  type: 'scheduled' | 'emergency' | 'preventive';
  description: string;
  performedAt: string;
  performedBy: string;
  cost: number;
  mileageAtMaintenance: number;
  nextMaintenanceDate?: string;
  notes?: string;
  parts?: string[];
}

export interface VehicleAnalytics {
  vehicleId: string;
  utilizationData: {
    labels: string[];
    values: number[];
  };
  fuelConsumptionTrend: {
    labels: string[];
    values: number[];
  };
  performanceMetrics: {
    totalDistance: number;
    averageSpeed: number;
    fuelEfficiency: number;
    utilizationRate: number;
    maintenanceCosts: number;
  };
  maintenanceHistory: VehicleMaintenanceRecord[];
  assignmentHistory: VehicleAssignmentHistory[];
}

// Driver Types
export interface DriverFormData {
  name: string;
  email: string;
  phone: string;
  licenseNumber: string;
  status: 'active' | 'inactive';
  vehicleId?: string;
}

export interface DriverFilters {
  status?: 'active' | 'inactive';
  search?: string;
  vehicleId?: string;
}

export interface DriverAssignment {
  driverId: string;
  vehicleId: string;
  assignedAt?: string;
  assignedBy?: string;
  reason?: string;
}

export interface VehicleAssignmentConflict {
  type: 'driver_already_assigned' | 'vehicle_unavailable' | 'driver_inactive' | 'vehicle_maintenance';
  message: string;
  conflictingVehicleId?: string;
  conflictingDriverId?: string;
  suggestions?: string[];
}

export interface VehicleRealTimeUpdate {
  vehicleId: string;
  updateType: 'location' | 'status' | 'telemetry' | 'alert';
  data: {
    location?: VehicleLocation;
    status?: VehicleRealTimeStatus;
    telemetry?: TelemetryData;
    alert?: VehicleMaintenanceAlert;
  };
  timestamp: string;
}

// Telemetry Types
export interface TelemetryQueryParams {
  vehicleId?: string;
  startDate?: string;
  endDate?: string;
  page?: number;
  pageSize?: number;
}

export interface TelemetryFilters {
  vehicleId?: string;
  startDate?: string;
  endDate?: string;
}

export interface TelemetryUpdate {
  vehicleId: string;
  data: TelemetryData;
}

// Alert Types
export interface AlertQueryParams {
  vehicleId?: string;
  type?: 'maintenance' | 'speed' | 'fuel' | 'location' | 'engine';
  severity?: 'low' | 'medium' | 'high' | 'critical';
  acknowledged?: boolean;
  startDate?: string;
  endDate?: string;
  page?: number;
  pageSize?: number;
}

export interface AlertFilters {
  vehicleId?: string;
  type?: 'maintenance' | 'speed' | 'fuel' | 'location' | 'engine';
  severity?: 'low' | 'medium' | 'high' | 'critical';
  acknowledged?: boolean;
  startDate?: string;
  endDate?: string;
}

export interface AlertAcknowledgment {
  alertId: string;
  userId: string;
  notes?: string;
}

export interface AlertResolution {
  alertId: string;
  userId: string;
  resolution: string;
  notes?: string;
}

export interface NotificationSettings {
  sound: boolean;
  desktop: boolean;
  email: boolean;
}

// Dashboard Types
export interface FleetMetrics {
  totalVehicles: number;
  activeVehicles: number;
  inactiveVehicles: number;
  maintenanceVehicles: number;
  totalDrivers: number;
  activeDrivers: number;
  unassignedDrivers: number;
}

export interface AlertMetrics {
  totalAlerts: number;
  criticalAlerts: number;
  highAlerts: number;
  mediumAlerts: number;
  lowAlerts: number;
  acknowledgedAlerts: number;
  unacknowledgedAlerts: number;
}

export interface TelemetryMetrics {
  averageSpeed: number;
  totalDistance: number;
  averageFuelLevel: number;
  activeVehiclesCount: number;
  lastUpdateTime: string;
}

export interface PerformanceMetrics {
  fuelEfficiency: number;
  maintenanceCosts: number;
  driverPerformance: number;
  vehicleUtilization: number;
  period: 'day' | 'week' | 'month';
}

export interface RecentActivity {
  id: string;
  type: 'alert' | 'maintenance' | 'driver_assignment' | 'vehicle_status';
  message: string;
  timestamp: string;
  severity?: 'low' | 'medium' | 'high' | 'critical';
  vehicleId?: string;
  driverId?: string;
}

export interface DashboardSummary {
  fleetMetrics: FleetMetrics;
  alertMetrics: AlertMetrics;
  telemetryMetrics: TelemetryMetrics;
  performanceMetrics: PerformanceMetrics;
  recentActivity: RecentActivity[];
  lastUpdated: string;
}

// Async Thunk Types
export interface AsyncThunkConfig {
  rejectValue: string;
}

// Common Action Payload Types
export interface ErrorPayload {
  message: string;
  code?: string;
  details?: any;
}

export interface SuccessPayload<T = any> {
  data: T;
  message?: string;
}

// Real-time Connection Types
export interface ConnectionStatus {
  isConnected: boolean;
  lastConnected?: string;
  reconnectAttempts?: number;
}

// Form Validation Types
export interface ValidationError {
  field: string;
  message: string;
}

export interface FormState<T> {
  data: T;
  errors: ValidationError[];
  isValid: boolean;
  isDirty: boolean;
  isSubmitting: boolean;
}

// API Configuration Types
export interface ApiConfig {
  baseURL: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
  headers: Record<string, string>;
}

export interface ApiErrorResponse {
  success: false;
  message: string;
  errors?: string[];
  code?: string;
  details?: any;
  timestamp?: string;
  path?: string;
}

export interface ApiError extends Error {
  response?: {
    status: number;
    data: ApiErrorResponse;
  };
  request?: any;
  config?: any;
  code?: string;
  isAxiosError: boolean;
}

// JWT Token Types
export interface TokenPayload {
  sub: string;
  email: string;
  role: string;
  exp: number;
  iat: number;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface RefreshTokenResponse {
  token: string;
  refreshToken: string;
  expiresIn: number;
}

// API Request/Response Interceptor Types
export interface RequestInterceptorConfig {
  onRequest?: (config: any) => any;
  onRequestError?: (error: any) => Promise<any>;
}

export interface ResponseInterceptorConfig {
  onResponse?: (response: any) => any;
  onResponseError?: (error: any) => Promise<any>;
}

// Retry Configuration
export interface RetryConfig {
  retries: number;
  retryDelay: number;
  retryCondition?: (error: ApiError) => boolean;
  shouldResetTimeout?: boolean;
}
