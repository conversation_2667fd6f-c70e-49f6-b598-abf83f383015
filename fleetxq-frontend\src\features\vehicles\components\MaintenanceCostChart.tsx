import React, { useState, useEffect } from 'react';
import {
  BarChart,
  Bar,
  <PERSON>Chart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ReferenceLine
} from 'recharts';
import {
  WrenchScrewdriverIcon,
  CurrencyDollarIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import type { Vehicle, VehicleMaintenanceRecord } from '../../../types';

interface MaintenanceCostChartProps {
  vehicle: Vehicle;
  timeRange: '30d' | '90d' | '6m' | '1y';
  className?: string;
}

interface MaintenanceCostData {
  period: string;
  routine: number;
  emergency: number;
  preventive: number;
  total: number;
  budget: number;
}

interface CostBreakdown {
  category: string;
  amount: number;
  percentage: number;
  color: string;
  trend: number;
}

const MaintenanceCostChart: React.FC<MaintenanceCostChartProps> = ({
  vehicle,
  timeRange,
  className = ''
}) => {
  const [chartView, setChartView] = useState<'timeline' | 'breakdown' | 'comparison'>('timeline');
  const [costData, setCostData] = useState<MaintenanceCostData[]>([]);
  const [costBreakdown, setCostBreakdown] = useState<CostBreakdown[]>([]);
  const [totalCost, setTotalCost] = useState(0);
  const [budgetVariance, setBudgetVariance] = useState(0);

  // Mock maintenance records for demonstration
  const mockMaintenanceRecords: VehicleMaintenanceRecord[] = [
    {
      id: '1',
      vehicleId: vehicle.id,
      type: 'routine',
      description: 'Oil change and filter replacement',
      performedAt: '2024-01-15T10:00:00Z',
      performedBy: 'Service Center A',
      cost: 120,
      mileageAtMaintenance: 15000,
      notes: 'Regular maintenance',
      parts: ['Oil filter', 'Engine oil']
    },
    {
      id: '2',
      vehicleId: vehicle.id,
      type: 'emergency',
      description: 'Brake pad replacement',
      performedAt: '2024-01-22T14:30:00Z',
      performedBy: 'Emergency Service',
      cost: 350,
      mileageAtMaintenance: 15200,
      notes: 'Urgent brake repair',
      parts: ['Brake pads', 'Brake fluid']
    },
    {
      id: '3',
      vehicleId: vehicle.id,
      type: 'preventive',
      description: 'Tire rotation and alignment',
      performedAt: '2024-02-01T09:00:00Z',
      performedBy: 'Service Center B',
      cost: 85,
      mileageAtMaintenance: 15500,
      notes: 'Preventive maintenance',
      parts: ['Alignment service']
    }
  ];

  useEffect(() => {
    // Generate mock data based on time range
    const generateCostData = (): MaintenanceCostData[] => {
      const data: MaintenanceCostData[] = [];
      const now = new Date();
      let periods: number;
      let periodType: 'day' | 'week' | 'month';

      switch (timeRange) {
        case '30d':
          periods = 30;
          periodType = 'day';
          break;
        case '90d':
          periods = 13;
          periodType = 'week';
          break;
        case '6m':
          periods = 6;
          periodType = 'month';
          break;
        case '1y':
          periods = 12;
          periodType = 'month';
          break;
        default:
          periods = 6;
          periodType = 'month';
      }

      for (let i = periods - 1; i >= 0; i--) {
        const date = new Date(now);
        
        if (periodType === 'day') {
          date.setDate(date.getDate() - i);
        } else if (periodType === 'week') {
          date.setDate(date.getDate() - (i * 7));
        } else {
          date.setMonth(date.getMonth() - i);
        }

        const routine = Math.random() * 200 + 50;
        const emergency = Math.random() * 300;
        const preventive = Math.random() * 150 + 25;
        const total = routine + emergency + preventive;
        const budget = 400; // Monthly budget

        data.push({
          period: periodType === 'day' ? 
            date.toLocaleDateString('en-US', { month: 'numeric', day: 'numeric' }) :
            periodType === 'week' ?
            `Week ${Math.ceil((now.getTime() - date.getTime()) / (7 * 24 * 60 * 60 * 1000)) + 1}` :
            date.toLocaleDateString('en-US', { month: 'short', year: '2-digit' }),
          routine: Math.round(routine),
          emergency: Math.round(emergency),
          preventive: Math.round(preventive),
          total: Math.round(total),
          budget
        });
      }

      return data;
    };

    const data = generateCostData();
    setCostData(data);

    // Calculate totals and breakdown
    const totalRoutine = data.reduce((sum, d) => sum + d.routine, 0);
    const totalEmergency = data.reduce((sum, d) => sum + d.emergency, 0);
    const totalPreventive = data.reduce((sum, d) => sum + d.preventive, 0);
    const grandTotal = totalRoutine + totalEmergency + totalPreventive;
    const totalBudget = data.reduce((sum, d) => sum + d.budget, 0);

    setTotalCost(grandTotal);
    setBudgetVariance(((grandTotal - totalBudget) / totalBudget) * 100);

    // Cost breakdown
    const breakdown: CostBreakdown[] = [
      {
        category: 'Routine Maintenance',
        amount: totalRoutine,
        percentage: (totalRoutine / grandTotal) * 100,
        color: '#3B82F6',
        trend: Math.random() * 20 - 10 // Random trend for demo
      },
      {
        category: 'Emergency Repairs',
        amount: totalEmergency,
        percentage: (totalEmergency / grandTotal) * 100,
        color: '#EF4444',
        trend: Math.random() * 20 - 10
      },
      {
        category: 'Preventive Care',
        amount: totalPreventive,
        percentage: (totalPreventive / grandTotal) * 100,
        color: '#10B981',
        trend: Math.random() * 20 - 10
      }
    ];

    setCostBreakdown(breakdown);
  }, [timeRange]);

  const formatCurrency = (value: number) => `$${value.toLocaleString()}`;

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900 mb-2">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {entry.name}: {formatCurrency(entry.value)}
            </p>
          ))}
          {payload[0] && (
            <p className="text-sm text-gray-600 mt-1 pt-1 border-t">
              Total: {formatCurrency(payload.reduce((sum: number, p: any) => sum + p.value, 0))}
            </p>
          )}
        </div>
      );
    }
    return null;
  };

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-orange-100 rounded-lg">
            <WrenchScrewdriverIcon className="h-6 w-6 text-orange-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Maintenance Costs</h3>
            <p className="text-sm text-gray-600">
              Cost analysis for {vehicle.vehicleName || `${vehicle.year} ${vehicle.make} ${vehicle.model}`}
            </p>
          </div>
        </div>

        {/* View Selector */}
        <div className="flex items-center space-x-2">
          <select
            value={chartView}
            onChange={(e) => setChartView(e.target.value as any)}
            className="input-field text-sm py-1"
          >
            <option value="timeline">Timeline</option>
            <option value="breakdown">Breakdown</option>
            <option value="comparison">Budget Comparison</option>
          </select>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-blue-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-800">Total Cost</p>
              <p className="text-2xl font-bold text-blue-900">{formatCurrency(totalCost)}</p>
            </div>
            <CurrencyDollarIcon className="h-8 w-8 text-blue-600" />
          </div>
        </div>

        <div className={`rounded-lg p-4 ${budgetVariance > 0 ? 'bg-red-50' : 'bg-green-50'}`}>
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm font-medium ${budgetVariance > 0 ? 'text-red-800' : 'text-green-800'}`}>
                Budget Variance
              </p>
              <p className={`text-2xl font-bold ${budgetVariance > 0 ? 'text-red-900' : 'text-green-900'}`}>
                {budgetVariance > 0 ? '+' : ''}{budgetVariance.toFixed(1)}%
              </p>
            </div>
            {budgetVariance > 0 ? (
              <TrendingUpIcon className="h-8 w-8 text-red-600" />
            ) : (
              <TrendingDownIcon className="h-8 w-8 text-green-600" />
            )}
          </div>
        </div>

        <div className="bg-yellow-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-yellow-800">Emergency %</p>
              <p className="text-2xl font-bold text-yellow-900">
                {((costBreakdown.find(c => c.category === 'Emergency Repairs')?.percentage || 0)).toFixed(1)}%
              </p>
            </div>
            <ExclamationTriangleIcon className="h-8 w-8 text-yellow-600" />
          </div>
        </div>

        <div className="bg-green-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-green-800">Avg per Period</p>
              <p className="text-2xl font-bold text-green-900">
                {formatCurrency(Math.round(totalCost / costData.length))}
              </p>
            </div>
            <WrenchScrewdriverIcon className="h-8 w-8 text-green-600" />
          </div>
        </div>
      </div>

      {/* Charts */}
      <div className="h-80 mb-6">
        <ResponsiveContainer width="100%" height="100%">
          {chartView === 'timeline' && (
            <BarChart data={costData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="period" tick={{ fontSize: 12 }} />
              <YAxis tick={{ fontSize: 12 }} />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <Bar dataKey="routine" stackId="a" fill="#3B82F6" name="Routine" />
              <Bar dataKey="emergency" stackId="a" fill="#EF4444" name="Emergency" />
              <Bar dataKey="preventive" stackId="a" fill="#10B981" name="Preventive" />
            </BarChart>
          )}

          {chartView === 'breakdown' && (
            <PieChart>
              <Pie
                data={costBreakdown}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ category, percentage }) => `${category}: ${percentage.toFixed(1)}%`}
                outerRadius={120}
                fill="#8884d8"
                dataKey="amount"
              >
                {costBreakdown.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip formatter={(value) => [formatCurrency(value as number), 'Cost']} />
            </PieChart>
          )}

          {chartView === 'comparison' && (
            <LineChart data={costData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="period" tick={{ fontSize: 12 }} />
              <YAxis tick={{ fontSize: 12 }} />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <Line
                type="monotone"
                dataKey="total"
                stroke="#3B82F6"
                strokeWidth={2}
                name="Actual Cost"
              />
              <Line
                type="monotone"
                dataKey="budget"
                stroke="#EF4444"
                strokeDasharray="5 5"
                strokeWidth={2}
                name="Budget"
              />
            </LineChart>
          )}
        </ResponsiveContainer>
      </div>

      {/* Cost Breakdown Table */}
      <div className="border-t border-gray-200 pt-6">
        <h4 className="text-lg font-semibold text-gray-900 mb-4">Cost Breakdown</h4>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Category
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Percentage
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Trend
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {costBreakdown.map((item, index) => (
                <tr key={index}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div
                        className="w-4 h-4 rounded-full mr-3"
                        style={{ backgroundColor: item.color }}
                      />
                      <span className="text-sm font-medium text-gray-900">{item.category}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatCurrency(item.amount)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {item.percentage.toFixed(1)}%
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className={`flex items-center text-sm ${
                      item.trend >= 0 ? 'text-red-600' : 'text-green-600'
                    }`}>
                      {item.trend >= 0 ? (
                        <TrendingUpIcon className="h-4 w-4 mr-1" />
                      ) : (
                        <TrendingDownIcon className="h-4 w-4 mr-1" />
                      )}
                      {Math.abs(item.trend).toFixed(1)}%
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Insights */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <h4 className="text-lg font-semibold text-gray-900 mb-3">Cost Insights</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h5 className="text-sm font-medium text-gray-700 mb-2">Key Observations</h5>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Emergency repairs account for {costBreakdown.find(c => c.category === 'Emergency Repairs')?.percentage.toFixed(1)}% of total costs</li>
              <li>• {budgetVariance > 0 ? 'Over' : 'Under'} budget by {Math.abs(budgetVariance).toFixed(1)}%</li>
              <li>• Average cost per period: {formatCurrency(Math.round(totalCost / costData.length))}</li>
              <li>• Preventive maintenance represents {costBreakdown.find(c => c.category === 'Preventive Care')?.percentage.toFixed(1)}% of spending</li>
            </ul>
          </div>
          <div>
            <h5 className="text-sm font-medium text-gray-700 mb-2">Recommendations</h5>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• {costBreakdown.find(c => c.category === 'Emergency Repairs')?.percentage > 30 ? 'Increase preventive maintenance to reduce emergency repairs' : 'Good balance between preventive and emergency maintenance'}</li>
              <li>• {budgetVariance > 10 ? 'Review budget allocation or reduce costs' : 'Costs are within acceptable range'}</li>
              <li>• Consider bulk purchasing for routine maintenance items</li>
              <li>• Schedule regular maintenance to prevent costly emergency repairs</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MaintenanceCostChart;
