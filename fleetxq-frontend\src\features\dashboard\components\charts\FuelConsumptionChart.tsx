import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
} from 'recharts';

export interface FuelConsumptionData {
  vehicleType: string;
  consumption: number;
  efficiency: number;
  cost: number;
  distance: number;
}

export interface FuelConsumptionChartProps {
  data: FuelConsumptionData[];
  height?: number;
  showLegend?: boolean;
  showGrid?: boolean;
  metric?: 'consumption' | 'efficiency' | 'cost';
}

const FuelConsumptionChart: React.FC<FuelConsumptionChartProps> = ({
  data,
  height = 300,
  showLegend = true,
  showGrid = true,
  metric = 'consumption',
}) => {
  const getMetricConfig = () => {
    switch (metric) {
      case 'efficiency':
        return {
          dataKey: 'efficiency',
          label: 'Fuel Efficiency (MPG)',
          color: '#10b981',
          unit: 'MPG',
        };
      case 'cost':
        return {
          dataKey: 'cost',
          label: 'Fuel Cost ($)',
          color: '#f59e0b',
          unit: '$',
        };
      default:
        return {
          dataKey: 'consumption',
          label: 'Fuel Consumption (Gallons)',
          color: '#3b82f6',
          unit: 'gal',
        };
    }
  };

  const metricConfig = getMetricConfig();

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="text-sm font-medium text-gray-900 mb-2">
            {label}
          </p>
          <div className="space-y-1 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Consumption:</span>
              <span className="font-medium">{data.consumption} gal</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Efficiency:</span>
              <span className="font-medium">{data.efficiency} MPG</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Cost:</span>
              <span className="font-medium">${data.cost}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Distance:</span>
              <span className="font-medium">{data.distance} miles</span>
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  const formatYAxisLabel = (value: number) => {
    if (metric === 'cost') {
      return `$${value}`;
    }
    return value.toString();
  };

  return (
    <ResponsiveContainer width="100%" height={height}>
      <BarChart
        data={data}
        margin={{
          top: 5,
          right: 30,
          left: 20,
          bottom: 5,
        }}
      >
        {showGrid && (
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        )}
        <XAxis 
          dataKey="vehicleType" 
          stroke="#6b7280"
          fontSize={12}
          angle={-45}
          textAnchor="end"
          height={80}
        />
        <YAxis 
          stroke="#6b7280"
          fontSize={12}
          tickFormatter={formatYAxisLabel}
          label={{ 
            value: metricConfig.label, 
            angle: -90, 
            position: 'insideLeft' 
          }}
        />
        <Tooltip content={<CustomTooltip />} />
        {showLegend && (
          <Legend 
            wrapperStyle={{ paddingTop: '20px' }}
          />
        )}
        <Bar
          dataKey={metricConfig.dataKey}
          fill={metricConfig.color}
          name={metricConfig.label}
          radius={[4, 4, 0, 0]}
        />
      </BarChart>
    </ResponsiveContainer>
  );
};

export default FuelConsumptionChart;
