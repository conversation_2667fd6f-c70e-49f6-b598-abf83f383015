import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import type {
  Vehicle,
  PaginatedResponse,
  VehicleFormData,
  VehicleSearchFilters,
  VehicleBulkOperation,
  VehicleAnalytics,
  VehicleRealTimeUpdate,
  VehicleAssignmentConflict,
  VehicleMaintenanceAlert,
  VehicleAssignmentHistory,
  VehicleMaintenanceRecord,
  VehicleStatus,
  VehicleRealTimeStatus
} from '../../types';
import { apiService } from '../../services/api';

// Vehicles state interface
interface VehiclesState {
  vehicles: Vehicle[];
  currentVehicle: Vehicle | null;
  selectedVehicles: string[];
  vehicleAnalytics: Record<string, VehicleAnalytics>;
  maintenanceAlerts: VehicleMaintenanceAlert[];
  assignmentHistory: VehicleAssignmentHistory[];
  maintenanceHistory: VehicleMaintenanceRecord[];
  realTimeUpdates: VehicleRealTimeUpdate[];
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  isBulkOperating: boolean;
  isLoadingAnalytics: boolean;
  isLoadingMaintenanceAlerts: boolean;
  error: string | null;
  bulkOperationError: string | null;
  analyticsError: string | null;
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
  filters: VehicleSearchFilters;
  assignmentConflicts: VehicleAssignmentConflict[];
}

const initialState: VehiclesState = {
  vehicles: [],
  currentVehicle: null,
  selectedVehicles: [],
  vehicleAnalytics: {},
  maintenanceAlerts: [],
  assignmentHistory: [],
  maintenanceHistory: [],
  realTimeUpdates: [],
  isLoading: false,
  isCreating: false,
  isUpdating: false,
  isDeleting: false,
  isBulkOperating: false,
  isLoadingAnalytics: false,
  isLoadingMaintenanceAlerts: false,
  error: null,
  bulkOperationError: null,
  analyticsError: null,
  pagination: {
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0,
  },
  filters: {},
  assignmentConflicts: [],
};

// Async thunks
export const fetchVehicles = createAsyncThunk(
  'vehicles/fetchVehicles',
  async (
    params: {
      page?: number;
      pageSize?: number;
      status?: string;
      search?: string;
    } = {},
    { rejectWithValue }
  ) => {
    try {
      const { page = 1, pageSize = 10, status, search } = params;
      let url = `/vehicles?page=${page}&pageSize=${pageSize}`;

      if (status) url += `&status=${status}`;
      if (search) url += `&search=${encodeURIComponent(search)}`;

      const response = await apiService.get<PaginatedResponse<Vehicle>>(url);
      return response.data;
    } catch (error: any) {
      const message =
        error.response?.data?.message || 'Failed to fetch vehicles';
      return rejectWithValue(message);
    }
  }
);

export const fetchVehicleById = createAsyncThunk(
  'vehicles/fetchVehicleById',
  async (vehicleId: string, { rejectWithValue }) => {
    try {
      const response = await apiService.get<Vehicle>(`/vehicles/${vehicleId}`);
      return response.data;
    } catch (error: any) {
      const message =
        error.response?.data?.message || 'Failed to fetch vehicle';
      return rejectWithValue(message);
    }
  }
);

export const createVehicle = createAsyncThunk(
  'vehicles/createVehicle',
  async (vehicleData: VehicleFormData, { rejectWithValue }) => {
    try {
      const response = await apiService.post<Vehicle>('/vehicles', vehicleData);
      return response.data;
    } catch (error: any) {
      const message =
        error.response?.data?.message || 'Failed to create vehicle';
      return rejectWithValue(message);
    }
  }
);

export const updateVehicle = createAsyncThunk(
  'vehicles/updateVehicle',
  async (
    { id, data }: { id: string; data: Partial<VehicleFormData> },
    { rejectWithValue }
  ) => {
    try {
      const response = await apiService.put<Vehicle>(`/vehicles/${id}`, data);
      return response.data;
    } catch (error: any) {
      const message =
        error.response?.data?.message || 'Failed to update vehicle';
      return rejectWithValue(message);
    }
  }
);

export const deleteVehicle = createAsyncThunk(
  'vehicles/deleteVehicle',
  async (vehicleId: string, { rejectWithValue }) => {
    try {
      await apiService.delete(`/vehicles/${vehicleId}`);
      return vehicleId;
    } catch (error: any) {
      const message =
        error.response?.data?.message || 'Failed to delete vehicle';
      return rejectWithValue(message);
    }
  }
);

// New async thunks for enhanced features
export const fetchVehicleAnalytics = createAsyncThunk(
  'vehicles/fetchVehicleAnalytics',
  async (vehicleId: string, { rejectWithValue }) => {
    try {
      const response = await apiService.get<VehicleAnalytics>(`/vehicles/${vehicleId}/analytics`);
      return { vehicleId, analytics: response.data };
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch vehicle analytics';
      return rejectWithValue(message);
    }
  }
);

export const fetchMaintenanceAlerts = createAsyncThunk(
  'vehicles/fetchMaintenanceAlerts',
  async (vehicleId?: string, { rejectWithValue }) => {
    try {
      const url = vehicleId ? `/vehicles/${vehicleId}/maintenance-alerts` : '/vehicles/maintenance-alerts';
      const response = await apiService.get<VehicleMaintenanceAlert[]>(url);
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch maintenance alerts';
      return rejectWithValue(message);
    }
  }
);

export const fetchAssignmentHistory = createAsyncThunk(
  'vehicles/fetchAssignmentHistory',
  async (vehicleId: string, { rejectWithValue }) => {
    try {
      const response = await apiService.get<VehicleAssignmentHistory[]>(`/vehicles/${vehicleId}/assignment-history`);
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch assignment history';
      return rejectWithValue(message);
    }
  }
);

export const fetchMaintenanceHistory = createAsyncThunk(
  'vehicles/fetchMaintenanceHistory',
  async (vehicleId: string, { rejectWithValue }) => {
    try {
      const response = await apiService.get<VehicleMaintenanceRecord[]>(`/vehicles/${vehicleId}/maintenance-history`);
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch maintenance history';
      return rejectWithValue(message);
    }
  }
);

export const performBulkOperation = createAsyncThunk(
  'vehicles/performBulkOperation',
  async (operation: VehicleBulkOperation, { rejectWithValue }) => {
    try {
      const response = await apiService.post<Vehicle[]>('/vehicles/bulk-operation', operation);
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to perform bulk operation';
      return rejectWithValue(message);
    }
  }
);

export const assignDriverToVehicle = createAsyncThunk(
  'vehicles/assignDriverToVehicle',
  async ({ vehicleId, driverId, reason }: { vehicleId: string; driverId: string; reason?: string }, { rejectWithValue }) => {
    try {
      const response = await apiService.post<Vehicle>(`/vehicles/${vehicleId}/assign-driver`, {
        driverId,
        reason
      });
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to assign driver';
      return rejectWithValue(message);
    }
  }
);

export const unassignDriverFromVehicle = createAsyncThunk(
  'vehicles/unassignDriverFromVehicle',
  async ({ vehicleId, reason }: { vehicleId: string; reason?: string }, { rejectWithValue }) => {
    try {
      const response = await apiService.post<Vehicle>(`/vehicles/${vehicleId}/unassign-driver`, {
        reason
      });
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to unassign driver';
      return rejectWithValue(message);
    }
  }
);

export const updateVehicleStatus = createAsyncThunk(
  'vehicles/updateVehicleStatus',
  async ({ vehicleId, status, reason }: { vehicleId: string; status: VehicleStatus; reason?: string }, { rejectWithValue }) => {
    try {
      const response = await apiService.patch<Vehicle>(`/vehicles/${vehicleId}/status`, {
        status,
        reason
      });
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to update vehicle status';
      return rejectWithValue(message);
    }
  }
);

const vehiclesSlice = createSlice({
  name: 'vehicles',
  initialState,
  reducers: {
    clearError: state => {
      state.error = null;
      state.bulkOperationError = null;
      state.analyticsError = null;
    },
    setCurrentVehicle: (state, action: PayloadAction<Vehicle | null>) => {
      state.currentVehicle = action.payload;
    },
    setFilters: (
      state,
      action: PayloadAction<Partial<VehicleSearchFilters>>
    ) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: state => {
      state.filters = {};
    },
    setPagination: (
      state,
      action: PayloadAction<Partial<VehiclesState['pagination']>>
    ) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
    setSelectedVehicles: (state, action: PayloadAction<string[]>) => {
      state.selectedVehicles = action.payload;
    },
    toggleVehicleSelection: (state, action: PayloadAction<string>) => {
      const vehicleId = action.payload;
      const index = state.selectedVehicles.indexOf(vehicleId);
      if (index > -1) {
        state.selectedVehicles.splice(index, 1);
      } else {
        state.selectedVehicles.push(vehicleId);
      }
    },
    clearSelectedVehicles: state => {
      state.selectedVehicles = [];
    },
    addRealTimeUpdate: (state, action: PayloadAction<VehicleRealTimeUpdate>) => {
      state.realTimeUpdates.unshift(action.payload);
      // Keep only the last 100 updates
      if (state.realTimeUpdates.length > 100) {
        state.realTimeUpdates = state.realTimeUpdates.slice(0, 100);
      }

      // Update vehicle data if it's in the current list
      if (action.payload.updateType === 'location' || action.payload.updateType === 'telemetry') {
        const vehicleIndex = state.vehicles.findIndex(v => v.id === action.payload.vehicleId);
        if (vehicleIndex > -1) {
          const vehicle = state.vehicles[vehicleIndex];
          if (action.payload.data.location) {
            vehicle.currentLocation = action.payload.data.location;
          }
          if (action.payload.data.telemetry) {
            vehicle.currentSpeed = action.payload.data.telemetry.speed;
            vehicle.currentFuelLevel = action.payload.data.telemetry.fuelLevel;
            vehicle.engineStatus = action.payload.data.telemetry.engineStatus;
            vehicle.lastTelemetryUpdate = action.payload.data.telemetry.timestamp;
          }
        }
      }
    },
    clearRealTimeUpdates: state => {
      state.realTimeUpdates = [];
    },
    setAssignmentConflicts: (state, action: PayloadAction<VehicleAssignmentConflict[]>) => {
      state.assignmentConflicts = action.payload;
    },
    clearAssignmentConflicts: state => {
      state.assignmentConflicts = [];
    },
  },
  extraReducers: builder => {
    builder
      // Fetch vehicles cases
      .addCase(fetchVehicles.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchVehicles.fulfilled, (state, action) => {
        state.isLoading = false;
        state.vehicles = action.payload.data;
        state.pagination = {
          page: action.payload.page,
          pageSize: action.payload.pageSize,
          total: action.payload.total,
          totalPages: action.payload.totalPages,
        };
        state.error = null;
      })
      .addCase(fetchVehicles.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Fetch vehicle by ID cases
      .addCase(fetchVehicleById.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchVehicleById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentVehicle = action.payload;
        state.error = null;
      })
      .addCase(fetchVehicleById.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Create vehicle cases
      .addCase(createVehicle.pending, state => {
        state.isCreating = true;
        state.error = null;
      })
      .addCase(createVehicle.fulfilled, (state, action) => {
        state.isCreating = false;
        state.vehicles.unshift(action.payload);
        state.pagination.total += 1;
        state.error = null;
      })
      .addCase(createVehicle.rejected, (state, action) => {
        state.isCreating = false;
        state.error = action.payload as string;
      })
      // Update vehicle cases
      .addCase(updateVehicle.pending, state => {
        state.isUpdating = true;
        state.error = null;
      })
      .addCase(updateVehicle.fulfilled, (state, action) => {
        state.isUpdating = false;
        const index = state.vehicles.findIndex(v => v.id === action.payload.id);
        if (index !== -1) {
          state.vehicles[index] = action.payload;
        }
        if (state.currentVehicle?.id === action.payload.id) {
          state.currentVehicle = action.payload;
        }
        state.error = null;
      })
      .addCase(updateVehicle.rejected, (state, action) => {
        state.isUpdating = false;
        state.error = action.payload as string;
      })
      // Delete vehicle cases
      .addCase(deleteVehicle.pending, state => {
        state.isDeleting = true;
        state.error = null;
      })
      .addCase(deleteVehicle.fulfilled, (state, action) => {
        state.isDeleting = false;
        state.vehicles = state.vehicles.filter(v => v.id !== action.payload);
        if (state.currentVehicle?.id === action.payload) {
          state.currentVehicle = null;
        }
        state.pagination.total = Math.max(0, state.pagination.total - 1);
        state.error = null;
      })
      .addCase(deleteVehicle.rejected, (state, action) => {
        state.isDeleting = false;
        state.error = action.payload as string;
      })
      // Fetch vehicle analytics cases
      .addCase(fetchVehicleAnalytics.pending, state => {
        state.isLoadingAnalytics = true;
        state.analyticsError = null;
      })
      .addCase(fetchVehicleAnalytics.fulfilled, (state, action) => {
        state.isLoadingAnalytics = false;
        state.vehicleAnalytics[action.payload.vehicleId] = action.payload.analytics;
        state.analyticsError = null;
      })
      .addCase(fetchVehicleAnalytics.rejected, (state, action) => {
        state.isLoadingAnalytics = false;
        state.analyticsError = action.payload as string;
      })
      // Fetch maintenance alerts cases
      .addCase(fetchMaintenanceAlerts.pending, state => {
        state.isLoadingMaintenanceAlerts = true;
        state.error = null;
      })
      .addCase(fetchMaintenanceAlerts.fulfilled, (state, action) => {
        state.isLoadingMaintenanceAlerts = false;
        state.maintenanceAlerts = action.payload;
        state.error = null;
      })
      .addCase(fetchMaintenanceAlerts.rejected, (state, action) => {
        state.isLoadingMaintenanceAlerts = false;
        state.error = action.payload as string;
      })
      // Fetch assignment history cases
      .addCase(fetchAssignmentHistory.fulfilled, (state, action) => {
        state.assignmentHistory = action.payload;
      })
      // Fetch maintenance history cases
      .addCase(fetchMaintenanceHistory.fulfilled, (state, action) => {
        state.maintenanceHistory = action.payload;
      })
      // Bulk operation cases
      .addCase(performBulkOperation.pending, state => {
        state.isBulkOperating = true;
        state.bulkOperationError = null;
      })
      .addCase(performBulkOperation.fulfilled, (state, action) => {
        state.isBulkOperating = false;
        // Update vehicles in the list
        action.payload.forEach(updatedVehicle => {
          const index = state.vehicles.findIndex(v => v.id === updatedVehicle.id);
          if (index !== -1) {
            state.vehicles[index] = updatedVehicle;
          }
        });
        state.selectedVehicles = [];
        state.bulkOperationError = null;
      })
      .addCase(performBulkOperation.rejected, (state, action) => {
        state.isBulkOperating = false;
        state.bulkOperationError = action.payload as string;
      })
      // Assign driver cases
      .addCase(assignDriverToVehicle.fulfilled, (state, action) => {
        const index = state.vehicles.findIndex(v => v.id === action.payload.id);
        if (index !== -1) {
          state.vehicles[index] = action.payload;
        }
        if (state.currentVehicle?.id === action.payload.id) {
          state.currentVehicle = action.payload;
        }
      })
      // Unassign driver cases
      .addCase(unassignDriverFromVehicle.fulfilled, (state, action) => {
        const index = state.vehicles.findIndex(v => v.id === action.payload.id);
        if (index !== -1) {
          state.vehicles[index] = action.payload;
        }
        if (state.currentVehicle?.id === action.payload.id) {
          state.currentVehicle = action.payload;
        }
      })
      // Update vehicle status cases
      .addCase(updateVehicleStatus.fulfilled, (state, action) => {
        const index = state.vehicles.findIndex(v => v.id === action.payload.id);
        if (index !== -1) {
          state.vehicles[index] = action.payload;
        }
        if (state.currentVehicle?.id === action.payload.id) {
          state.currentVehicle = action.payload;
        }
      });
  },
});

export const {
  clearError,
  setCurrentVehicle,
  setFilters,
  clearFilters,
  setPagination,
  setSelectedVehicles,
  toggleVehicleSelection,
  clearSelectedVehicles,
  addRealTimeUpdate,
  clearRealTimeUpdates,
  setAssignmentConflicts,
  clearAssignmentConflicts,
} = vehiclesSlice.actions;

// Selectors
export const selectVehicles = (state: { vehicles: VehiclesState }) =>
  state.vehicles.vehicles;
export const selectCurrentVehicle = (state: { vehicles: VehiclesState }) =>
  state.vehicles.currentVehicle;
export const selectSelectedVehicles = (state: { vehicles: VehiclesState }) =>
  state.vehicles.selectedVehicles;
export const selectVehicleAnalytics = (state: { vehicles: VehiclesState }) =>
  state.vehicles.vehicleAnalytics;
export const selectMaintenanceAlerts = (state: { vehicles: VehiclesState }) =>
  state.vehicles.maintenanceAlerts;
export const selectAssignmentHistory = (state: { vehicles: VehiclesState }) =>
  state.vehicles.assignmentHistory;
export const selectMaintenanceHistory = (state: { vehicles: VehiclesState }) =>
  state.vehicles.maintenanceHistory;
export const selectRealTimeUpdates = (state: { vehicles: VehiclesState }) =>
  state.vehicles.realTimeUpdates;
export const selectVehiclesLoading = (state: { vehicles: VehiclesState }) =>
  state.vehicles.isLoading;
export const selectVehiclesCreating = (state: { vehicles: VehiclesState }) =>
  state.vehicles.isCreating;
export const selectVehiclesUpdating = (state: { vehicles: VehiclesState }) =>
  state.vehicles.isUpdating;
export const selectVehiclesDeleting = (state: { vehicles: VehiclesState }) =>
  state.vehicles.isDeleting;
export const selectVehiclesBulkOperating = (state: { vehicles: VehiclesState }) =>
  state.vehicles.isBulkOperating;
export const selectVehiclesLoadingAnalytics = (state: { vehicles: VehiclesState }) =>
  state.vehicles.isLoadingAnalytics;
export const selectVehiclesLoadingMaintenanceAlerts = (state: { vehicles: VehiclesState }) =>
  state.vehicles.isLoadingMaintenanceAlerts;
export const selectVehiclesError = (state: { vehicles: VehiclesState }) =>
  state.vehicles.error;
export const selectVehiclesBulkOperationError = (state: { vehicles: VehiclesState }) =>
  state.vehicles.bulkOperationError;
export const selectVehiclesAnalyticsError = (state: { vehicles: VehiclesState }) =>
  state.vehicles.analyticsError;
export const selectVehiclesPagination = (state: { vehicles: VehiclesState }) =>
  state.vehicles.pagination;
export const selectVehiclesFilters = (state: { vehicles: VehiclesState }) =>
  state.vehicles.filters;
export const selectAssignmentConflicts = (state: { vehicles: VehiclesState }) =>
  state.vehicles.assignmentConflicts;

// Computed selectors
export const selectVehicleById = (vehicleId: string) => (state: { vehicles: VehiclesState }) =>
  state.vehicles.vehicles.find(vehicle => vehicle.id === vehicleId);

export const selectVehiclesByStatus = (status: VehicleStatus) => (state: { vehicles: VehiclesState }) =>
  state.vehicles.vehicles.filter(vehicle => vehicle.status === status);

export const selectAvailableVehicles = (state: { vehicles: VehiclesState }) =>
  state.vehicles.vehicles.filter(vehicle => vehicle.isAvailable);

export const selectVehiclesNeedingMaintenance = (state: { vehicles: VehiclesState }) =>
  state.vehicles.vehicles.filter(vehicle => vehicle.needsMaintenance);

export const selectVehiclesRequiringAttention = (state: { vehicles: VehiclesState }) =>
  state.vehicles.vehicles.filter(vehicle => vehicle.requiresImmediateAttention);

export const selectVehicleAnalyticsById = (vehicleId: string) => (state: { vehicles: VehiclesState }) =>
  state.vehicles.vehicleAnalytics[vehicleId];

export const selectMaintenanceAlertsForVehicle = (vehicleId: string) => (state: { vehicles: VehiclesState }) =>
  state.vehicles.maintenanceAlerts.filter(alert => alert.vehicleId === vehicleId);

export const selectCriticalMaintenanceAlerts = (state: { vehicles: VehiclesState }) =>
  state.vehicles.maintenanceAlerts.filter(alert => alert.priority === 'critical');

export const selectRecentRealTimeUpdates = (limit: number = 10) => (state: { vehicles: VehiclesState }) =>
  state.vehicles.realTimeUpdates.slice(0, limit);

export default vehiclesSlice.reducer;
