import { useMemo } from 'react';
import { useAuth } from './useAuth';

// Define permission types based on FleetXQ requirements
export type Permission =
  | 'view_dashboard'
  | 'view_vehicles'
  | 'manage_vehicles'
  | 'create_vehicles'
  | 'edit_vehicles'
  | 'delete_vehicles'
  | 'assign_drivers'
  | 'view_vehicle_analytics'
  | 'view_vehicle_location'
  | 'view_vehicle_maintenance'
  | 'schedule_maintenance'
  | 'view_assignment_history'
  | 'bulk_vehicle_operations'
  | 'export_vehicle_data'
  | 'view_drivers'
  | 'manage_drivers'
  | 'view_telemetry'
  | 'view_alerts'
  | 'manage_alerts'
  | 'view_admin_panel'
  | 'manage_users'
  | 'manage_system_settings'
  | 'view_reports'
  | 'export_data';

// Role-based permissions mapping
const ROLE_PERMISSIONS: Record<'admin' | 'manager' | 'driver', Permission[]> = {
  admin: [
    'view_dashboard',
    'view_vehicles',
    'manage_vehicles',
    'create_vehicles',
    'edit_vehicles',
    'delete_vehicles',
    'assign_drivers',
    'view_vehicle_analytics',
    'view_vehicle_location',
    'view_vehicle_maintenance',
    'schedule_maintenance',
    'view_assignment_history',
    'bulk_vehicle_operations',
    'export_vehicle_data',
    'view_drivers',
    'manage_drivers',
    'view_telemetry',
    'view_alerts',
    'manage_alerts',
    'view_admin_panel',
    'manage_users',
    'manage_system_settings',
    'view_reports',
    'export_data',
  ],
  manager: [
    'view_dashboard',
    'view_vehicles',
    'manage_vehicles',
    'create_vehicles',
    'edit_vehicles',
    'assign_drivers',
    'view_vehicle_analytics',
    'view_vehicle_location',
    'view_vehicle_maintenance',
    'schedule_maintenance',
    'view_assignment_history',
    'bulk_vehicle_operations',
    'export_vehicle_data',
    'view_drivers',
    'manage_drivers',
    'view_telemetry',
    'view_alerts',
    'manage_alerts',
    'view_reports',
    'export_data',
  ],
  driver: [
    'view_dashboard',
    'view_vehicles',
    'view_vehicle_location',
    'view_telemetry',
    'view_alerts',
  ],
};

export const usePermissions = () => {
  const { user, isAuthenticated } = useAuth();

  // Get user permissions based on role
  const permissions = useMemo(() => {
    if (!isAuthenticated || !user) {
      return [];
    }
    return ROLE_PERMISSIONS[user.role] || [];
  }, [isAuthenticated, user]);

  // Check if user has a specific permission
  const hasPermission = (permission: Permission): boolean => {
    return permissions.includes(permission);
  };

  // Check if user has any of the specified permissions
  const hasAnyPermission = (requiredPermissions: Permission[]): boolean => {
    return requiredPermissions.some(permission => permissions.includes(permission));
  };

  // Check if user has all of the specified permissions
  const hasAllPermissions = (requiredPermissions: Permission[]): boolean => {
    return requiredPermissions.every(permission => permissions.includes(permission));
  };

  // Check if user can access a specific route based on permissions
  const canAccessRoute = (routePermissions: Permission[]): boolean => {
    if (!isAuthenticated || routePermissions.length === 0) {
      return isAuthenticated;
    }
    return hasAnyPermission(routePermissions);
  };

  // Get permissions for UI rendering (useful for conditional rendering)
  const getUIPermissions = () => ({
    canViewDashboard: hasPermission('view_dashboard'),
    canViewVehicles: hasPermission('view_vehicles'),
    canManageVehicles: hasPermission('manage_vehicles'),
    canCreateVehicles: hasPermission('create_vehicles'),
    canEditVehicles: hasPermission('edit_vehicles'),
    canDeleteVehicles: hasPermission('delete_vehicles'),
    canAssignDrivers: hasPermission('assign_drivers'),
    canViewVehicleAnalytics: hasPermission('view_vehicle_analytics'),
    canViewVehicleLocation: hasPermission('view_vehicle_location'),
    canViewVehicleMaintenance: hasPermission('view_vehicle_maintenance'),
    canScheduleMaintenance: hasPermission('schedule_maintenance'),
    canViewAssignmentHistory: hasPermission('view_assignment_history'),
    canBulkVehicleOperations: hasPermission('bulk_vehicle_operations'),
    canExportVehicleData: hasPermission('export_vehicle_data'),
    canViewDrivers: hasPermission('view_drivers'),
    canManageDrivers: hasPermission('manage_drivers'),
    canViewTelemetry: hasPermission('view_telemetry'),
    canViewAlerts: hasPermission('view_alerts'),
    canManageAlerts: hasPermission('manage_alerts'),
    canViewAdminPanel: hasPermission('view_admin_panel'),
    canManageUsers: hasPermission('manage_users'),
    canManageSystemSettings: hasPermission('manage_system_settings'),
    canViewReports: hasPermission('view_reports'),
    canExportData: hasPermission('export_data'),
  });

  return {
    permissions,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    canAccessRoute,
    getUIPermissions,
  };
};
