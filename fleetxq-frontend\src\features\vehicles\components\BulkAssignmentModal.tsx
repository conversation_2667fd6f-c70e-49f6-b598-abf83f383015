import React, { useState, useEffect } from 'react';
import { 
  XMarkIcon, 
  ExclamationTriangleIcon,
  UserIcon,
  TruckIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';
import { useAppDispatch, useAppSelector } from '../../../store/hooks';
import {
  performBulkOperation,
  selectVehiclesBulkOperating,
  selectVehiclesBulkOperationError,
  selectVehicles,
  clearError
} from '../vehiclesSlice';
import type { Vehicle, VehicleAssignmentConflict } from '../../../types';
import LoadingSpinner from '../../../components/LoadingSpinner';

interface BulkAssignmentModalProps {
  selectedVehicleIds: string[];
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

interface AssignmentPlan {
  vehicleId: string;
  vehicle: Vehicle;
  driverId: string | null;
  driverName: string | null;
  conflicts: VehicleAssignmentConflict[];
  status: 'pending' | 'success' | 'error';
}

// Mock driver data
const mockDrivers = [
  { id: '1', name: '<PERSON>', email: '<EMAIL>', status: 'active', currentVehicleId: null },
  { id: '2', name: 'Jane <PERSON>', email: '<EMAIL>', status: 'active', currentVehicleId: null },
  { id: '3', name: 'Mike <PERSON>', email: '<EMAIL>', status: 'active', currentVehicleId: null },
  { id: '4', name: 'Sarah Wilson', email: '<EMAIL>', status: 'active', currentVehicleId: null },
  { id: '5', name: 'David Brown', email: '<EMAIL>', status: 'active', currentVehicleId: null },
];

const BulkAssignmentModal: React.FC<BulkAssignmentModalProps> = ({
  selectedVehicleIds,
  isOpen,
  onClose,
  onSuccess
}) => {
  const dispatch = useAppDispatch();
  const vehicles = useAppSelector(selectVehicles);
  const isBulkOperating = useAppSelector(selectVehiclesBulkOperating);
  const bulkOperationError = useAppSelector(selectVehiclesBulkOperationError);

  const [assignmentPlans, setAssignmentPlans] = useState<AssignmentPlan[]>([]);
  const [assignmentMode, setAssignmentMode] = useState<'manual' | 'auto'>('manual');
  const [reason, setReason] = useState('');

  // Initialize assignment plans when modal opens
  useEffect(() => {
    if (isOpen && selectedVehicleIds.length > 0) {
      const selectedVehicles = vehicles.filter(v => selectedVehicleIds.includes(v.id));
      const plans: AssignmentPlan[] = selectedVehicles.map(vehicle => ({
        vehicleId: vehicle.id,
        vehicle,
        driverId: null,
        driverName: null,
        conflicts: [],
        status: 'pending'
      }));
      setAssignmentPlans(plans);
      dispatch(clearError());
    }
  }, [isOpen, selectedVehicleIds, vehicles, dispatch]);

  // Auto-assign drivers
  const handleAutoAssign = () => {
    const availableDrivers = mockDrivers.filter(d => 
      d.status === 'active' && 
      !d.currentVehicleId &&
      !assignmentPlans.some(plan => plan.driverId === d.id)
    );

    const updatedPlans = assignmentPlans.map((plan, index) => {
      if (index < availableDrivers.length) {
        const driver = availableDrivers[index];
        return {
          ...plan,
          driverId: driver.id,
          driverName: driver.name,
          conflicts: validateAssignment(plan.vehicle, driver.id)
        };
      }
      return {
        ...plan,
        driverId: null,
        driverName: null,
        conflicts: []
      };
    });

    setAssignmentPlans(updatedPlans);
  };

  // Validate assignment for conflicts
  const validateAssignment = (vehicle: Vehicle, driverId: string): VehicleAssignmentConflict[] => {
    const conflicts: VehicleAssignmentConflict[] = [];
    const driver = mockDrivers.find(d => d.id === driverId);

    if (!driver) return conflicts;

    // Check if driver is already assigned in this bulk operation
    const duplicateAssignment = assignmentPlans.find(plan => 
      plan.driverId === driverId && plan.vehicleId !== vehicle.id
    );
    if (duplicateAssignment) {
      conflicts.push({
        type: 'driver_already_assigned',
        message: `${driver.name} is already assigned to another vehicle in this operation`,
        suggestions: ['Choose a different driver']
      });
    }

    // Check if driver is inactive
    if (driver.status === 'inactive') {
      conflicts.push({
        type: 'driver_inactive',
        message: `${driver.name} is currently inactive`,
        suggestions: ['Choose an active driver']
      });
    }

    // Check if vehicle is in maintenance
    if (vehicle.status === 'maintenance') {
      conflicts.push({
        type: 'vehicle_maintenance',
        message: 'Vehicle is currently under maintenance',
        suggestions: ['Complete maintenance first']
      });
    }

    return conflicts;
  };

  // Update individual assignment
  const updateAssignment = (vehicleId: string, driverId: string | null) => {
    setAssignmentPlans(prev => prev.map(plan => {
      if (plan.vehicleId === vehicleId) {
        const driver = driverId ? mockDrivers.find(d => d.id === driverId) : null;
        return {
          ...plan,
          driverId,
          driverName: driver?.name || null,
          conflicts: driverId ? validateAssignment(plan.vehicle, driverId) : []
        };
      }
      return plan;
    }));
  };

  // Execute bulk assignment
  const handleExecuteAssignment = async () => {
    const validPlans = assignmentPlans.filter(plan => 
      plan.driverId && plan.conflicts.length === 0
    );

    if (validPlans.length === 0) {
      return;
    }

    try {
      // In a real app, this would be a proper bulk assignment API call
      for (const plan of validPlans) {
        await dispatch(performBulkOperation({
          vehicleIds: [plan.vehicleId],
          operation: 'assignDriver',
          data: {
            driverId: plan.driverId!,
            reason
          }
        })).unwrap();
        
        // Update plan status
        setAssignmentPlans(prev => prev.map(p => 
          p.vehicleId === plan.vehicleId ? { ...p, status: 'success' } : p
        ));
      }

      onSuccess?.();
      handleClose();
    } catch (error) {
      console.error('Bulk assignment failed:', error);
      // Mark failed assignments
      setAssignmentPlans(prev => prev.map(p => 
        validPlans.some(vp => vp.vehicleId === p.vehicleId) ? { ...p, status: 'error' } : p
      ));
    }
  };

  const handleClose = () => {
    if (!isBulkOperating) {
      setAssignmentPlans([]);
      setReason('');
      dispatch(clearError());
      onClose();
    }
  };

  if (!isOpen) return null;

  const totalConflicts = assignmentPlans.reduce((sum, plan) => sum + plan.conflicts.length, 0);
  const validAssignments = assignmentPlans.filter(plan => plan.driverId && plan.conflicts.length === 0).length;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-25" onClick={handleClose} />
        
        <div className="relative bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Bulk Driver Assignment
              </h2>
              <p className="text-sm text-gray-600 mt-1">
                Assign drivers to {selectedVehicleIds.length} selected vehicles
              </p>
            </div>
            <button
              onClick={handleClose}
              disabled={isBulkOperating}
              className="p-2 hover:bg-gray-100 rounded-lg disabled:opacity-50"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>

          {/* Assignment Mode */}
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center space-x-4 mb-4">
              <label className="flex items-center space-x-2">
                <input
                  type="radio"
                  name="assignmentMode"
                  value="manual"
                  checked={assignmentMode === 'manual'}
                  onChange={(e) => setAssignmentMode(e.target.value as 'manual')}
                  className="text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm font-medium text-gray-700">Manual Assignment</span>
              </label>
              <label className="flex items-center space-x-2">
                <input
                  type="radio"
                  name="assignmentMode"
                  value="auto"
                  checked={assignmentMode === 'auto'}
                  onChange={(e) => setAssignmentMode(e.target.value as 'auto')}
                  className="text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm font-medium text-gray-700">Auto Assignment</span>
              </label>
            </div>

            {assignmentMode === 'auto' && (
              <button
                onClick={handleAutoAssign}
                className="btn-secondary text-sm"
                disabled={isBulkOperating}
              >
                Auto Assign Available Drivers
              </button>
            )}
          </div>

          {/* Error Display */}
          {bulkOperationError && (
            <div className="p-6 border-b border-gray-200">
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <p className="text-sm text-red-700">{bulkOperationError}</p>
              </div>
            </div>
          )}

          {/* Summary */}
          <div className="p-6 border-b border-gray-200">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div className="bg-blue-50 rounded-lg p-3">
                <div className="text-2xl font-bold text-blue-600">{assignmentPlans.length}</div>
                <div className="text-sm text-blue-800">Total Vehicles</div>
              </div>
              <div className="bg-green-50 rounded-lg p-3">
                <div className="text-2xl font-bold text-green-600">{validAssignments}</div>
                <div className="text-sm text-green-800">Valid Assignments</div>
              </div>
              <div className="bg-red-50 rounded-lg p-3">
                <div className="text-2xl font-bold text-red-600">{totalConflicts}</div>
                <div className="text-sm text-red-800">Conflicts</div>
              </div>
            </div>
          </div>

          {/* Assignment Plans */}
          <div className="p-6 max-h-96 overflow-y-auto">
            <div className="space-y-4">
              {assignmentPlans.map((plan) => (
                <div key={plan.vehicleId} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-gray-100 rounded-lg">
                        <TruckIcon className="h-5 w-5 text-gray-600" />
                      </div>
                      <div>
                        <h4 className="text-sm font-semibold text-gray-900">
                          {plan.vehicle.vehicleName || `${plan.vehicle.year} ${plan.vehicle.make} ${plan.vehicle.model}`}
                        </h4>
                        <p className="text-sm text-gray-600">{plan.vehicle.licensePlate}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      {plan.status === 'success' && (
                        <CheckCircleIcon className="h-5 w-5 text-green-600" />
                      )}
                      {plan.status === 'error' && (
                        <XCircleIcon className="h-5 w-5 text-red-600" />
                      )}
                      {plan.conflicts.length > 0 && (
                        <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600" />
                      )}
                    </div>
                  </div>

                  {/* Driver Selection */}
                  <div className="mb-3">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Assign Driver
                    </label>
                    <select
                      value={plan.driverId || ''}
                      onChange={(e) => updateAssignment(plan.vehicleId, e.target.value || null)}
                      className="input-field"
                      disabled={isBulkOperating || assignmentMode === 'auto'}
                    >
                      <option value="">Select a driver</option>
                      {mockDrivers.filter(d => d.status === 'active').map(driver => (
                        <option key={driver.id} value={driver.id}>
                          {driver.name} ({driver.email})
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Conflicts */}
                  {plan.conflicts.length > 0 && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                      <div className="flex items-start space-x-2">
                        <ExclamationTriangleIcon className="h-4 w-4 text-yellow-600 mt-0.5" />
                        <div className="flex-1">
                          <h5 className="text-sm font-medium text-yellow-800 mb-1">Conflicts</h5>
                          {plan.conflicts.map((conflict, index) => (
                            <div key={index} className="text-sm text-yellow-700 mb-1">
                              {conflict.message}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Reason */}
          <div className="p-6 border-t border-gray-200">
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Reason for Bulk Assignment
              </label>
              <textarea
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                rows={2}
                className="input-field"
                placeholder="Enter reason for bulk assignment..."
                disabled={isBulkOperating}
              />
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3">
              <button
                onClick={handleClose}
                disabled={isBulkOperating}
                className="btn-secondary disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                onClick={handleExecuteAssignment}
                disabled={isBulkOperating || validAssignments === 0}
                className="btn-primary disabled:opacity-50 flex items-center space-x-2"
              >
                {isBulkOperating && <LoadingSpinner size="sm" />}
                <span>
                  Assign {validAssignments} Vehicle{validAssignments !== 1 ? 's' : ''}
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BulkAssignmentModal;
