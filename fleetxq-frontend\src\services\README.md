# FleetXQ API Service Layer

This directory contains the centralized API service layer for the FleetXQ frontend application, implemented according to **Prompt 1.3** specifications.

## Overview

The API service layer provides a comprehensive, type-safe interface for communicating with the FleetXQ backend. It includes:

- Centralized API configuration with Axios
- JWT token management and automatic refresh
- Request/response interceptors
- Error handling and retry logic
- Domain-specific service modules
- TypeScript interfaces for all API contracts

## Architecture

### Base API Service (`api.ts`)

The core API service provides:
- **Axios Configuration**: Base URL, timeout, headers
- **JWT Token Management**: Automatic token attachment and refresh
- **Request Interceptors**: Token injection, request logging
- **Response Interceptors**: Error handling, token refresh on 401
- **Retry Logic**: Exponential backoff for network errors
- **Error Handling**: Consistent error transformation

### Service Modules

Each domain has its own service module:

#### 1. Auth Service (`authService.ts`)
- Login/logout operations
- Token refresh management
- User profile operations
- Password management
- Email verification

#### 2. Vehicle Service (`vehicleService.ts`)
- CRUD operations for vehicles
- Status updates and assignments
- Maintenance scheduling
- Location tracking
- Performance statistics

#### 3. Driver Service (`driverService.ts`)
- Driver management operations
- Vehicle assignments
- Performance tracking
- Driving history
- License management

#### 4. Telemetry Service (`telemetryService.ts`)
- Real-time telemetry data
- Historical data queries
- Fleet metrics
- Location tracking
- Performance analysis

#### 5. Alert Service (`alertService.ts`)
- Alert management
- Acknowledgment operations
- Alert rules configuration
- Notification settings
- Statistics and trends

#### 6. Dashboard Service (`dashboardService.ts`)
- Dashboard summaries
- Analytics and metrics
- Performance data
- Real-time updates
- Custom widgets

## Usage

### Basic Usage

```typescript
import { apiService, authService, vehicleService } from '@/services';

// Login user
const loginResponse = await authService.login({
  email: '<EMAIL>',
  password: 'password'
});

// Get vehicles
const vehicles = await vehicleService.getVehicles();

// Direct API calls
const response = await apiService.get<User>('/users/profile');
```

### Error Handling

All services use consistent error handling:

```typescript
try {
  const vehicles = await vehicleService.getVehicles();
} catch (error) {
  if (error.response?.status === 401) {
    // Handle unauthorized
  } else if (error.response?.status >= 500) {
    // Handle server error
  } else {
    // Handle other errors
  }
}
```

### Pagination

Services support pagination where applicable:

```typescript
const paginatedVehicles = await vehicleService.getVehicles(
  { status: 'active' }, // filters
  1, // page
  20 // pageSize
);
```

## Configuration

### Environment Variables

```env
VITE_API_URL=http://localhost:5000/api
```

### API Configuration

The base API service can be configured:

```typescript
apiService.updateConfig({
  baseURL: 'https://api.fleetxq.com',
  timeout: 30000,
  retryAttempts: 5
});
```

## Features

### JWT Token Management
- Automatic token attachment to requests
- Token refresh on 401 responses
- Queue failed requests during refresh
- Automatic logout on refresh failure

### Retry Logic
- Exponential backoff strategy
- Configurable retry attempts
- Network error detection
- Timeout handling

### Request/Response Interceptors
- Request logging in development
- Response time tracking
- Error transformation
- Token management

### TypeScript Support
- Full type safety for all API calls
- Interface definitions for requests/responses
- Generic type support
- Error type definitions

## Testing

Run the integration tests:

```bash
npm test src/services/__tests__/api-integration.test.ts
```

## File Structure

```
src/services/
├── api.ts                 # Base API service
├── authService.ts         # Authentication service
├── vehicleService.ts      # Vehicle management
├── driverService.ts       # Driver management
├── telemetryService.ts    # Telemetry data
├── alertService.ts        # Alert management
├── dashboardService.ts    # Dashboard analytics
├── index.ts              # Service exports
├── __tests__/            # Integration tests
└── README.md             # This file
```

## Best Practices

1. **Always use the service modules** instead of direct API calls
2. **Handle errors appropriately** in your components
3. **Use TypeScript interfaces** for type safety
4. **Implement loading states** for async operations
5. **Cache data when appropriate** to reduce API calls

## Error Codes

Common error scenarios:
- `401`: Unauthorized (token expired/invalid)
- `403`: Forbidden (insufficient permissions)
- `404`: Resource not found
- `422`: Validation error
- `500+`: Server errors (will be retried)

## Contributing

When adding new API endpoints:
1. Add TypeScript interfaces to `types/index.ts`
2. Implement methods in the appropriate service
3. Add error handling
4. Update tests
5. Document the new functionality
