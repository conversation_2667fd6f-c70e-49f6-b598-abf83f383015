import React, { <PERSON>ps<PERSON>ithChildren } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { configureStore, PreloadedState } from '@reduxjs/toolkit';
import { Provider } from 'react-redux';
import { MemoryRouter } from 'react-router-dom';
import { RootState } from '../store';
import authSlice from '../features/auth/authSlice';
import vehiclesSlice from '../features/vehicles/vehiclesSlice';
import driversSlice from '../features/drivers/driversSlice';
import telemetrySlice from '../features/telemetry/telemetrySlice';
import alertsSlice from '../features/alerts/alertsSlice';
import dashboardSlice from '../features/dashboard/dashboardSlice';
import type { User } from '../types';

// Mock user data for testing
export const mockUsers = {
  admin: {
    id: '1',
    email: '<EMAIL>',
    name: 'Admin User',
    role: 'admin' as const,
    isAuthenticated: true,
  },
  manager: {
    id: '2',
    email: '<EMAIL>',
    name: 'Manager User',
    role: 'manager' as const,
    isAuthenticated: true,
  },
  driver: {
    id: '3',
    email: '<EMAIL>',
    name: 'Driver User',
    role: 'driver' as const,
    isAuthenticated: true,
  },
};

// Mock tokens
export const mockTokens = {
  accessToken: 'mock-access-token',
  refreshToken: 'mock-refresh-token',
};

// Create a test store
export function createTestStore(preloadedState?: PreloadedState<RootState>) {
  return configureStore({
    reducer: {
      auth: authSlice,
      vehicles: vehiclesSlice,
      drivers: driversSlice,
      telemetry: telemetrySlice,
      alerts: alertsSlice,
      dashboard: dashboardSlice,
    },
    preloadedState,
    middleware: getDefaultMiddleware =>
      getDefaultMiddleware({
        serializableCheck: {
          ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
        },
      }),
  });
}

// Create authenticated state
export function createAuthenticatedState(user: User): PreloadedState<RootState> {
  return {
    auth: {
      user,
      token: mockTokens.accessToken,
      refreshToken: mockTokens.refreshToken,
      isAuthenticated: true,
      isLoading: false,
      error: null,
    },
  };
}

// Create unauthenticated state
export function createUnauthenticatedState(): PreloadedState<RootState> {
  return {
    auth: {
      user: null,
      token: null,
      refreshToken: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
    },
  };
}

// Custom render function with Redux and Router
interface ExtendedRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  preloadedState?: PreloadedState<RootState>;
  store?: ReturnType<typeof createTestStore>;
  initialEntries?: string[];
}

export function renderWithProviders(
  ui: React.ReactElement,
  {
    preloadedState,
    store = createTestStore(preloadedState),
    initialEntries = ['/'],
    ...renderOptions
  }: ExtendedRenderOptions = {}
) {
  function Wrapper({ children }: PropsWithChildren<{}>): JSX.Element {
    return (
      <Provider store={store}>
        <MemoryRouter initialEntries={initialEntries}>
          {children}
        </MemoryRouter>
      </Provider>
    );
  }

  return {
    store,
    ...render(ui, { wrapper: Wrapper, ...renderOptions }),
  };
}

// Helper to create mock API responses
export const createMockApiResponse = <T,>(data: T) => ({
  data,
  status: 200,
  statusText: 'OK',
  headers: {},
  config: {},
});

// Helper to create mock API error
export const createMockApiError = (message: string, status = 400) => ({
  response: {
    data: { message },
    status,
    statusText: status === 400 ? 'Bad Request' : 'Internal Server Error',
  },
  message,
  isAxiosError: true,
});

// Mock JWT token for testing
export const createMockJWT = (payload: any) => {
  const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));
  const payloadEncoded = btoa(JSON.stringify(payload));
  const signature = 'mock-signature';
  return `${header}.${payloadEncoded}.${signature}`;
};

// Helper to wait for async operations
export const waitFor = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));
