# FleetXQ Authentication System

This document provides comprehensive documentation for the FleetXQ authentication system, including JWT token management, role-based access control, and route protection.

## Table of Contents

1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Authentication Flow](#authentication-flow)
4. [Components](#components)
5. [Hooks](#hooks)
6. [Route Protection](#route-protection)
7. [Token Management](#token-management)
8. [Permissions System](#permissions-system)
9. [Usage Examples](#usage-examples)
10. [Testing](#testing)

## Overview

The FleetXQ authentication system provides:

- JWT-based authentication with automatic token refresh
- Role-based access control (Admin, Manager, Driver)
- Permission-based UI rendering
- Secure token storage with encryption
- Comprehensive route protection
- Automatic session management

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Components    │    │     Hooks       │    │   Services      │
│                 │    │                 │    │                 │
│ • LoginPage     │◄──►│ • useAuth       │◄──►│ • API Service   │
│ • LogoutButton  │    │ • usePermissions│    │ • Auth Service  │
│ • UserProfile   │    │ • useTokenRefresh│   │ • Token Storage │
│ • RoleGuard     │    │                 │    │                 │
│ • ProtectedRoute│    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Redux Store   │
                    │                 │
                    │ • Auth Slice    │
                    │ • User State    │
                    │ • Token State   │
                    └─────────────────┘
```

## Authentication Flow

### 1. Application Initialization

```typescript
// App.tsx
function App() {
  return (
    <Provider store={store}>
      <AuthProvider>  {/* Handles auth initialization */}
        <RouterProvider router={router} />
      </AuthProvider>
    </Provider>
  );
}
```

### 2. Login Process

1. User enters credentials in `LoginPage`
2. `useAuth` hook calls `loginUser` action
3. API request sent with credentials
4. Server responds with JWT tokens
5. Tokens stored securely using `tokenStorage`
6. User state updated in Redux store
7. User redirected to intended page

### 3. Token Refresh

1. `useTokenRefresh` monitors token expiry
2. Automatic refresh 5 minutes before expiry
3. API interceptor handles 401 responses
4. Failed refresh triggers logout

## Components

### LoginPage

Enhanced login form with validation and error handling.

```typescript
import LoginPage from '../features/auth/LoginPage';

// Usage in routes
{
  path: 'login',
  element: <LoginPage />
}
```

### LogoutButton

Configurable logout button with confirmation dialog.

```typescript
import LogoutButton from '../features/auth/LogoutButton';

// Basic usage
<LogoutButton />

// With confirmation
<LogoutButton 
  showConfirmation={true}
  variant="button"
  onLogoutComplete={() => console.log('Logged out')}
/>
```

### UserProfileDropdown

User profile dropdown with role indicator and navigation.

```typescript
import UserProfileDropdown from '../features/auth/UserProfileDropdown';

<UserProfileDropdown 
  showAvatar={true}
  showRoleBadge={true}
/>
```

### RoleIndicatorBadge

Visual role indicator with customizable appearance.

```typescript
import RoleIndicatorBadge from '../features/auth/RoleIndicatorBadge';

<RoleIndicatorBadge 
  role="admin"
  size="md"
  variant="solid"
  showIcon={true}
/>
```

## Hooks

### useAuth

Primary authentication hook providing user state and actions.

```typescript
import { useAuth } from '../hooks/useAuth';

function MyComponent() {
  const { 
    user, 
    isAuthenticated, 
    isLoading, 
    error,
    login, 
    logout, 
    hasRole,
    hasAnyRole 
  } = useAuth();

  const handleLogin = async () => {
    const result = await login({ email, password });
    if (result.success) {
      // Handle success
    } else {
      // Handle error
      console.error(result.error);
    }
  };

  return (
    <div>
      {isAuthenticated ? (
        <p>Welcome, {user?.name}!</p>
      ) : (
        <button onClick={handleLogin}>Login</button>
      )}
    </div>
  );
}
```

### usePermissions

Permission-based access control hook.

```typescript
import { usePermissions } from '../hooks/usePermissions';

function MyComponent() {
  const { 
    hasPermission, 
    hasAnyPermission,
    canAccessRoute,
    getUIPermissions 
  } = usePermissions();

  const uiPermissions = getUIPermissions();

  return (
    <div>
      {hasPermission('view_vehicles') && (
        <VehiclesList />
      )}
      
      {uiPermissions.canManageUsers && (
        <UserManagement />
      )}
    </div>
  );
}
```

### useTokenRefresh

Automatic token refresh management.

```typescript
import { useTokenRefresh } from '../hooks/useTokenRefresh';

function MyComponent() {
  const { 
    refreshTokenNow, 
    isTokenExpired, 
    needsRefresh,
    getTimeUntilExpiry 
  } = useTokenRefresh();

  // Manual refresh
  const handleRefresh = async () => {
    const result = await refreshTokenNow();
    if (!result.success) {
      console.error('Refresh failed:', result.error);
    }
  };

  return (
    <div>
      <p>Token expires in: {getTimeUntilExpiry()}ms</p>
      {needsRefresh() && (
        <button onClick={handleRefresh}>Refresh Token</button>
      )}
    </div>
  );
}
```

## Route Protection

### ProtectedRoute

Main route protection component with role and permission support.

```typescript
import ProtectedRoute from '../routes/ProtectedRoute';

// Basic protection (requires authentication)
<ProtectedRoute>
  <DashboardPage />
</ProtectedRoute>

// Role-based protection
<ProtectedRoute requiredRole="admin">
  <AdminPanel />
</ProtectedRoute>

// Multiple allowed roles
<ProtectedRoute allowedRoles={['admin', 'manager']}>
  <ManagerPanel />
</ProtectedRoute>

// Permission-based protection
<ProtectedRoute requiredPermissions={['manage_users']}>
  <UserManagement />
</ProtectedRoute>
```

### RoleGuard

Component-level role and permission guarding.

```typescript
import RoleGuard from '../components/RoleGuard';

// Role-based guarding
<RoleGuard allowedRoles={['admin']}>
  <AdminOnlyComponent />
</RoleGuard>

// Permission-based guarding
<RoleGuard requiredPermissions={['view_reports']}>
  <ReportsComponent />
</RoleGuard>

// With fallback component
<RoleGuard 
  allowedRoles={['admin']}
  showFallback={true}
  fallbackComponent={() => <div>Access Denied</div>}
>
  <AdminComponent />
</RoleGuard>
```

### PermissionGuard

UI-level permission guarding for conditional rendering.

```typescript
import PermissionGuard from '../components/PermissionGuard';

<PermissionGuard permissions={['manage_vehicles']}>
  <button>Add Vehicle</button>
</PermissionGuard>

<PermissionGuard 
  permissions={['view_reports', 'export_data']}
  requireAll={false}  // User needs ANY of these permissions
>
  <ReportsSection />
</PermissionGuard>
```

## Token Management

### Secure Storage

The `tokenStorage` utility provides secure token management:

```typescript
import { tokenStorage } from '../utils/tokenStorage';

// Store tokens
tokenStorage.setTokens(accessToken, refreshToken, {
  useSessionStorage: false,  // Use localStorage
  encrypt: true             // Encrypt tokens
});

// Retrieve tokens
const accessToken = tokenStorage.getAccessToken();
const refreshToken = tokenStorage.getRefreshToken();

// Check token status
const isExpired = tokenStorage.isTokenExpired();
const needsRefresh = tokenStorage.needsRefresh();

// Clear tokens
tokenStorage.clearTokens();
```

### Automatic Refresh

Token refresh is handled automatically by:

1. `useTokenRefresh` hook scheduling refresh
2. API interceptors handling 401 responses
3. Background refresh for active users

## Permissions System

### Role-Based Permissions

```typescript
const ROLE_PERMISSIONS = {
  admin: [
    'view_dashboard', 'view_vehicles', 'manage_vehicles',
    'view_drivers', 'manage_drivers', 'view_telemetry',
    'view_alerts', 'manage_alerts', 'view_admin_panel',
    'manage_users', 'manage_system_settings', 'view_reports',
    'export_data'
  ],
  manager: [
    'view_dashboard', 'view_vehicles', 'manage_vehicles',
    'view_drivers', 'manage_drivers', 'view_telemetry',
    'view_alerts', 'manage_alerts', 'view_reports',
    'export_data'
  ],
  driver: [
    'view_dashboard', 'view_vehicles', 'view_telemetry',
    'view_alerts'
  ]
};
```

### Custom Permission Checks

```typescript
// Check single permission
const canManageUsers = hasPermission('manage_users');

// Check multiple permissions (ANY)
const canAccessReports = hasAnyPermission(['view_reports', 'export_data']);

// Check multiple permissions (ALL)
const canFullyManageVehicles = hasAllPermissions(['view_vehicles', 'manage_vehicles']);

// Route-based permission check
const canAccessRoute = canAccessRoute(['manage_users']);
```

## Usage Examples

### Complete Authentication Setup

```typescript
// App.tsx - Root application setup
import React from 'react';
import { Provider } from 'react-redux';
import { RouterProvider } from 'react-router-dom';
import { store } from './store';
import { router } from './routes';
import AuthProvider from './features/auth/AuthProvider';

function App() {
  return (
    <Provider store={store}>
      <AuthProvider>
        <RouterProvider router={router} />
      </AuthProvider>
    </Provider>
  );
}

export default App;
```

### Protected Dashboard Component

```typescript
// DashboardPage.tsx
import React from 'react';
import { useAuth } from '../hooks/useAuth';
import { usePermissions } from '../hooks/usePermissions';
import PermissionGuard from '../components/PermissionGuard';
import UserProfileDropdown from '../features/auth/UserProfileDropdown';

const DashboardPage: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const { getUIPermissions } = usePermissions();
  const permissions = getUIPermissions();

  if (!isAuthenticated) {
    return <div>Please log in to access the dashboard.</div>;
  }

  return (
    <div className="dashboard">
      <header className="dashboard-header">
        <h1>FleetXQ Dashboard</h1>
        <UserProfileDropdown />
      </header>

      <main className="dashboard-content">
        <div className="welcome-section">
          <h2>Welcome back, {user?.name}!</h2>
          <p>Role: {user?.role}</p>
        </div>

        <div className="dashboard-widgets">
          {permissions.canViewVehicles && (
            <div className="widget">
              <h3>Vehicles Overview</h3>
              {/* Vehicle content */}
            </div>
          )}

          {permissions.canViewDrivers && (
            <div className="widget">
              <h3>Drivers Overview</h3>
              {/* Drivers content */}
            </div>
          )}

          <PermissionGuard permissions={['view_reports']}>
            <div className="widget">
              <h3>Reports</h3>
              {/* Reports content */}
            </div>
          </PermissionGuard>

          <PermissionGuard permissions={['view_admin_panel']}>
            <div className="widget">
              <h3>Admin Panel</h3>
              {/* Admin content */}
            </div>
          </PermissionGuard>
        </div>
      </main>
    </div>
  );
};

export default DashboardPage;
```

### Navigation with Role-Based Menu

```typescript
// Navigation.tsx
import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { usePermissions } from '../hooks/usePermissions';
import { getNavigationRoutes } from '../routes/routeConfig';

const Navigation: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const { hasPermission } = usePermissions();

  if (!isAuthenticated || !user) {
    return null;
  }

  const navigationRoutes = getNavigationRoutes(user.role);

  return (
    <nav className="navigation">
      <ul>
        {navigationRoutes.map(route => {
          // Check if user has required permissions for this route
          const canAccess = !route.requiredPermissions ||
            route.requiredPermissions.some(permission => hasPermission(permission));

          if (!canAccess) return null;

          return (
            <li key={route.path}>
              <Link to={route.path} className="nav-link">
                <span className="nav-icon">{route.icon}</span>
                <span className="nav-text">{route.name}</span>
              </Link>
            </li>
          );
        })}
      </ul>
    </nav>
  );
};

export default Navigation;
```

### Custom Hook for Feature Access

```typescript
// useFeatureAccess.ts
import { useAuth } from './useAuth';
import { usePermissions } from './usePermissions';

export const useFeatureAccess = () => {
  const { user, isAuthenticated } = useAuth();
  const { hasPermission, hasAnyPermission } = usePermissions();

  return {
    // Vehicle management features
    canViewVehicles: hasPermission('view_vehicles'),
    canAddVehicle: hasPermission('manage_vehicles'),
    canEditVehicle: hasPermission('manage_vehicles'),
    canDeleteVehicle: hasPermission('manage_vehicles') && user?.role === 'admin',

    // Driver management features
    canViewDrivers: hasPermission('view_drivers'),
    canManageDrivers: hasPermission('manage_drivers'),

    // Reporting features
    canViewReports: hasPermission('view_reports'),
    canExportData: hasPermission('export_data'),
    canViewAnalytics: hasAnyPermission(['view_reports', 'view_admin_panel']),

    // Admin features
    canAccessAdminPanel: hasPermission('view_admin_panel'),
    canManageUsers: hasPermission('manage_users'),
    canManageSystemSettings: hasPermission('manage_system_settings'),

    // UI helpers
    isAdmin: user?.role === 'admin',
    isManager: user?.role === 'manager',
    isDriver: user?.role === 'driver',
    isAuthenticated,
  };
};
```

### Error Boundary with Authentication

```typescript
// AuthErrorBoundary.tsx
import React, { Component, ErrorInfo, ReactNode } from 'react';
import { tokenStorage } from '../utils/tokenStorage';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

class AuthErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Authentication error:', error, errorInfo);

    // Clear tokens if authentication-related error
    if (error.message.includes('token') || error.message.includes('auth')) {
      tokenStorage.clearTokens();
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="auth-error-boundary">
          <h2>Authentication Error</h2>
          <p>Something went wrong with authentication. Please try logging in again.</p>
          <button onClick={() => window.location.href = '/auth/login'}>
            Go to Login
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

export default AuthErrorBoundary;
```

## Testing

### Running Tests

```bash
# Run all tests
npm run test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:ui
```

### Test Examples

```typescript
// Example test for authentication hook
import { renderHook } from '@testing-library/react';
import { useAuth } from '../useAuth';
import { createTestStore, mockUsers } from '../../test/utils';

describe('useAuth', () => {
  it('should return authenticated state', () => {
    const { result } = renderHook(() => useAuth(), {
      wrapper: ({ children }) => (
        <Provider store={createTestStore(createAuthenticatedState(mockUsers.admin))}>
          {children}
        </Provider>
      ),
    });

    expect(result.current.isAuthenticated).toBe(true);
    expect(result.current.user).toEqual(mockUsers.admin);
  });
});
```

## Best Practices

1. **Always use hooks for authentication state** - Don't access Redux store directly
2. **Implement proper error handling** - Handle network errors and token expiry
3. **Use permission guards for UI elements** - Hide/show features based on permissions
4. **Test authentication flows thoroughly** - Include edge cases and error scenarios
5. **Keep tokens secure** - Use the provided token storage utility
6. **Handle loading states** - Show appropriate loading indicators
7. **Implement proper logout** - Clear all user data and redirect appropriately

## Troubleshooting

### Common Issues

1. **Token not persisting** - Check localStorage/sessionStorage permissions
2. **Infinite refresh loops** - Verify token expiry logic
3. **Route protection not working** - Ensure ProtectedRoute is properly configured
4. **Permission checks failing** - Verify user role and permission mappings

### Debug Tools

```typescript
// Enable debug logging
localStorage.setItem('debug', 'fleetxq:auth');

// Check current auth state
console.log('Auth state:', store.getState().auth);

// Check token status
console.log('Token expired:', tokenStorage.isTokenExpired());
console.log('Needs refresh:', tokenStorage.needsRefresh());
```
