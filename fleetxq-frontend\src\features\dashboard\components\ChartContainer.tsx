import React, { ReactNode, useState } from 'react';
import { ArrowPathIcon, ArrowsPointingOutIcon } from '@heroicons/react/24/outline';
import LoadingSpinner from '../../../components/LoadingSpinner';

export interface ChartContainerProps {
  title: string;
  subtitle?: string;
  children: ReactNode;
  isLoading?: boolean;
  error?: string | null;
  onRefresh?: () => void;
  onFullscreen?: () => void;
  actions?: ReactNode;
  className?: string;
  height?: string | number;
  showRefresh?: boolean;
  showFullscreen?: boolean;
  refreshing?: boolean;
}

const ChartContainer: React.FC<ChartContainerProps> = ({
  title,
  subtitle,
  children,
  isLoading = false,
  error = null,
  onRefresh,
  onFullscreen,
  actions,
  className = '',
  height = 400,
  showRefresh = true,
  showFullscreen = true,
  refreshing = false,
}) => {
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    if (onRefresh && !isRefreshing && !refreshing) {
      setIsRefreshing(true);
      try {
        await onRefresh();
      } finally {
        setIsRefreshing(false);
      }
    }
  };

  const containerHeight = typeof height === 'number' ? `${height}px` : height;

  return (
    <div className={`card ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          {subtitle && (
            <p className="text-sm text-gray-600 mt-1">{subtitle}</p>
          )}
        </div>
        <div className="flex items-center space-x-2">
          {actions}
          {showRefresh && onRefresh && (
            <button
              onClick={handleRefresh}
              disabled={isLoading || isRefreshing || refreshing}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              title="Refresh chart data"
            >
              <ArrowPathIcon 
                className={`h-5 w-5 ${(isRefreshing || refreshing) ? 'animate-spin' : ''}`} 
              />
            </button>
          )}
          {showFullscreen && onFullscreen && (
            <button
              onClick={onFullscreen}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
              title="View fullscreen"
            >
              <ArrowsPointingOutIcon className="h-5 w-5" />
            </button>
          )}
        </div>
      </div>

      {/* Content */}
      <div 
        className="relative"
        style={{ height: containerHeight }}
      >
        {/* Loading State */}
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-10">
            <div className="text-center">
              <LoadingSpinner size="md" />
              <p className="mt-2 text-sm text-gray-600">Loading chart data...</p>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && !isLoading && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <div className="mx-auto h-12 w-12 text-gray-400">
                <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
              </div>
              <h3 className="mt-2 text-sm font-medium text-gray-900">Chart Error</h3>
              <p className="mt-1 text-sm text-gray-500">{error}</p>
              {onRefresh && (
                <button
                  onClick={handleRefresh}
                  className="mt-3 btn-primary text-sm"
                >
                  Try Again
                </button>
              )}
            </div>
          </div>
        )}

        {/* Chart Content */}
        {!error && (
          <div className={`h-full ${isLoading ? 'opacity-50' : ''}`}>
            {children}
          </div>
        )}
      </div>
    </div>
  );
};

export default ChartContainer;
