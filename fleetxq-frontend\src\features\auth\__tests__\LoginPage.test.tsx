import React from 'react';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import LoginPage from '../LoginPage';
import { renderWithProviders, createUnauthenticatedState } from '../../../test/utils';
import * as useAuthModule from '../../../hooks/useAuth';

// Mock the useAuth hook
const mockLogin = vi.fn();
const mockClearAuthError = vi.fn();

vi.mock('../../../hooks/useAuth', () => ({
  useAuth: vi.fn(),
}));

describe('LoginPage', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup default mock implementation
    vi.mocked(useAuthModule.useAuth).mockReturnValue({
      login: mockLogin,
      isLoading: false,
      error: null,
      clearAuthError: mockClearAuthError,
      user: null,
      isAuthenticated: false,
      auth: null,
      logout: vi.fn(),
      initialize: vi.fn(),
      hasRole: vi.fn(),
      hasAnyRole: vi.fn(),
    });
  });

  it('should render login form', () => {
    renderWithProviders(<LoginPage />, {
      preloadedState: createUnauthenticatedState(),
      initialEntries: ['/auth/login'],
    });

    expect(screen.getByText('Sign in to FleetXQ')).toBeInTheDocument();
    expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
  });

  it('should show validation errors for empty fields', async () => {
    renderWithProviders(<LoginPage />, {
      preloadedState: createUnauthenticatedState(),
      initialEntries: ['/auth/login'],
    });

    const submitButton = screen.getByRole('button', { name: /sign in/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Email is required')).toBeInTheDocument();
      expect(screen.getByText('Password is required')).toBeInTheDocument();
    });

    expect(mockLogin).not.toHaveBeenCalled();
  });

  it('should show validation error for invalid email', async () => {
    renderWithProviders(<LoginPage />, {
      preloadedState: createUnauthenticatedState(),
      initialEntries: ['/auth/login'],
    });

    const emailInput = screen.getByLabelText(/email address/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    await user.type(emailInput, 'invalid-email');
    await user.type(passwordInput, 'password123');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Please enter a valid email address')).toBeInTheDocument();
    });

    expect(mockLogin).not.toHaveBeenCalled();
  });

  it('should call login with valid credentials', async () => {
    mockLogin.mockResolvedValue({ success: true });

    renderWithProviders(<LoginPage />, {
      preloadedState: createUnauthenticatedState(),
      initialEntries: ['/auth/login'],
    });

    const emailInput = screen.getByLabelText(/email address/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'password123');
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
      });
    });
  });

  it('should show loading state during login', () => {
    vi.mocked(useAuthModule.useAuth).mockReturnValue({
      login: mockLogin,
      isLoading: true,
      error: null,
      clearAuthError: mockClearAuthError,
      user: null,
      isAuthenticated: false,
      auth: null,
      logout: vi.fn(),
      initialize: vi.fn(),
      hasRole: vi.fn(),
      hasAnyRole: vi.fn(),
    });

    renderWithProviders(<LoginPage />, {
      preloadedState: createUnauthenticatedState(),
      initialEntries: ['/auth/login'],
    });

    expect(screen.getByText('Signing in...')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /signing in/i })).toBeDisabled();
  });

  it('should show error message when login fails', async () => {
    mockLogin.mockResolvedValue({ success: false, error: 'Invalid credentials' });

    renderWithProviders(<LoginPage />, {
      preloadedState: createUnauthenticatedState(),
      initialEntries: ['/auth/login'],
    });

    const emailInput = screen.getByLabelText(/email address/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'wrongpassword');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Invalid credentials')).toBeInTheDocument();
    });
  });
});
