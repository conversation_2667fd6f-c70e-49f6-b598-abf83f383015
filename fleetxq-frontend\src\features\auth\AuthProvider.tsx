import React, { useEffect, useState } from 'react';
import { useAuth } from '../../hooks/useAuth';
import { useTokenRefresh } from '../../hooks/useTokenRefresh';
import { tokenStorage } from '../../utils/tokenStorage';
import LoadingSpinner from '../../components/LoadingSpinner';

interface AuthProviderProps {
  children: React.ReactNode;
}

const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [isInitializing, setIsInitializing] = useState(true);
  const [initializationError, setInitializationError] = useState<string | null>(null);
  const { initialize, isAuthenticated } = useAuth();
  const { refreshTokenNow, isTokenExpired, needsRefresh } = useTokenRefresh();

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        setIsInitializing(true);
        setInitializationError(null);

        // Check if we have stored tokens
        const storedToken = tokenStorage.getAccessToken();
        
        if (!storedToken) {
          // No stored token, user needs to login
          setIsInitializing(false);
          return;
        }

        // Check if token is expired
        if (isTokenExpired(storedToken)) {
          console.log('Stored token is expired, attempting refresh...');
          
          // Try to refresh the token
          const refreshResult = await refreshTokenNow();
          
          if (!refreshResult.success) {
            console.log('Token refresh failed, clearing storage');
            tokenStorage.clearTokens();
            setIsInitializing(false);
            return;
          }
        } else if (needsRefresh(storedToken)) {
          // Token is close to expiry, refresh proactively
          console.log('Token needs refresh, refreshing proactively...');
          refreshTokenNow(); // Don't await, let it happen in background
        }

        // Initialize authentication state
        const result = await initialize();
        
        if (!result.success) {
          console.error('Authentication initialization failed:', result.error);
          setInitializationError(result.error || 'Failed to initialize authentication');
          tokenStorage.clearTokens();
        }

      } catch (error) {
        console.error('Error during authentication initialization:', error);
        setInitializationError('An unexpected error occurred during initialization');
        tokenStorage.clearTokens();
      } finally {
        setIsInitializing(false);
      }
    };

    initializeAuth();
  }, [initialize, refreshTokenNow, isTokenExpired, needsRefresh]);

  // Show loading spinner during initialization
  if (isInitializing) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <LoadingSpinner />
          <p className="mt-4 text-gray-600">Initializing FleetXQ...</p>
        </div>
      </div>
    );
  }

  // Show error if initialization failed
  if (initializationError) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center max-w-md mx-auto">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <div className="flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-red-100 rounded-full">
              <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Initialization Error
            </h3>
            <p className="text-gray-600 mb-4">
              {initializationError}
            </p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

export default AuthProvider;
