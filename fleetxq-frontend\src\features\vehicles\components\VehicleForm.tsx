import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { useAppDispatch, useAppSelector } from '../../../store/hooks';
import {
  createVehicle,
  updateVehicle,
  selectVehiclesCreating,
  selectVehiclesUpdating,
  selectVehiclesError,
  clearError
} from '../vehiclesSlice';
import type { Vehicle, VehicleFormData, VehicleStatus } from '../../../types';
import LoadingSpinner from '../../../components/LoadingSpinner';

interface VehicleFormProps {
  vehicle?: Vehicle | null;
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (vehicle: Vehicle) => void;
}

// Validation schema
const vehicleSchema = yup.object({
  vehicleName: yup
    .string()
    .required('Vehicle name is required')
    .min(2, 'Vehicle name must be at least 2 characters')
    .max(100, 'Vehicle name must be less than 100 characters'),
  make: yup
    .string()
    .required('Make is required')
    .min(2, 'Make must be at least 2 characters')
    .max(50, 'Make must be less than 50 characters'),
  model: yup
    .string()
    .required('Model is required')
    .min(1, 'Model must be at least 1 character')
    .max(50, 'Model must be less than 50 characters'),
  year: yup
    .number()
    .required('Year is required')
    .min(1900, 'Year must be 1900 or later')
    .max(new Date().getFullYear() + 1, 'Year cannot be in the future'),
  licensePlate: yup
    .string()
    .required('License plate is required')
    .min(2, 'License plate must be at least 2 characters')
    .max(20, 'License plate must be less than 20 characters'),
  vin: yup
    .string()
    .required('VIN is required')
    .length(17, 'VIN must be exactly 17 characters')
    .matches(/^[A-HJ-NPR-Z0-9]{17}$/, 'VIN contains invalid characters'),
  vehicleType: yup
    .string()
    .required('Vehicle type is required')
    .min(2, 'Vehicle type must be at least 2 characters')
    .max(50, 'Vehicle type must be less than 50 characters'),
  brand: yup
    .string()
    .optional()
    .max(50, 'Brand must be less than 50 characters'),
  color: yup
    .string()
    .optional()
    .max(30, 'Color must be less than 30 characters'),
  fuelType: yup
    .string()
    .required('Fuel type is required')
    .oneOf(['Gasoline', 'Diesel', 'Electric', 'Hybrid'], 'Invalid fuel type'),
  fuelTankCapacity: yup
    .number()
    .optional()
    .min(1, 'Fuel tank capacity must be at least 1 liter')
    .max(1000, 'Fuel tank capacity must be less than 1000 liters'),
  status: yup
    .string()
    .required('Status is required')
    .oneOf(['active', 'maintenance', 'offline', 'retired'], 'Invalid status'),
  driverId: yup
    .string()
    .optional()
});

const VehicleForm: React.FC<VehicleFormProps> = ({
  vehicle,
  isOpen,
  onClose,
  onSuccess
}) => {
  const dispatch = useAppDispatch();
  const isCreating = useAppSelector(selectVehiclesCreating);
  const isUpdating = useAppSelector(selectVehiclesUpdating);
  const error = useAppSelector(selectVehiclesError);
  const [availableDrivers, setAvailableDrivers] = useState<Array<{ id: string; name: string }>>([]);

  const isLoading = isCreating || isUpdating;
  const isEditMode = !!vehicle;

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isDirty },
    setValue,
    watch
  } = useForm<VehicleFormData>({
    resolver: yupResolver(vehicleSchema),
    defaultValues: {
      vehicleName: '',
      make: '',
      model: '',
      year: new Date().getFullYear(),
      licensePlate: '',
      vin: '',
      vehicleType: '',
      brand: '',
      color: '',
      fuelType: 'Gasoline',
      fuelTankCapacity: undefined,
      status: 'active' as VehicleStatus,
      driverId: ''
    }
  });

  // Load form data when vehicle changes
  useEffect(() => {
    if (vehicle && isOpen) {
      reset({
        vehicleName: vehicle.vehicleName || '',
        make: vehicle.make || '',
        model: vehicle.model || '',
        year: vehicle.year || new Date().getFullYear(),
        licensePlate: vehicle.licensePlate || '',
        vin: vehicle.vin || '',
        vehicleType: vehicle.vehicleType || '',
        brand: vehicle.brand || '',
        color: vehicle.color || '',
        fuelType: vehicle.fuelType || 'Gasoline',
        fuelTankCapacity: vehicle.fuelTankCapacity,
        status: vehicle.status || 'active',
        driverId: vehicle.driverId || ''
      });
    } else if (!vehicle && isOpen) {
      reset({
        vehicleName: '',
        make: '',
        model: '',
        year: new Date().getFullYear(),
        licensePlate: '',
        vin: '',
        vehicleType: '',
        brand: '',
        color: '',
        fuelType: 'Gasoline',
        fuelTankCapacity: undefined,
        status: 'active',
        driverId: ''
      });
    }
  }, [vehicle, isOpen, reset]);

  // Clear errors when form opens
  useEffect(() => {
    if (isOpen) {
      dispatch(clearError());
    }
  }, [isOpen, dispatch]);

  // Mock available drivers - in real app, this would come from an API
  useEffect(() => {
    // This would be replaced with actual API call
    setAvailableDrivers([
      { id: '1', name: 'John Doe' },
      { id: '2', name: 'Jane Smith' },
      { id: '3', name: 'Mike Johnson' }
    ]);
  }, []);

  const onSubmit = async (data: VehicleFormData) => {
    try {
      let result;
      if (isEditMode && vehicle) {
        result = await dispatch(updateVehicle({ id: vehicle.id, data })).unwrap();
      } else {
        result = await dispatch(createVehicle(data)).unwrap();
      }
      
      onSuccess?.(result);
      onClose();
    } catch (error) {
      // Error is handled by Redux state
      console.error('Form submission error:', error);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      reset();
      dispatch(clearError());
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-25" onClick={handleClose} />
        
        <div className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">
              {isEditMode ? 'Edit Vehicle' : 'Add New Vehicle'}
            </h2>
            <button
              onClick={handleClose}
              disabled={isLoading}
              className="p-2 hover:bg-gray-100 rounded-lg disabled:opacity-50"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
            {/* Error display */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            )}

            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Vehicle Name *
                  </label>
                  <input
                    {...register('vehicleName')}
                    className="input-field"
                    placeholder="e.g., Fleet Vehicle 001"
                  />
                  {errors.vehicleName && (
                    <p className="mt-1 text-sm text-red-600">{errors.vehicleName.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Vehicle Type *
                  </label>
                  <select {...register('vehicleType')} className="input-field">
                    <option value="">Select type</option>
                    <option value="Car">Car</option>
                    <option value="Truck">Truck</option>
                    <option value="Van">Van</option>
                    <option value="Bus">Bus</option>
                    <option value="Motorcycle">Motorcycle</option>
                  </select>
                  {errors.vehicleType && (
                    <p className="mt-1 text-sm text-red-600">{errors.vehicleType.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Make *
                  </label>
                  <input
                    {...register('make')}
                    className="input-field"
                    placeholder="e.g., Ford"
                  />
                  {errors.make && (
                    <p className="mt-1 text-sm text-red-600">{errors.make.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Model *
                  </label>
                  <input
                    {...register('model')}
                    className="input-field"
                    placeholder="e.g., Transit"
                  />
                  {errors.model && (
                    <p className="mt-1 text-sm text-red-600">{errors.model.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Year *
                  </label>
                  <input
                    {...register('year', { valueAsNumber: true })}
                    type="number"
                    className="input-field"
                    min="1900"
                    max={new Date().getFullYear() + 1}
                  />
                  {errors.year && (
                    <p className="mt-1 text-sm text-red-600">{errors.year.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Brand
                  </label>
                  <input
                    {...register('brand')}
                    className="input-field"
                    placeholder="e.g., Ford"
                  />
                  {errors.brand && (
                    <p className="mt-1 text-sm text-red-600">{errors.brand.message}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Vehicle Details */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Vehicle Details</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    License Plate *
                  </label>
                  <input
                    {...register('licensePlate')}
                    className="input-field"
                    placeholder="e.g., ABC-123"
                  />
                  {errors.licensePlate && (
                    <p className="mt-1 text-sm text-red-600">{errors.licensePlate.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    VIN *
                  </label>
                  <input
                    {...register('vin')}
                    className="input-field"
                    placeholder="17-character VIN"
                    maxLength={17}
                  />
                  {errors.vin && (
                    <p className="mt-1 text-sm text-red-600">{errors.vin.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Color
                  </label>
                  <input
                    {...register('color')}
                    className="input-field"
                    placeholder="e.g., White"
                  />
                  {errors.color && (
                    <p className="mt-1 text-sm text-red-600">{errors.color.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Fuel Type *
                  </label>
                  <select {...register('fuelType')} className="input-field">
                    <option value="Gasoline">Gasoline</option>
                    <option value="Diesel">Diesel</option>
                    <option value="Electric">Electric</option>
                    <option value="Hybrid">Hybrid</option>
                  </select>
                  {errors.fuelType && (
                    <p className="mt-1 text-sm text-red-600">{errors.fuelType.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Fuel Tank Capacity (L)
                  </label>
                  <input
                    {...register('fuelTankCapacity', { valueAsNumber: true })}
                    type="number"
                    className="input-field"
                    placeholder="e.g., 60"
                    min="1"
                    max="1000"
                  />
                  {errors.fuelTankCapacity && (
                    <p className="mt-1 text-sm text-red-600">{errors.fuelTankCapacity.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Status *
                  </label>
                  <select {...register('status')} className="input-field">
                    <option value="active">Active</option>
                    <option value="maintenance">Maintenance</option>
                    <option value="offline">Offline</option>
                    <option value="retired">Retired</option>
                  </select>
                  {errors.status && (
                    <p className="mt-1 text-sm text-red-600">{errors.status.message}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Driver Assignment */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Driver Assignment</h3>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Assigned Driver
                </label>
                <select {...register('driverId')} className="input-field">
                  <option value="">No driver assigned</option>
                  {availableDrivers.map(driver => (
                    <option key={driver.id} value={driver.id}>
                      {driver.name}
                    </option>
                  ))}
                </select>
                {errors.driverId && (
                  <p className="mt-1 text-sm text-red-600">{errors.driverId.message}</p>
                )}
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={handleClose}
                disabled={isLoading}
                className="btn-secondary disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading || !isDirty}
                className="btn-primary disabled:opacity-50 flex items-center space-x-2"
              >
                {isLoading && <LoadingSpinner size="sm" />}
                <span>{isEditMode ? 'Update Vehicle' : 'Create Vehicle'}</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default VehicleForm;
