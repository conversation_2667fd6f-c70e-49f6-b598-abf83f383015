import { apiService } from './api';
import type {
  ApiResponse,
  PaginatedResponse,
  Alert,
  AlertQueryParams,
  AlertFilters,
  AlertAcknowledgment,
  AlertResolution,
  NotificationSettings,
} from '../types';

export class AlertService {
  private readonly baseUrl = '/alerts';

  /**
   * Get alerts with optional filters and pagination
   */
  async getAlerts(
    params?: AlertQueryParams
  ): Promise<PaginatedResponse<Alert>> {
    try {
      const queryParams = new URLSearchParams();

      if (params?.vehicleId) queryParams.append('vehicleId', params.vehicleId);
      if (params?.type) queryParams.append('type', params.type);
      if (params?.severity) queryParams.append('severity', params.severity);
      if (params?.acknowledged !== undefined) {
        queryParams.append('acknowledged', params.acknowledged.toString());
      }
      if (params?.startDate) queryParams.append('startDate', params.startDate);
      if (params?.endDate) queryParams.append('endDate', params.endDate);
      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.pageSize)
        queryParams.append('pageSize', params.pageSize.toString());

      const response = await apiService.get<PaginatedResponse<Alert>>(
        `${this.baseUrl}?${queryParams.toString()}`
      );

      return response.data;
    } catch (error) {
      console.error('Failed to get alerts:', error);
      throw error;
    }
  }

  /**
   * Get alert by ID
   */
  async getAlertById(id: string): Promise<ApiResponse<Alert>> {
    try {
      return await apiService.get<Alert>(`${this.baseUrl}/${id}`);
    } catch (error) {
      console.error(`Failed to get alert ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get active/unacknowledged alerts
   */
  async getActiveAlerts(
    vehicleId?: string,
    severity?: 'low' | 'medium' | 'high' | 'critical'
  ): Promise<ApiResponse<Alert[]>> {
    try {
      const params = new URLSearchParams({ acknowledged: 'false' });
      if (vehicleId) params.append('vehicleId', vehicleId);
      if (severity) params.append('severity', severity);

      return await apiService.get<Alert[]>(
        `${this.baseUrl}/active?${params.toString()}`
      );
    } catch (error) {
      console.error('Failed to get active alerts:', error);
      throw error;
    }
  }

  /**
   * Get critical alerts
   */
  async getCriticalAlerts(): Promise<ApiResponse<Alert[]>> {
    try {
      return await apiService.get<Alert[]>(`${this.baseUrl}/critical`);
    } catch (error) {
      console.error('Failed to get critical alerts:', error);
      throw error;
    }
  }

  /**
   * Acknowledge an alert
   */
  async acknowledgeAlert(
    acknowledgment: AlertAcknowledgment
  ): Promise<ApiResponse<Alert>> {
    try {
      return await apiService.post<Alert>(
        `${this.baseUrl}/${acknowledgment.alertId}/acknowledge`,
        acknowledgment
      );
    } catch (error) {
      console.error(
        `Failed to acknowledge alert ${acknowledgment.alertId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Bulk acknowledge alerts
   */
  async bulkAcknowledgeAlerts(
    alertIds: string[],
    userId: string,
    notes?: string
  ): Promise<ApiResponse<Alert[]>> {
    try {
      return await apiService.post<Alert[]>(
        `${this.baseUrl}/bulk-acknowledge`,
        {
          alertIds,
          userId,
          notes,
        }
      );
    } catch (error) {
      console.error('Failed to bulk acknowledge alerts:', error);
      throw error;
    }
  }

  /**
   * Resolve an alert
   */
  async resolveAlert(resolution: AlertResolution): Promise<ApiResponse<Alert>> {
    try {
      return await apiService.post<Alert>(
        `${this.baseUrl}/${resolution.alertId}/resolve`,
        resolution
      );
    } catch (error) {
      console.error(`Failed to resolve alert ${resolution.alertId}:`, error);
      throw error;
    }
  }

  /**
   * Dismiss an alert
   */
  async dismissAlert(
    alertId: string,
    userId: string,
    reason?: string
  ): Promise<ApiResponse<Alert>> {
    try {
      return await apiService.post<Alert>(
        `${this.baseUrl}/${alertId}/dismiss`,
        {
          userId,
          reason,
        }
      );
    } catch (error) {
      console.error(`Failed to dismiss alert ${alertId}:`, error);
      throw error;
    }
  }

  /**
   * Get alert statistics
   */
  async getAlertStatistics(
    startDate?: string,
    endDate?: string,
    vehicleId?: string
  ): Promise<ApiResponse<any>> {
    try {
      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);
      if (vehicleId) params.append('vehicleId', vehicleId);

      return await apiService.get<any>(
        `${this.baseUrl}/statistics?${params.toString()}`
      );
    } catch (error) {
      console.error('Failed to get alert statistics:', error);
      throw error;
    }
  }

  /**
   * Get alert trends
   */
  async getAlertTrends(
    period: 'day' | 'week' | 'month' = 'week',
    vehicleId?: string
  ): Promise<ApiResponse<any[]>> {
    try {
      const params = new URLSearchParams({ period });
      if (vehicleId) params.append('vehicleId', vehicleId);

      return await apiService.get<any[]>(
        `${this.baseUrl}/trends?${params.toString()}`
      );
    } catch (error) {
      console.error('Failed to get alert trends:', error);
      throw error;
    }
  }

  /**
   * Get alert history for a specific vehicle
   */
  async getVehicleAlertHistory(
    vehicleId: string,
    startDate?: string,
    endDate?: string,
    page: number = 1,
    pageSize: number = 10
  ): Promise<PaginatedResponse<Alert>> {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
      });

      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      const response = await apiService.get<PaginatedResponse<Alert>>(
        `${this.baseUrl}/vehicle/${vehicleId}/history?${params.toString()}`
      );

      return response.data;
    } catch (error) {
      console.error(
        `Failed to get alert history for vehicle ${vehicleId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Create custom alert rule
   */
  async createAlertRule(ruleData: {
    name: string;
    description: string;
    conditions: any;
    severity: 'low' | 'medium' | 'high' | 'critical';
    enabled: boolean;
    vehicleIds?: string[];
  }): Promise<ApiResponse<any>> {
    try {
      return await apiService.post<any>(`${this.baseUrl}/rules`, ruleData);
    } catch (error) {
      console.error('Failed to create alert rule:', error);
      throw error;
    }
  }

  /**
   * Get alert rules
   */
  async getAlertRules(
    page: number = 1,
    pageSize: number = 10
  ): Promise<PaginatedResponse<any>> {
    try {
      const response = await apiService.get<PaginatedResponse<any>>(
        `${this.baseUrl}/rules?page=${page}&pageSize=${pageSize}`
      );
      return response.data;
    } catch (error) {
      console.error('Failed to get alert rules:', error);
      throw error;
    }
  }

  /**
   * Update alert rule
   */
  async updateAlertRule(
    ruleId: string,
    ruleData: any
  ): Promise<ApiResponse<any>> {
    try {
      return await apiService.put<any>(
        `${this.baseUrl}/rules/${ruleId}`,
        ruleData
      );
    } catch (error) {
      console.error(`Failed to update alert rule ${ruleId}:`, error);
      throw error;
    }
  }

  /**
   * Delete alert rule
   */
  async deleteAlertRule(ruleId: string): Promise<ApiResponse<void>> {
    try {
      return await apiService.delete<void>(`${this.baseUrl}/rules/${ruleId}`);
    } catch (error) {
      console.error(`Failed to delete alert rule ${ruleId}:`, error);
      throw error;
    }
  }

  /**
   * Get notification settings
   */
  async getNotificationSettings(): Promise<ApiResponse<NotificationSettings>> {
    try {
      return await apiService.get<NotificationSettings>(
        `${this.baseUrl}/notification-settings`
      );
    } catch (error) {
      console.error('Failed to get notification settings:', error);
      throw error;
    }
  }

  /**
   * Update notification settings
   */
  async updateNotificationSettings(
    settings: NotificationSettings
  ): Promise<ApiResponse<NotificationSettings>> {
    try {
      return await apiService.put<NotificationSettings>(
        `${this.baseUrl}/notification-settings`,
        settings
      );
    } catch (error) {
      console.error('Failed to update notification settings:', error);
      throw error;
    }
  }

  /**
   * Export alerts data
   */
  async exportAlerts(
    format: 'csv' | 'xlsx' = 'csv',
    filters?: AlertFilters
  ): Promise<Blob> {
    try {
      const params = new URLSearchParams({ format });

      if (filters?.vehicleId) params.append('vehicleId', filters.vehicleId);
      if (filters?.type) params.append('type', filters.type);
      if (filters?.severity) params.append('severity', filters.severity);
      if (filters?.acknowledged !== undefined) {
        params.append('acknowledged', filters.acknowledged.toString());
      }
      if (filters?.startDate) params.append('startDate', filters.startDate);
      if (filters?.endDate) params.append('endDate', filters.endDate);

      const response = await apiService.get<Blob>(
        `${this.baseUrl}/export?${params.toString()}`,
        { responseType: 'blob' }
      );

      return response.data;
    } catch (error) {
      console.error('Failed to export alerts:', error);
      throw error;
    }
  }

  /**
   * Test alert notification
   */
  async testNotification(
    type: 'email' | 'sms' | 'push',
    message: string
  ): Promise<ApiResponse<void>> {
    try {
      return await apiService.post<void>(`${this.baseUrl}/test-notification`, {
        type,
        message,
      });
    } catch (error) {
      console.error('Failed to test notification:', error);
      throw error;
    }
  }
}

// Create and export singleton instance
export const alertService = new AlertService();
export default alertService;
