import { apiService } from './api';
import type {
  ApiResponse,
  PaginatedResponse,
  Vehicle,
  VehicleFormData,
  VehicleSearchFilters,
  VehicleBulkOperation,
  VehicleAnalytics,
  VehicleMaintenanceAlert,
  VehicleAssignmentHistory,
  VehicleMaintenanceRecord,
  VehicleRealTimeUpdate,
  VehicleStatus,
  TelemetryData
} from '../types';

export class VehicleService {
  private readonly baseUrl = '/vehicles';

  /**
   * Get all vehicles with optional filters and pagination
   */
  async getVehicles(
    filters?: VehicleSearchFilters,
    page: number = 1,
    pageSize: number = 10
  ): Promise<PaginatedResponse<Vehicle>> {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
      });

      // Enhanced filter support
      if (filters?.status) {
        params.append('status', filters.status);
      }
      if (filters?.search) {
        params.append('search', filters.search);
      }
      if (filters?.vehicleType) {
        params.append('vehicleType', filters.vehicleType);
      }
      if (filters?.fuelType) {
        params.append('fuelType', filters.fuelType);
      }
      if (filters?.driverId) {
        params.append('driverId', filters.driverId);
      }
      if (filters?.needsMaintenance !== undefined) {
        params.append('needsMaintenance', filters.needsMaintenance.toString());
      }
      if (filters?.isAvailable !== undefined) {
        params.append('isAvailable', filters.isAvailable.toString());
      }
      if (filters?.realTimeStatus) {
        params.append('realTimeStatus', filters.realTimeStatus);
      }
      if (filters?.sortBy) {
        params.append('sortBy', filters.sortBy);
      }
      if (filters?.sortOrder) {
        params.append('sortOrder', filters.sortOrder);
      }
      if (filters?.dateRange) {
        params.append('startDate', filters.dateRange.startDate);
        params.append('endDate', filters.dateRange.endDate);
      }

      const response = await apiService.get<PaginatedResponse<Vehicle>>(
        `${this.baseUrl}?${params.toString()}`
      );

      return response.data;
    } catch (error) {
      console.error('Failed to get vehicles:', error);
      throw error;
    }
  }

  /**
   * Get vehicle by ID
   */
  async getVehicleById(id: string): Promise<ApiResponse<Vehicle>> {
    try {
      return await apiService.get<Vehicle>(`${this.baseUrl}/${id}`);
    } catch (error) {
      console.error(`Failed to get vehicle ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create new vehicle
   */
  async createVehicle(
    vehicleData: VehicleFormData
  ): Promise<ApiResponse<Vehicle>> {
    try {
      return await apiService.post<Vehicle>(`${this.baseUrl}`, vehicleData);
    } catch (error) {
      console.error('Failed to create vehicle:', error);
      throw error;
    }
  }

  /**
   * Update existing vehicle
   */
  async updateVehicle(
    id: string,
    vehicleData: Partial<VehicleFormData>
  ): Promise<ApiResponse<Vehicle>> {
    try {
      return await apiService.put<Vehicle>(
        `${this.baseUrl}/${id}`,
        vehicleData
      );
    } catch (error) {
      console.error(`Failed to update vehicle ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete vehicle
   */
  async deleteVehicle(id: string): Promise<ApiResponse<void>> {
    try {
      return await apiService.delete<void>(`${this.baseUrl}/${id}`);
    } catch (error) {
      console.error(`Failed to delete vehicle ${id}:`, error);
      throw error;
    }
  }

  /**
   * Update vehicle status
   */
  async updateVehicleStatus(
    id: string,
    status: VehicleStatus,
    reason?: string
  ): Promise<ApiResponse<Vehicle>> {
    try {
      return await apiService.patch<Vehicle>(`${this.baseUrl}/${id}/status`, {
        status,
        reason,
      });
    } catch (error) {
      console.error(`Failed to update vehicle ${id} status:`, error);
      throw error;
    }
  }

  /**
   * Assign driver to vehicle
   */
  async assignDriver(
    vehicleId: string,
    driverId: string,
    reason?: string
  ): Promise<ApiResponse<Vehicle>> {
    try {
      return await apiService.post<Vehicle>(
        `${this.baseUrl}/${vehicleId}/assign-driver`,
        {
          driverId,
          reason,
        }
      );
    } catch (error) {
      console.error(`Failed to assign driver to vehicle ${vehicleId}:`, error);
      throw error;
    }
  }

  /**
   * Unassign driver from vehicle
   */
  async unassignDriver(
    vehicleId: string,
    reason?: string
  ): Promise<ApiResponse<Vehicle>> {
    try {
      return await apiService.post<Vehicle>(
        `${this.baseUrl}/${vehicleId}/unassign-driver`,
        { reason }
      );
    } catch (error) {
      console.error(
        `Failed to unassign driver from vehicle ${vehicleId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Get vehicle maintenance history
   */
  async getMaintenanceHistory(
    vehicleId: string,
    page: number = 1,
    pageSize: number = 10
  ): Promise<PaginatedResponse<any>> {
    try {
      const response = await apiService.get<PaginatedResponse<any>>(
        `${this.baseUrl}/${vehicleId}/maintenance?page=${page}&pageSize=${pageSize}`
      );
      return response.data;
    } catch (error) {
      console.error(
        `Failed to get maintenance history for vehicle ${vehicleId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Schedule maintenance for vehicle
   */
  async scheduleMaintenance(
    vehicleId: string,
    maintenanceData: {
      type: string;
      description: string;
      scheduledDate: string;
      estimatedCost?: number;
      notes?: string;
    }
  ): Promise<ApiResponse<any>> {
    try {
      return await apiService.post<any>(
        `${this.baseUrl}/${vehicleId}/maintenance`,
        maintenanceData
      );
    } catch (error) {
      console.error(
        `Failed to schedule maintenance for vehicle ${vehicleId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Get vehicle location history
   */
  async getLocationHistory(
    vehicleId: string,
    startDate?: string,
    endDate?: string,
    page: number = 1,
    pageSize: number = 100
  ): Promise<PaginatedResponse<any>> {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
      });

      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      const response = await apiService.get<PaginatedResponse<any>>(
        `${this.baseUrl}/${vehicleId}/location-history?${params.toString()}`
      );
      return response.data;
    } catch (error) {
      console.error(
        `Failed to get location history for vehicle ${vehicleId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Get current vehicle location
   */
  async getCurrentLocation(vehicleId: string): Promise<ApiResponse<any>> {
    try {
      return await apiService.get<any>(
        `${this.baseUrl}/${vehicleId}/current-location`
      );
    } catch (error) {
      console.error(
        `Failed to get current location for vehicle ${vehicleId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Get vehicle statistics
   */
  async getVehicleStats(
    vehicleId: string,
    period: 'day' | 'week' | 'month' = 'week'
  ): Promise<ApiResponse<any>> {
    try {
      return await apiService.get<any>(
        `${this.baseUrl}/${vehicleId}/stats?period=${period}`
      );
    } catch (error) {
      console.error(`Failed to get stats for vehicle ${vehicleId}:`, error);
      throw error;
    }
  }

  /**
   * Get all available vehicles (not assigned to drivers)
   */
  async getAvailableVehicles(): Promise<ApiResponse<Vehicle[]>> {
    try {
      return await apiService.get<Vehicle[]>(`${this.baseUrl}/available`);
    } catch (error) {
      console.error('Failed to get available vehicles:', error);
      throw error;
    }
  }

  /**
   * Get vehicle analytics
   */
  async getVehicleAnalytics(
    vehicleId: string,
    timeRange?: '7d' | '30d' | '90d' | '1y'
  ): Promise<ApiResponse<VehicleAnalytics>> {
    try {
      const params = timeRange ? `?timeRange=${timeRange}` : '';
      return await apiService.get<VehicleAnalytics>(
        `${this.baseUrl}/${vehicleId}/analytics${params}`
      );
    } catch (error) {
      console.error(`Failed to get analytics for vehicle ${vehicleId}:`, error);
      throw error;
    }
  }

  /**
   * Get maintenance alerts for vehicle or all vehicles
   */
  async getMaintenanceAlerts(
    vehicleId?: string
  ): Promise<ApiResponse<VehicleMaintenanceAlert[]>> {
    try {
      const url = vehicleId
        ? `${this.baseUrl}/${vehicleId}/maintenance-alerts`
        : `${this.baseUrl}/maintenance-alerts`;
      return await apiService.get<VehicleMaintenanceAlert[]>(url);
    } catch (error) {
      console.error('Failed to get maintenance alerts:', error);
      throw error;
    }
  }

  /**
   * Get assignment history for vehicle
   */
  async getAssignmentHistory(
    vehicleId: string,
    page: number = 1,
    pageSize: number = 50
  ): Promise<PaginatedResponse<VehicleAssignmentHistory>> {
    try {
      const response = await apiService.get<PaginatedResponse<VehicleAssignmentHistory>>(
        `${this.baseUrl}/${vehicleId}/assignment-history?page=${page}&pageSize=${pageSize}`
      );
      return response.data;
    } catch (error) {
      console.error(`Failed to get assignment history for vehicle ${vehicleId}:`, error);
      throw error;
    }
  }

  /**
   * Get maintenance history for vehicle
   */
  async getMaintenanceHistory(
    vehicleId: string,
    page: number = 1,
    pageSize: number = 50
  ): Promise<PaginatedResponse<VehicleMaintenanceRecord>> {
    try {
      const response = await apiService.get<PaginatedResponse<VehicleMaintenanceRecord>>(
        `${this.baseUrl}/${vehicleId}/maintenance-history?page=${page}&pageSize=${pageSize}`
      );
      return response.data;
    } catch (error) {
      console.error(`Failed to get maintenance history for vehicle ${vehicleId}:`, error);
      throw error;
    }
  }

  /**
   * Perform bulk operation on vehicles
   */
  async performBulkOperation(
    operation: VehicleBulkOperation
  ): Promise<ApiResponse<Vehicle[]>> {
    try {
      return await apiService.post<Vehicle[]>(
        `${this.baseUrl}/bulk-operation`,
        operation
      );
    } catch (error) {
      console.error('Failed to perform bulk operation:', error);
      throw error;
    }
  }

  /**
   * Get real-time vehicle updates
   */
  async getRealTimeUpdates(
    vehicleIds?: string[],
    limit: number = 100
  ): Promise<ApiResponse<VehicleRealTimeUpdate[]>> {
    try {
      const params = new URLSearchParams({ limit: limit.toString() });
      if (vehicleIds && vehicleIds.length > 0) {
        vehicleIds.forEach(id => params.append('vehicleIds', id));
      }

      return await apiService.get<VehicleRealTimeUpdate[]>(
        `${this.baseUrl}/real-time-updates?${params.toString()}`
      );
    } catch (error) {
      console.error('Failed to get real-time updates:', error);
      throw error;
    }
  }

  /**
   * Get vehicle telemetry data
   */
  async getTelemetryData(
    vehicleId: string,
    startDate?: string,
    endDate?: string,
    limit: number = 1000
  ): Promise<ApiResponse<TelemetryData[]>> {
    try {
      const params = new URLSearchParams({ limit: limit.toString() });
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      return await apiService.get<TelemetryData[]>(
        `${this.baseUrl}/${vehicleId}/telemetry?${params.toString()}`
      );
    } catch (error) {
      console.error(`Failed to get telemetry data for vehicle ${vehicleId}:`, error);
      throw error;
    }
  }

  /**
   * Bulk update vehicle statuses
   */
  async bulkUpdateStatus(
    vehicleIds: string[],
    status: VehicleStatus
  ): Promise<ApiResponse<Vehicle[]>> {
    try {
      return await apiService.post<Vehicle[]>(
        `${this.baseUrl}/bulk-update-status`,
        {
          vehicleIds,
          status,
        }
      );
    } catch (error) {
      console.error('Failed to bulk update vehicle statuses:', error);
      throw error;
    }
  }

  /**
   * Export vehicles data
   */
  async exportVehicles(
    format: 'csv' | 'xlsx' = 'csv',
    filters?: VehicleSearchFilters
  ): Promise<Blob> {
    try {
      const params = new URLSearchParams({ format });

      if (filters?.status) params.append('status', filters.status);
      if (filters?.search) params.append('search', filters.search);
      if (filters?.vehicleType) params.append('vehicleType', filters.vehicleType);
      if (filters?.fuelType) params.append('fuelType', filters.fuelType);
      if (filters?.driverId) params.append('driverId', filters.driverId);
      if (filters?.needsMaintenance !== undefined) {
        params.append('needsMaintenance', filters.needsMaintenance.toString());
      }
      if (filters?.isAvailable !== undefined) {
        params.append('isAvailable', filters.isAvailable.toString());
      }

      const response = await apiService.get<Blob>(
        `${this.baseUrl}/export?${params.toString()}`,
        { responseType: 'blob' }
      );

      return response.data;
    } catch (error) {
      console.error('Failed to export vehicles:', error);
      throw error;
    }
  }
}

// Create and export singleton instance
export const vehicleService = new VehicleService();
export default vehicleService;
