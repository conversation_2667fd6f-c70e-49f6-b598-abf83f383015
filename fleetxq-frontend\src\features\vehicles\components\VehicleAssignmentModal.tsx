import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { 
  XMarkIcon, 
  ExclamationTriangleIcon,
  UserIcon,
  TruckIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { useAppDispatch, useAppSelector } from '../../../store/hooks';
import {
  assignDriverToVehicle,
  unassignDriverFromVehicle,
  selectVehiclesUpdating,
  selectVehiclesError,
  selectAssignmentConflicts,
  clearAssignmentConflicts,
  clearError
} from '../vehiclesSlice';
import type { Vehicle, VehicleAssignmentConflict } from '../../../types';
import LoadingSpinner from '../../../components/LoadingSpinner';

interface VehicleAssignmentModalProps {
  vehicle: Vehicle | null;
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (vehicle: Vehicle) => void;
}

interface AssignmentFormData {
  driverId: string;
  reason?: string;
}

// Mock driver data - in real app, this would come from an API
const mockDrivers = [
  { id: '1', name: '<PERSON>', email: '<EMAIL>', status: 'active', currentVehicleId: null },
  { id: '2', name: 'Jane Smith', email: '<EMAIL>', status: 'active', currentVehicleId: 'vehicle-2' },
  { id: '3', name: 'Mike Johnson', email: '<EMAIL>', status: 'active', currentVehicleId: null },
  { id: '4', name: 'Sarah Wilson', email: '<EMAIL>', status: 'inactive', currentVehicleId: null },
  { id: '5', name: 'David Brown', email: '<EMAIL>', status: 'active', currentVehicleId: null },
];

const assignmentSchema = yup.object({
  driverId: yup
    .string()
    .required('Please select a driver'),
  reason: yup
    .string()
    .optional()
    .max(200, 'Reason must be less than 200 characters')
});

const VehicleAssignmentModal: React.FC<VehicleAssignmentModalProps> = ({
  vehicle,
  isOpen,
  onClose,
  onSuccess
}) => {
  const dispatch = useAppDispatch();
  const isUpdating = useAppSelector(selectVehiclesUpdating);
  const error = useAppSelector(selectVehiclesError);
  const assignmentConflicts = useAppSelector(selectAssignmentConflicts);

  const [availableDrivers, setAvailableDrivers] = useState(mockDrivers);
  const [showConflictResolution, setShowConflictResolution] = useState(false);

  const {
    register,
    handleSubmit,
    reset,
    watch,
    formState: { errors, isDirty }
  } = useForm<AssignmentFormData>({
    resolver: yupResolver(assignmentSchema),
    defaultValues: {
      driverId: vehicle?.driverId || '',
      reason: ''
    }
  });

  const selectedDriverId = watch('driverId');
  const selectedDriver = availableDrivers.find(d => d.id === selectedDriverId);

  // Reset form when vehicle changes
  useEffect(() => {
    if (vehicle && isOpen) {
      reset({
        driverId: vehicle.driverId || '',
        reason: ''
      });
    }
  }, [vehicle, isOpen, reset]);

  // Clear errors when modal opens
  useEffect(() => {
    if (isOpen) {
      dispatch(clearError());
      dispatch(clearAssignmentConflicts());
    }
  }, [isOpen, dispatch]);

  // Check for assignment conflicts when driver is selected
  useEffect(() => {
    if (selectedDriverId && selectedDriver && vehicle) {
      const conflicts: VehicleAssignmentConflict[] = [];

      // Check if driver is already assigned to another vehicle
      if (selectedDriver.currentVehicleId && selectedDriver.currentVehicleId !== vehicle.id) {
        conflicts.push({
          type: 'driver_already_assigned',
          message: `${selectedDriver.name} is already assigned to another vehicle`,
          conflictingVehicleId: selectedDriver.currentVehicleId,
          suggestions: ['Unassign driver from current vehicle first', 'Choose a different driver']
        });
      }

      // Check if driver is inactive
      if (selectedDriver.status === 'inactive') {
        conflicts.push({
          type: 'driver_inactive',
          message: `${selectedDriver.name} is currently inactive`,
          suggestions: ['Activate the driver first', 'Choose an active driver']
        });
      }

      // Check if vehicle is in maintenance
      if (vehicle.status === 'maintenance') {
        conflicts.push({
          type: 'vehicle_maintenance',
          message: 'Vehicle is currently under maintenance',
          suggestions: ['Complete maintenance first', 'Choose a different vehicle']
        });
      }

      if (conflicts.length > 0) {
        // In real app, this would be set through Redux action
        setShowConflictResolution(true);
      } else {
        setShowConflictResolution(false);
      }
    }
  }, [selectedDriverId, selectedDriver, vehicle]);

  const onSubmit = async (data: AssignmentFormData) => {
    if (!vehicle) return;

    try {
      if (data.driverId) {
        // Assign driver
        const result = await dispatch(assignDriverToVehicle({
          vehicleId: vehicle.id,
          driverId: data.driverId,
          reason: data.reason
        })).unwrap();
        onSuccess?.(result);
      } else {
        // Unassign current driver
        const result = await dispatch(unassignDriverFromVehicle({
          vehicleId: vehicle.id,
          reason: data.reason
        })).unwrap();
        onSuccess?.(result);
      }
      handleClose();
    } catch (error) {
      console.error('Assignment failed:', error);
    }
  };

  const handleClose = () => {
    if (!isUpdating) {
      reset();
      dispatch(clearError());
      dispatch(clearAssignmentConflicts());
      setShowConflictResolution(false);
      onClose();
    }
  };

  const handleUnassign = async () => {
    if (!vehicle) return;

    try {
      const result = await dispatch(unassignDriverFromVehicle({
        vehicleId: vehicle.id,
        reason: 'Manual unassignment'
      })).unwrap();
      onSuccess?.(result);
      handleClose();
    } catch (error) {
      console.error('Unassignment failed:', error);
    }
  };

  if (!isOpen || !vehicle) return null;

  const currentDriver = vehicle.driverId ? 
    availableDrivers.find(d => d.id === vehicle.driverId) : null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-25" onClick={handleClose} />
        
        <div className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <UserIcon className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  Driver Assignment
                </h2>
                <p className="text-sm text-gray-600">
                  {vehicle.vehicleName || `${vehicle.year} ${vehicle.make} ${vehicle.model}`} • {vehicle.licensePlate}
                </p>
              </div>
            </div>
            <button
              onClick={handleClose}
              disabled={isUpdating}
              className="p-2 hover:bg-gray-100 rounded-lg disabled:opacity-50"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>

          {/* Current Assignment */}
          {currentDriver && (
            <div className="p-6 bg-blue-50 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <UserIcon className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">Currently Assigned</p>
                    <p className="text-lg font-semibold text-blue-900">{currentDriver.name}</p>
                    <p className="text-sm text-gray-600">{currentDriver.email}</p>
                  </div>
                </div>
                <button
                  onClick={handleUnassign}
                  disabled={isUpdating}
                  className="btn-secondary text-sm disabled:opacity-50"
                >
                  {isUpdating ? <LoadingSpinner size="sm" /> : 'Unassign'}
                </button>
              </div>
            </div>
          )}

          {/* Form */}
          <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
            {/* Error display */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            )}

            {/* Conflict Resolution */}
            {showConflictResolution && assignmentConflicts.length > 0 && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-start space-x-2">
                  <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 mt-0.5" />
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-yellow-800 mb-2">Assignment Conflicts Detected</h4>
                    {assignmentConflicts.map((conflict, index) => (
                      <div key={index} className="mb-3 last:mb-0">
                        <p className="text-sm text-yellow-700 mb-1">{conflict.message}</p>
                        {conflict.suggestions && (
                          <ul className="text-xs text-yellow-600 ml-4 list-disc">
                            {conflict.suggestions.map((suggestion, i) => (
                              <li key={i}>{suggestion}</li>
                            ))}
                          </ul>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Driver Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Driver
              </label>
              <select
                {...register('driverId')}
                className="input-field"
                disabled={isUpdating}
              >
                <option value="">No driver assigned</option>
                {availableDrivers.map(driver => (
                  <option 
                    key={driver.id} 
                    value={driver.id}
                    disabled={driver.status === 'inactive'}
                  >
                    {driver.name} ({driver.email}) 
                    {driver.status === 'inactive' && ' - Inactive'}
                    {driver.currentVehicleId && driver.currentVehicleId !== vehicle.id && ' - Already Assigned'}
                  </option>
                ))}
              </select>
              {errors.driverId && (
                <p className="mt-1 text-sm text-red-600">{errors.driverId.message}</p>
              )}
            </div>

            {/* Selected Driver Info */}
            {selectedDriver && (
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-900 mb-2">Driver Information</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Name:</span>
                    <span className="ml-2 text-gray-900">{selectedDriver.name}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Email:</span>
                    <span className="ml-2 text-gray-900">{selectedDriver.email}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Status:</span>
                    <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${
                      selectedDriver.status === 'active' ? 
                        'bg-green-100 text-green-800' : 
                        'bg-red-100 text-red-800'
                    }`}>
                      {selectedDriver.status}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-500">Current Assignment:</span>
                    <span className="ml-2 text-gray-900">
                      {selectedDriver.currentVehicleId ? 'Assigned' : 'Available'}
                    </span>
                  </div>
                </div>
              </div>
            )}

            {/* Reason */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Reason (Optional)
              </label>
              <textarea
                {...register('reason')}
                rows={3}
                className="input-field"
                placeholder="Enter reason for assignment change..."
                disabled={isUpdating}
              />
              {errors.reason && (
                <p className="mt-1 text-sm text-red-600">{errors.reason.message}</p>
              )}
            </div>

            {/* Form Actions */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={handleClose}
                disabled={isUpdating}
                className="btn-secondary disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isUpdating || !isDirty || (showConflictResolution && assignmentConflicts.length > 0)}
                className="btn-primary disabled:opacity-50 flex items-center space-x-2"
              >
                {isUpdating && <LoadingSpinner size="sm" />}
                <span>
                  {selectedDriverId ? 'Assign Driver' : 'Unassign Driver'}
                </span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default VehicleAssignmentModal;
