import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import type { Driver, PaginatedResponse } from '../../types';
import { apiService } from '../../services/api';

// Driver creation/update interface
interface DriverFormData {
  name: string;
  email: string;
  phone: string;
  licenseNumber: string;
  status: 'active' | 'inactive';
  vehicleId?: string;
}

// Driver assignment interface
interface DriverAssignment {
  driverId: string;
  vehicleId: string;
}

// Drivers state interface
interface DriversState {
  drivers: Driver[];
  currentDriver: Driver | null;
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  isAssigning: boolean;
  error: string | null;
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
  filters: {
    status?: 'active' | 'inactive';
    search?: string;
    vehicleId?: string;
  };
}

const initialState: DriversState = {
  drivers: [],
  currentDriver: null,
  isLoading: false,
  isCreating: false,
  isUpdating: false,
  isDeleting: false,
  isAssigning: false,
  error: null,
  pagination: {
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0,
  },
  filters: {},
};

// Async thunks
export const fetchDrivers = createAsyncThunk(
  'drivers/fetchDrivers',
  async (
    params: {
      page?: number;
      pageSize?: number;
      status?: string;
      search?: string;
      vehicleId?: string;
    } = {},
    { rejectWithValue }
  ) => {
    try {
      const { page = 1, pageSize = 10, status, search, vehicleId } = params;
      let url = `/drivers?page=${page}&pageSize=${pageSize}`;

      if (status) url += `&status=${status}`;
      if (search) url += `&search=${encodeURIComponent(search)}`;
      if (vehicleId) url += `&vehicleId=${vehicleId}`;

      const response = await apiService.get<PaginatedResponse<Driver>>(url);
      return response.data;
    } catch (error: any) {
      const message =
        error.response?.data?.message || 'Failed to fetch drivers';
      return rejectWithValue(message);
    }
  }
);

export const fetchDriverById = createAsyncThunk(
  'drivers/fetchDriverById',
  async (driverId: string, { rejectWithValue }) => {
    try {
      const response = await apiService.get<Driver>(`/drivers/${driverId}`);
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch driver';
      return rejectWithValue(message);
    }
  }
);

export const createDriver = createAsyncThunk(
  'drivers/createDriver',
  async (driverData: DriverFormData, { rejectWithValue }) => {
    try {
      const response = await apiService.post<Driver>('/drivers', driverData);
      return response.data;
    } catch (error: any) {
      const message =
        error.response?.data?.message || 'Failed to create driver';
      return rejectWithValue(message);
    }
  }
);

export const updateDriver = createAsyncThunk(
  'drivers/updateDriver',
  async (
    { id, data }: { id: string; data: Partial<DriverFormData> },
    { rejectWithValue }
  ) => {
    try {
      const response = await apiService.put<Driver>(`/drivers/${id}`, data);
      return response.data;
    } catch (error: any) {
      const message =
        error.response?.data?.message || 'Failed to update driver';
      return rejectWithValue(message);
    }
  }
);

export const deleteDriver = createAsyncThunk(
  'drivers/deleteDriver',
  async (driverId: string, { rejectWithValue }) => {
    try {
      await apiService.delete(`/drivers/${driverId}`);
      return driverId;
    } catch (error: any) {
      const message =
        error.response?.data?.message || 'Failed to delete driver';
      return rejectWithValue(message);
    }
  }
);

export const assignDriverToVehicle = createAsyncThunk(
  'drivers/assignDriverToVehicle',
  async ({ driverId, vehicleId }: DriverAssignment, { rejectWithValue }) => {
    try {
      const response = await apiService.post<Driver>(
        `/drivers/${driverId}/assign`,
        { vehicleId }
      );
      return response.data;
    } catch (error: any) {
      const message =
        error.response?.data?.message || 'Failed to assign driver to vehicle';
      return rejectWithValue(message);
    }
  }
);

export const unassignDriverFromVehicle = createAsyncThunk(
  'drivers/unassignDriverFromVehicle',
  async (driverId: string, { rejectWithValue }) => {
    try {
      const response = await apiService.post<Driver>(
        `/drivers/${driverId}/unassign`
      );
      return response.data;
    } catch (error: any) {
      const message =
        error.response?.data?.message ||
        'Failed to unassign driver from vehicle';
      return rejectWithValue(message);
    }
  }
);

const driversSlice = createSlice({
  name: 'drivers',
  initialState,
  reducers: {
    clearError: state => {
      state.error = null;
    },
    setCurrentDriver: (state, action: PayloadAction<Driver | null>) => {
      state.currentDriver = action.payload;
    },
    setFilters: (
      state,
      action: PayloadAction<Partial<DriversState['filters']>>
    ) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: state => {
      state.filters = {};
    },
    setPagination: (
      state,
      action: PayloadAction<Partial<DriversState['pagination']>>
    ) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
  },
  extraReducers: builder => {
    builder
      // Fetch drivers cases
      .addCase(fetchDrivers.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchDrivers.fulfilled, (state, action) => {
        state.isLoading = false;
        state.drivers = action.payload.data;
        state.pagination = {
          page: action.payload.page,
          pageSize: action.payload.pageSize,
          total: action.payload.total,
          totalPages: action.payload.totalPages,
        };
        state.error = null;
      })
      .addCase(fetchDrivers.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Fetch driver by ID cases
      .addCase(fetchDriverById.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchDriverById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentDriver = action.payload;
        state.error = null;
      })
      .addCase(fetchDriverById.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Create driver cases
      .addCase(createDriver.pending, state => {
        state.isCreating = true;
        state.error = null;
      })
      .addCase(createDriver.fulfilled, (state, action) => {
        state.isCreating = false;
        state.drivers.unshift(action.payload);
        state.pagination.total += 1;
        state.error = null;
      })
      .addCase(createDriver.rejected, (state, action) => {
        state.isCreating = false;
        state.error = action.payload as string;
      })
      // Update driver cases
      .addCase(updateDriver.pending, state => {
        state.isUpdating = true;
        state.error = null;
      })
      .addCase(updateDriver.fulfilled, (state, action) => {
        state.isUpdating = false;
        const index = state.drivers.findIndex(d => d.id === action.payload.id);
        if (index !== -1) {
          state.drivers[index] = action.payload;
        }
        if (state.currentDriver?.id === action.payload.id) {
          state.currentDriver = action.payload;
        }
        state.error = null;
      })
      .addCase(updateDriver.rejected, (state, action) => {
        state.isUpdating = false;
        state.error = action.payload as string;
      })
      // Delete driver cases
      .addCase(deleteDriver.pending, state => {
        state.isDeleting = true;
        state.error = null;
      })
      .addCase(deleteDriver.fulfilled, (state, action) => {
        state.isDeleting = false;
        state.drivers = state.drivers.filter(d => d.id !== action.payload);
        if (state.currentDriver?.id === action.payload) {
          state.currentDriver = null;
        }
        state.pagination.total = Math.max(0, state.pagination.total - 1);
        state.error = null;
      })
      .addCase(deleteDriver.rejected, (state, action) => {
        state.isDeleting = false;
        state.error = action.payload as string;
      })
      // Assign driver cases
      .addCase(assignDriverToVehicle.pending, state => {
        state.isAssigning = true;
        state.error = null;
      })
      .addCase(assignDriverToVehicle.fulfilled, (state, action) => {
        state.isAssigning = false;
        const index = state.drivers.findIndex(d => d.id === action.payload.id);
        if (index !== -1) {
          state.drivers[index] = action.payload;
        }
        if (state.currentDriver?.id === action.payload.id) {
          state.currentDriver = action.payload;
        }
        state.error = null;
      })
      .addCase(assignDriverToVehicle.rejected, (state, action) => {
        state.isAssigning = false;
        state.error = action.payload as string;
      })
      // Unassign driver cases
      .addCase(unassignDriverFromVehicle.pending, state => {
        state.isAssigning = true;
        state.error = null;
      })
      .addCase(unassignDriverFromVehicle.fulfilled, (state, action) => {
        state.isAssigning = false;
        const index = state.drivers.findIndex(d => d.id === action.payload.id);
        if (index !== -1) {
          state.drivers[index] = action.payload;
        }
        if (state.currentDriver?.id === action.payload.id) {
          state.currentDriver = action.payload;
        }
        state.error = null;
      })
      .addCase(unassignDriverFromVehicle.rejected, (state, action) => {
        state.isAssigning = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  clearError,
  setCurrentDriver,
  setFilters,
  clearFilters,
  setPagination,
} = driversSlice.actions;

// Selectors
export const selectDrivers = (state: { drivers: DriversState }) =>
  state.drivers.drivers;
export const selectCurrentDriver = (state: { drivers: DriversState }) =>
  state.drivers.currentDriver;
export const selectDriversLoading = (state: { drivers: DriversState }) =>
  state.drivers.isLoading;
export const selectDriversCreating = (state: { drivers: DriversState }) =>
  state.drivers.isCreating;
export const selectDriversUpdating = (state: { drivers: DriversState }) =>
  state.drivers.isUpdating;
export const selectDriversDeleting = (state: { drivers: DriversState }) =>
  state.drivers.isDeleting;
export const selectDriversAssigning = (state: { drivers: DriversState }) =>
  state.drivers.isAssigning;
export const selectDriversError = (state: { drivers: DriversState }) =>
  state.drivers.error;
export const selectDriversPagination = (state: { drivers: DriversState }) =>
  state.drivers.pagination;
export const selectDriversFilters = (state: { drivers: DriversState }) =>
  state.drivers.filters;

export default driversSlice.reducer;
