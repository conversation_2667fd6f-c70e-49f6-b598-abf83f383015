import axios, { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { tokenStorage } from '../utils/tokenStorage';
import { store } from '../store';
import { refreshToken, logoutUser } from '../features/auth/authSlice';

// Flag to prevent multiple refresh attempts
let isRefreshing = false;
let failedQueue: Array<{
  resolve: (value?: any) => void;
  reject: (error?: any) => void;
}> = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token);
    }
  });
  
  failedQueue = [];
};

// Request interceptor to add auth token
export const setupRequestInterceptor = (axiosInstance: typeof axios) => {
  axiosInstance.interceptors.request.use(
    (config: AxiosRequestConfig) => {
      const token = tokenStorage.getAccessToken();
      
      if (token && config.headers) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      
      return config;
    },
    (error: AxiosError) => {
      return Promise.reject(error);
    }
  );
};

// Response interceptor to handle token refresh
export const setupResponseInterceptor = (axiosInstance: typeof axios) => {
  axiosInstance.interceptors.response.use(
    (response: AxiosResponse) => {
      return response;
    },
    async (error: AxiosError) => {
      const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };

      // Check if error is 401 and we haven't already tried to refresh
      if (error.response?.status === 401 && !originalRequest._retry) {
        if (isRefreshing) {
          // If we're already refreshing, queue this request
          return new Promise((resolve, reject) => {
            failedQueue.push({ resolve, reject });
          }).then(token => {
            if (originalRequest.headers) {
              originalRequest.headers.Authorization = `Bearer ${token}`;
            }
            return axiosInstance(originalRequest);
          }).catch(err => {
            return Promise.reject(err);
          });
        }

        originalRequest._retry = true;
        isRefreshing = true;

        try {
          // Attempt to refresh the token
          const refreshResult = await store.dispatch(refreshToken()).unwrap();
          
          if (refreshResult.token) {
            // Update the authorization header for the original request
            if (originalRequest.headers) {
              originalRequest.headers.Authorization = `Bearer ${refreshResult.token}`;
            }
            
            // Process the queue with the new token
            processQueue(null, refreshResult.token);
            
            // Retry the original request
            return axiosInstance(originalRequest);
          } else {
            throw new Error('No token received from refresh');
          }
        } catch (refreshError) {
          // Refresh failed, logout user
          processQueue(refreshError, null);
          
          // Clear tokens and logout
          tokenStorage.clearTokens();
          store.dispatch(logoutUser());
          
          // Redirect to login if not already there
          if (window.location.pathname !== '/auth/login') {
            window.location.href = '/auth/login';
          }
          
          return Promise.reject(refreshError);
        } finally {
          isRefreshing = false;
        }
      }

      // For other errors or if refresh failed, reject the promise
      return Promise.reject(error);
    }
  );
};

// Setup both interceptors
export const setupAuthInterceptors = (axiosInstance: typeof axios) => {
  setupRequestInterceptor(axiosInstance);
  setupResponseInterceptor(axiosInstance);
};
