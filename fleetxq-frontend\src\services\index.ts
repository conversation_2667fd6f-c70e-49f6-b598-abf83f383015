// API Service Layer - Centralized exports for FleetXQ Frontend
// This file provides a single entry point for all API services

// Base API service
export { apiService, default as api } from './api';

// Domain-specific services
export { authService, default as auth } from './authService';
export { vehicleService, default as vehicle } from './vehicleService';
export { driverService, default as driver } from './driverService';
export { telemetryService, default as telemetry } from './telemetryService';
export { alertService, default as alert } from './alertService';
export { dashboardService, default as dashboard } from './dashboardService';

// Service classes for advanced usage
export { AuthService } from './authService';
export { VehicleService } from './vehicleService';
export { DriverService } from './driverService';
export { TelemetryService } from './telemetryService';
export { AlertService } from './alertService';
export { DashboardService } from './dashboardService';

// Re-export types for convenience
export type {
  ApiResponse,
  PaginatedResponse,
  ApiConfig,
  ApiError,
  ApiErrorResponse,
  RefreshTokenRequest,
  RefreshTokenResponse,
  RetryConfig,
} from '../types';
