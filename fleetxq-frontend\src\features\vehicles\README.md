# Vehicle Management System

This document provides an overview of the comprehensive vehicle management system implemented in FleetXQ.

## Features Overview

### 1. Vehicle List Management
- **Enhanced Vehicle Cards**: Display comprehensive vehicle information including real-time status, fuel levels, location, and maintenance alerts
- **Multiple View Modes**: List, grid, and map views for different user preferences
- **Advanced Filtering**: Filter by status, vehicle type, fuel type, assignment status, maintenance needs, and more
- **Bulk Operations**: Perform actions on multiple vehicles simultaneously (admin/manager only)
- **Real-time Updates**: Live updates of vehicle status and location
- **Export Functionality**: Export vehicle data in CSV/Excel formats

### 2. Vehicle Details & Analytics
- **Comprehensive Analytics Dashboard**: Fuel consumption, utilization rates, maintenance costs, and performance metrics
- **Interactive Charts**: Built with Recharts for responsive data visualization
- **Real-time Location Tracking**: Live vehicle location with map integration
- **Maintenance Management**: Track maintenance history, alerts, and schedule new maintenance
- **Assignment History**: Complete driver assignment tracking and management

### 3. Driver Assignment System
- **Assignment Modal**: Intuitive interface for assigning/unassigning drivers
- **Conflict Detection**: Automatic detection of assignment conflicts
- **Bulk Assignment**: Assign multiple vehicles to drivers simultaneously
- **Assignment History**: Track all assignment changes with reasons and timestamps

### 4. Real-time Features
- **Live Vehicle Status**: Real-time updates of vehicle location, fuel, and engine status
- **Maintenance Alerts**: Proactive alerts for upcoming and overdue maintenance
- **Performance Monitoring**: Continuous tracking of vehicle utilization and efficiency

## Architecture

### Component Structure
```
vehicles/
├── VehiclesPage.tsx              # Main page with routing
├── VehiclesList.tsx              # Enhanced list with all view modes
├── components/
│   ├── VehicleCard.tsx           # Individual vehicle card
│   ├── VehicleForm.tsx           # Create/edit vehicle form
│   ├── VehicleDetails.tsx        # Detailed vehicle view
│   ├── VehicleNavigation.tsx     # Navigation between sections
│   ├── VehicleMap.tsx            # Real-time location map
│   ├── VehicleAnalyticsDashboard.tsx  # Main analytics dashboard
│   ├── FuelConsumptionChart.tsx  # Fuel analytics
│   ├── MaintenanceCostChart.tsx  # Maintenance cost tracking
│   ├── VehicleUtilizationChart.tsx   # Utilization analytics
│   ├── VehicleAssignmentModal.tsx     # Driver assignment
│   ├── VehicleAssignmentHistory.tsx  # Assignment history
│   └── BulkAssignmentModal.tsx   # Bulk operations
├── vehiclesSlice.ts              # Redux state management
└── __tests__/                    # Comprehensive test suite
```

### State Management
The vehicle system uses Redux Toolkit for state management with the following structure:

```typescript
interface VehiclesState {
  vehicles: Vehicle[];
  currentVehicle: Vehicle | null;
  selectedVehicles: string[];
  vehicleAnalytics: Record<string, VehicleAnalytics>;
  maintenanceAlerts: VehicleMaintenanceAlert[];
  assignmentHistory: VehicleAssignmentHistory[];
  realTimeUpdates: VehicleRealTimeUpdate[];
  // Loading states
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  // Error handling
  error: string | null;
  bulkOperationError: string | null;
  // Pagination and filtering
  pagination: PaginationState;
  filters: VehicleSearchFilters;
}
```

## Routing Structure

### Main Routes
- `/vehicles` - Vehicle list with all view modes
- `/vehicles/:id` - Vehicle overview
- `/vehicles/:id/analytics` - Vehicle analytics dashboard
- `/vehicles/:id/location` - Real-time location tracking
- `/vehicles/:id/maintenance` - Maintenance history and alerts
- `/vehicles/:id/assignments` - Driver assignment history

### Permission-based Access
Each route is protected by permission guards:

```typescript
// Example route protection
<PermissionGuard requiredPermissions={['view_vehicle_analytics']}>
  <VehicleAnalyticsDashboard />
</PermissionGuard>
```

## Permissions System

### Vehicle-specific Permissions
- `view_vehicles` - View vehicle list and basic details
- `create_vehicles` - Add new vehicles
- `edit_vehicles` - Modify vehicle information
- `delete_vehicles` - Remove vehicles
- `assign_drivers` - Assign/unassign drivers
- `view_vehicle_analytics` - Access analytics dashboard
- `view_vehicle_location` - View real-time location
- `view_vehicle_maintenance` - Access maintenance information
- `schedule_maintenance` - Schedule maintenance appointments
- `view_assignment_history` - View driver assignment history
- `bulk_vehicle_operations` - Perform bulk operations
- `export_vehicle_data` - Export vehicle data

### Role-based Access
- **Admin**: Full access to all features
- **Manager**: All features except user management
- **Driver**: Limited to viewing vehicles and location

## API Integration

### Service Layer
The `vehicleService.ts` provides comprehensive API integration:

```typescript
// Enhanced service methods
getVehicles(filters, page, pageSize)
getVehicleAnalytics(vehicleId, timeRange)
getMaintenanceAlerts(vehicleId?)
getAssignmentHistory(vehicleId)
performBulkOperation(operation)
getRealTimeUpdates(vehicleIds?)
assignDriver(vehicleId, driverId, reason?)
```

### Real-time Updates
The system supports real-time updates through:
- WebSocket connections for live vehicle status
- Periodic polling for analytics data
- Event-driven updates for maintenance alerts

## Testing Strategy

### Test Coverage
- **Unit Tests**: Individual components and utilities
- **Integration Tests**: Component interactions and routing
- **Service Tests**: API service layer
- **Redux Tests**: State management logic

### Test Files
- `VehicleCard.test.tsx` - Vehicle card component
- `VehicleForm.test.tsx` - Form validation and submission
- `vehiclesSlice.test.ts` - Redux state management
- `vehicleService.test.ts` - API service layer
- `VehicleRouting.integration.test.tsx` - Routing and permissions

## Performance Optimizations

### Code Splitting
- Lazy loading of analytics components
- Route-based code splitting
- Dynamic imports for heavy charts

### Data Management
- Efficient pagination for large vehicle lists
- Memoized selectors for computed data
- Optimistic updates for better UX

### Real-time Efficiency
- Debounced search and filtering
- Selective real-time updates
- Efficient chart re-rendering

## Usage Examples

### Basic Vehicle List
```tsx
import VehiclesList from './features/vehicles/VehiclesList';

function App() {
  return <VehiclesList />;
}
```

### Vehicle Details with Analytics
```tsx
import VehicleDetails from './features/vehicles/components/VehicleDetails';

function VehicleDetailsPage() {
  return (
    <VehicleDetails
      onEdit={handleEdit}
      onAssignDriver={handleAssignDriver}
      onScheduleMaintenance={handleScheduleMaintenance}
    />
  );
}
```

### Custom Analytics Dashboard
```tsx
import VehicleAnalyticsDashboard from './features/vehicles/components/VehicleAnalyticsDashboard';

function CustomAnalytics({ vehicle }) {
  return (
    <VehicleAnalyticsDashboard
      vehicle={vehicle}
      className="custom-analytics"
    />
  );
}
```

## Future Enhancements

### Planned Features
- Advanced route optimization
- Predictive maintenance using ML
- Enhanced real-time tracking with geofencing
- Mobile app integration
- Advanced reporting and dashboards

### Technical Improvements
- GraphQL integration for efficient data fetching
- Offline support with service workers
- Enhanced accessibility features
- Performance monitoring and optimization

## Contributing

When contributing to the vehicle management system:

1. Follow the established component patterns
2. Add comprehensive tests for new features
3. Update permissions system for new actions
4. Maintain backward compatibility
5. Document new APIs and components

## Support

For questions or issues related to the vehicle management system:
- Check the test files for usage examples
- Review the Redux state structure
- Consult the permissions documentation
- Test with different user roles
