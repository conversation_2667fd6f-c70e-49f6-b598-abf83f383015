import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import { apiService } from '../../services/api';

// Dashboard metrics interfaces
interface FleetMetrics {
  totalVehicles: number;
  activeVehicles: number;
  inactiveVehicles: number;
  maintenanceVehicles: number;
  totalDrivers: number;
  activeDrivers: number;
  unassignedDrivers: number;
}

interface AlertMetrics {
  totalAlerts: number;
  criticalAlerts: number;
  highAlerts: number;
  mediumAlerts: number;
  lowAlerts: number;
  acknowledgedAlerts: number;
  unacknowledgedAlerts: number;
}

interface TelemetryMetrics {
  averageSpeed: number;
  totalDistance: number;
  averageFuelLevel: number;
  activeVehiclesCount: number;
  lastUpdateTime: string;
}

interface PerformanceMetrics {
  fuelEfficiency: number;
  maintenanceCosts: number;
  driverPerformance: number;
  vehicleUtilization: number;
  period: 'day' | 'week' | 'month';
}

interface RecentActivity {
  id: string;
  type: 'alert' | 'maintenance' | 'driver_assignment' | 'vehicle_status';
  message: string;
  timestamp: string;
  severity?: 'low' | 'medium' | 'high' | 'critical';
  vehicleId?: string;
  driverId?: string;
}

interface DashboardSummary {
  fleetMetrics: FleetMetrics;
  alertMetrics: AlertMetrics;
  telemetryMetrics: TelemetryMetrics;
  performanceMetrics: PerformanceMetrics;
  recentActivity: RecentActivity[];
  lastUpdated: string;
}

// Real-time and customization interfaces
interface DashboardWidget {
  id: string;
  type: 'metric' | 'chart' | 'alert' | 'activity' | 'custom';
  title: string;
  position: { x: number; y: number; w: number; h: number };
  isVisible: boolean;
  config?: Record<string, any>;
}

interface DashboardLayout {
  id: string;
  name: string;
  widgets: DashboardWidget[];
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

interface DashboardPreferences {
  defaultLayout: string;
  autoRefresh: boolean;
  refreshInterval: number;
  notifications: {
    sound: boolean;
    desktop: boolean;
    email: boolean;
  };
  theme: 'light' | 'dark' | 'auto';
  timeZone: string;
  dateFormat: string;
  numberFormat: string;
}

interface RealTimeConnection {
  isConnected: boolean;
  lastConnected?: string;
  reconnectAttempts: number;
  error?: string;
}

interface ChartData {
  id: string;
  type: 'line' | 'bar' | 'pie' | 'area' | 'scatter';
  data: any[];
  config: Record<string, any>;
  lastUpdated: string;
  isLoading: boolean;
  error?: string;
}

// Dashboard state interface
interface DashboardState {
  summary: DashboardSummary | null;
  fleetMetrics: FleetMetrics | null;
  alertMetrics: AlertMetrics | null;
  telemetryMetrics: TelemetryMetrics | null;
  performanceMetrics: PerformanceMetrics | null;
  recentActivity: RecentActivity[];
  isLoading: boolean;
  isLoadingMetrics: boolean;
  isLoadingActivity: boolean;
  error: string | null;
  selectedPeriod: 'day' | 'week' | 'month';
  autoRefresh: boolean;
  refreshInterval: number; // in seconds
  lastUpdated: string | null;

  // Real-time features
  realTimeConnection: RealTimeConnection;
  liveUpdatesEnabled: boolean;

  // Widget and layout management
  currentLayout: DashboardLayout | null;
  availableLayouts: DashboardLayout[];
  isCustomizing: boolean;

  // Dashboard preferences
  preferences: DashboardPreferences;

  // Chart data
  charts: Record<string, ChartData>;

  // Filters and time range
  timeRange: {
    start: string;
    end: string;
    preset: 'today' | 'yesterday' | 'last7days' | 'last30days' | 'custom';
  };

  // Export functionality
  isExporting: boolean;
  exportError: string | null;
}

const initialState: DashboardState = {
  summary: null,
  fleetMetrics: null,
  alertMetrics: null,
  telemetryMetrics: null,
  performanceMetrics: null,
  recentActivity: [],
  isLoading: false,
  isLoadingMetrics: false,
  isLoadingActivity: false,
  error: null,
  selectedPeriod: 'day',
  autoRefresh: true,
  refreshInterval: 30,
  lastUpdated: null,

  // Real-time features
  realTimeConnection: {
    isConnected: false,
    reconnectAttempts: 0,
  },
  liveUpdatesEnabled: true,

  // Widget and layout management
  currentLayout: null,
  availableLayouts: [],
  isCustomizing: false,

  // Dashboard preferences
  preferences: {
    defaultLayout: 'default',
    autoRefresh: true,
    refreshInterval: 30,
    notifications: {
      sound: true,
      desktop: true,
      email: false,
    },
    theme: 'light',
    timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    dateFormat: 'MM/dd/yyyy',
    numberFormat: 'en-US',
  },

  // Chart data
  charts: {},

  // Filters and time range
  timeRange: {
    start: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 24 hours ago
    end: new Date().toISOString(),
    preset: 'today',
  },

  // Export functionality
  isExporting: false,
  exportError: null,
};

// Async thunks
export const fetchDashboardSummary = createAsyncThunk(
  'dashboard/fetchDashboardSummary',
  async (period: 'day' | 'week' | 'month' = 'day', { rejectWithValue }) => {
    try {
      const response = await apiService.get<DashboardSummary>(
        `/dashboard/summary?period=${period}`
      );
      return response.data;
    } catch (error: any) {
      const message =
        error.response?.data?.message || 'Failed to fetch dashboard summary';
      return rejectWithValue(message);
    }
  }
);

export const fetchFleetMetrics = createAsyncThunk(
  'dashboard/fetchFleetMetrics',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiService.get<FleetMetrics>(
        '/dashboard/fleet-metrics'
      );
      return response.data;
    } catch (error: any) {
      const message =
        error.response?.data?.message || 'Failed to fetch fleet metrics';
      return rejectWithValue(message);
    }
  }
);

export const fetchAlertMetrics = createAsyncThunk(
  'dashboard/fetchAlertMetrics',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiService.get<AlertMetrics>(
        '/dashboard/alert-metrics'
      );
      return response.data;
    } catch (error: any) {
      const message =
        error.response?.data?.message || 'Failed to fetch alert metrics';
      return rejectWithValue(message);
    }
  }
);

export const fetchTelemetryMetrics = createAsyncThunk(
  'dashboard/fetchTelemetryMetrics',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiService.get<TelemetryMetrics>(
        '/dashboard/telemetry-metrics'
      );
      return response.data;
    } catch (error: any) {
      const message =
        error.response?.data?.message || 'Failed to fetch telemetry metrics';
      return rejectWithValue(message);
    }
  }
);

export const fetchPerformanceMetrics = createAsyncThunk(
  'dashboard/fetchPerformanceMetrics',
  async (period: 'day' | 'week' | 'month' = 'day', { rejectWithValue }) => {
    try {
      const response = await apiService.get<PerformanceMetrics>(
        `/dashboard/performance-metrics?period=${period}`
      );
      return response.data;
    } catch (error: any) {
      const message =
        error.response?.data?.message || 'Failed to fetch performance metrics';
      return rejectWithValue(message);
    }
  }
);

export const fetchRecentActivity = createAsyncThunk(
  'dashboard/fetchRecentActivity',
  async (limit: number = 10, { rejectWithValue }) => {
    try {
      const response = await apiService.get<RecentActivity[]>(
        `/dashboard/recent-activity?limit=${limit}`
      );
      return response.data;
    } catch (error: any) {
      const message =
        error.response?.data?.message || 'Failed to fetch recent activity';
      return rejectWithValue(message);
    }
  }
);

export const refreshDashboard = createAsyncThunk(
  'dashboard/refreshDashboard',
  async (period: 'day' | 'week' | 'month' = 'day', { dispatch }) => {
    // Fetch all dashboard data concurrently
    const promises = [
      dispatch(fetchFleetMetrics()),
      dispatch(fetchAlertMetrics()),
      dispatch(fetchTelemetryMetrics()),
      dispatch(fetchPerformanceMetrics(period)),
      dispatch(fetchRecentActivity()),
    ];

    await Promise.all(promises);
    return period;
  }
);

// New async thunks for enhanced functionality
export const fetchDashboardLayouts = createAsyncThunk(
  'dashboard/fetchDashboardLayouts',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiService.get<DashboardLayout[]>('/dashboard/layouts');
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch dashboard layouts';
      return rejectWithValue(message);
    }
  }
);

export const saveDashboardLayout = createAsyncThunk(
  'dashboard/saveDashboardLayout',
  async (layout: Omit<DashboardLayout, 'id' | 'createdAt' | 'updatedAt'>, { rejectWithValue }) => {
    try {
      const response = await apiService.post<DashboardLayout>('/dashboard/layouts', layout);
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to save dashboard layout';
      return rejectWithValue(message);
    }
  }
);

export const updateDashboardLayout = createAsyncThunk(
  'dashboard/updateDashboardLayout',
  async ({ id, layout }: { id: string; layout: Partial<DashboardLayout> }, { rejectWithValue }) => {
    try {
      const response = await apiService.put<DashboardLayout>(`/dashboard/layouts/${id}`, layout);
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to update dashboard layout';
      return rejectWithValue(message);
    }
  }
);

export const deleteDashboardLayout = createAsyncThunk(
  'dashboard/deleteDashboardLayout',
  async (id: string, { rejectWithValue }) => {
    try {
      await apiService.delete(`/dashboard/layouts/${id}`);
      return id;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to delete dashboard layout';
      return rejectWithValue(message);
    }
  }
);

export const fetchDashboardPreferences = createAsyncThunk(
  'dashboard/fetchDashboardPreferences',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiService.get<DashboardPreferences>('/dashboard/preferences');
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch dashboard preferences';
      return rejectWithValue(message);
    }
  }
);

export const updateDashboardPreferences = createAsyncThunk(
  'dashboard/updateDashboardPreferences',
  async (preferences: Partial<DashboardPreferences>, { rejectWithValue }) => {
    try {
      const response = await apiService.put<DashboardPreferences>('/dashboard/preferences', preferences);
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to update dashboard preferences';
      return rejectWithValue(message);
    }
  }
);

export const fetchChartData = createAsyncThunk(
  'dashboard/fetchChartData',
  async ({ chartId, config }: { chartId: string; config?: Record<string, any> }, { rejectWithValue }) => {
    try {
      const response = await apiService.get<any[]>(`/dashboard/charts/${chartId}`, { params: config });
      return { chartId, data: response.data };
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch chart data';
      return rejectWithValue({ chartId, message });
    }
  }
);

export const exportDashboardData = createAsyncThunk(
  'dashboard/exportDashboardData',
  async ({ format, timeRange }: { format: 'csv' | 'excel' | 'pdf'; timeRange?: { start: string; end: string } }, { rejectWithValue }) => {
    try {
      const response = await apiService.post('/dashboard/export', { format, timeRange }, {
        responseType: 'blob',
      });

      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `dashboard-export-${new Date().toISOString().split('T')[0]}.${format}`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      return { format, timestamp: new Date().toISOString() };
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to export dashboard data';
      return rejectWithValue(message);
    }
  }
);

const dashboardSlice = createSlice({
  name: 'dashboard',
  initialState,
  reducers: {
    clearError: state => {
      state.error = null;
    },
    setSelectedPeriod: (
      state,
      action: PayloadAction<'day' | 'week' | 'month'>
    ) => {
      state.selectedPeriod = action.payload;
    },
    setAutoRefresh: (state, action: PayloadAction<boolean>) => {
      state.autoRefresh = action.payload;
    },
    setRefreshInterval: (state, action: PayloadAction<number>) => {
      state.refreshInterval = action.payload;
    },
    updateMetrics: (
      state,
      action: PayloadAction<Partial<DashboardSummary>>
    ) => {
      if (state.summary) {
        state.summary = { ...state.summary, ...action.payload };
      }
      state.lastUpdated = new Date().toISOString();
    },
    addRecentActivity: (state, action: PayloadAction<RecentActivity>) => {
      state.recentActivity.unshift(action.payload);
      // Keep only the latest 20 activities
      if (state.recentActivity.length > 20) {
        state.recentActivity = state.recentActivity.slice(0, 20);
      }
    },
    clearDashboard: state => {
      state.summary = null;
      state.fleetMetrics = null;
      state.alertMetrics = null;
      state.telemetryMetrics = null;
      state.performanceMetrics = null;
      state.recentActivity = [];
      state.lastUpdated = null;
    },

    // Real-time connection actions
    setRealTimeConnection: (state, action: PayloadAction<RealTimeConnection>) => {
      state.realTimeConnection = action.payload;
    },
    setLiveUpdatesEnabled: (state, action: PayloadAction<boolean>) => {
      state.liveUpdatesEnabled = action.payload;
    },

    // Layout and customization actions
    setCurrentLayout: (state, action: PayloadAction<DashboardLayout>) => {
      state.currentLayout = action.payload;
    },
    setIsCustomizing: (state, action: PayloadAction<boolean>) => {
      state.isCustomizing = action.payload;
    },
    updateWidgetPosition: (state, action: PayloadAction<{ widgetId: string; position: { x: number; y: number; w: number; h: number } }>) => {
      if (state.currentLayout) {
        const widget = state.currentLayout.widgets.find(w => w.id === action.payload.widgetId);
        if (widget) {
          widget.position = action.payload.position;
        }
      }
    },
    toggleWidgetVisibility: (state, action: PayloadAction<string>) => {
      if (state.currentLayout) {
        const widget = state.currentLayout.widgets.find(w => w.id === action.payload);
        if (widget) {
          widget.isVisible = !widget.isVisible;
        }
      }
    },
    updateWidgetConfig: (state, action: PayloadAction<{ widgetId: string; config: Record<string, any> }>) => {
      if (state.currentLayout) {
        const widget = state.currentLayout.widgets.find(w => w.id === action.payload.widgetId);
        if (widget) {
          widget.config = { ...widget.config, ...action.payload.config };
        }
      }
    },

    // Preferences actions
    updatePreferences: (state, action: PayloadAction<Partial<DashboardPreferences>>) => {
      state.preferences = { ...state.preferences, ...action.payload };
    },

    // Chart data actions
    setChartData: (state, action: PayloadAction<{ chartId: string; data: ChartData }>) => {
      state.charts[action.payload.chartId] = action.payload.data;
    },
    setChartLoading: (state, action: PayloadAction<{ chartId: string; isLoading: boolean }>) => {
      if (state.charts[action.payload.chartId]) {
        state.charts[action.payload.chartId].isLoading = action.payload.isLoading;
      }
    },
    setChartError: (state, action: PayloadAction<{ chartId: string; error: string }>) => {
      if (state.charts[action.payload.chartId]) {
        state.charts[action.payload.chartId].error = action.payload.error;
        state.charts[action.payload.chartId].isLoading = false;
      }
    },

    // Time range actions
    setTimeRange: (state, action: PayloadAction<{ start: string; end: string; preset: 'today' | 'yesterday' | 'last7days' | 'last30days' | 'custom' }>) => {
      state.timeRange = action.payload;
    },

    // Export actions
    setExportError: (state, action: PayloadAction<string | null>) => {
      state.exportError = action.payload;
    },
  },
  extraReducers: builder => {
    builder
      // Fetch dashboard summary cases
      .addCase(fetchDashboardSummary.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchDashboardSummary.fulfilled, (state, action) => {
        state.isLoading = false;
        state.summary = action.payload;
        state.fleetMetrics = action.payload.fleetMetrics;
        state.alertMetrics = action.payload.alertMetrics;
        state.telemetryMetrics = action.payload.telemetryMetrics;
        state.performanceMetrics = action.payload.performanceMetrics;
        state.recentActivity = action.payload.recentActivity;
        state.lastUpdated = action.payload.lastUpdated;
        state.error = null;
      })
      .addCase(fetchDashboardSummary.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Fetch fleet metrics cases
      .addCase(fetchFleetMetrics.pending, state => {
        state.isLoadingMetrics = true;
        state.error = null;
      })
      .addCase(fetchFleetMetrics.fulfilled, (state, action) => {
        state.isLoadingMetrics = false;
        state.fleetMetrics = action.payload;
        state.lastUpdated = new Date().toISOString();
        state.error = null;
      })
      .addCase(fetchFleetMetrics.rejected, (state, action) => {
        state.isLoadingMetrics = false;
        state.error = action.payload as string;
      })
      // Fetch alert metrics cases
      .addCase(fetchAlertMetrics.pending, state => {
        state.isLoadingMetrics = true;
        state.error = null;
      })
      .addCase(fetchAlertMetrics.fulfilled, (state, action) => {
        state.isLoadingMetrics = false;
        state.alertMetrics = action.payload;
        state.lastUpdated = new Date().toISOString();
        state.error = null;
      })
      .addCase(fetchAlertMetrics.rejected, (state, action) => {
        state.isLoadingMetrics = false;
        state.error = action.payload as string;
      })
      // Fetch telemetry metrics cases
      .addCase(fetchTelemetryMetrics.pending, state => {
        state.isLoadingMetrics = true;
        state.error = null;
      })
      .addCase(fetchTelemetryMetrics.fulfilled, (state, action) => {
        state.isLoadingMetrics = false;
        state.telemetryMetrics = action.payload;
        state.lastUpdated = new Date().toISOString();
        state.error = null;
      })
      .addCase(fetchTelemetryMetrics.rejected, (state, action) => {
        state.isLoadingMetrics = false;
        state.error = action.payload as string;
      })
      // Fetch performance metrics cases
      .addCase(fetchPerformanceMetrics.pending, state => {
        state.isLoadingMetrics = true;
        state.error = null;
      })
      .addCase(fetchPerformanceMetrics.fulfilled, (state, action) => {
        state.isLoadingMetrics = false;
        state.performanceMetrics = action.payload;
        state.lastUpdated = new Date().toISOString();
        state.error = null;
      })
      .addCase(fetchPerformanceMetrics.rejected, (state, action) => {
        state.isLoadingMetrics = false;
        state.error = action.payload as string;
      })
      // Fetch recent activity cases
      .addCase(fetchRecentActivity.pending, state => {
        state.isLoadingActivity = true;
        state.error = null;
      })
      .addCase(fetchRecentActivity.fulfilled, (state, action) => {
        state.isLoadingActivity = false;
        state.recentActivity = action.payload;
        state.lastUpdated = new Date().toISOString();
        state.error = null;
      })
      .addCase(fetchRecentActivity.rejected, (state, action) => {
        state.isLoadingActivity = false;
        state.error = action.payload as string;
      })
      // Refresh dashboard cases
      .addCase(refreshDashboard.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(refreshDashboard.fulfilled, (state, action) => {
        state.isLoading = false;
        state.selectedPeriod = action.payload;
        state.lastUpdated = new Date().toISOString();
        state.error = null;
      })
      .addCase(refreshDashboard.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Fetch dashboard layouts cases
      .addCase(fetchDashboardLayouts.fulfilled, (state, action) => {
        state.availableLayouts = action.payload;
        if (!state.currentLayout && action.payload.length > 0) {
          state.currentLayout = action.payload.find(layout => layout.isDefault) || action.payload[0];
        }
      })
      // Save dashboard layout cases
      .addCase(saveDashboardLayout.fulfilled, (state, action) => {
        state.availableLayouts.push(action.payload);
        state.currentLayout = action.payload;
      })
      // Update dashboard layout cases
      .addCase(updateDashboardLayout.fulfilled, (state, action) => {
        const index = state.availableLayouts.findIndex(layout => layout.id === action.payload.id);
        if (index !== -1) {
          state.availableLayouts[index] = action.payload;
          if (state.currentLayout?.id === action.payload.id) {
            state.currentLayout = action.payload;
          }
        }
      })
      // Delete dashboard layout cases
      .addCase(deleteDashboardLayout.fulfilled, (state, action) => {
        state.availableLayouts = state.availableLayouts.filter(layout => layout.id !== action.payload);
        if (state.currentLayout?.id === action.payload) {
          state.currentLayout = state.availableLayouts.find(layout => layout.isDefault) || state.availableLayouts[0] || null;
        }
      })
      // Fetch dashboard preferences cases
      .addCase(fetchDashboardPreferences.fulfilled, (state, action) => {
        state.preferences = action.payload;
      })
      // Update dashboard preferences cases
      .addCase(updateDashboardPreferences.fulfilled, (state, action) => {
        state.preferences = action.payload;
      })
      // Fetch chart data cases
      .addCase(fetchChartData.pending, (state, action) => {
        const chartId = action.meta.arg.chartId;
        if (!state.charts[chartId]) {
          state.charts[chartId] = {
            id: chartId,
            type: 'line',
            data: [],
            config: {},
            lastUpdated: '',
            isLoading: true,
          };
        } else {
          state.charts[chartId].isLoading = true;
          state.charts[chartId].error = undefined;
        }
      })
      .addCase(fetchChartData.fulfilled, (state, action) => {
        const { chartId, data } = action.payload;
        if (state.charts[chartId]) {
          state.charts[chartId].data = data;
          state.charts[chartId].lastUpdated = new Date().toISOString();
          state.charts[chartId].isLoading = false;
          state.charts[chartId].error = undefined;
        }
      })
      .addCase(fetchChartData.rejected, (state, action) => {
        const { chartId, message } = action.payload as { chartId: string; message: string };
        if (state.charts[chartId]) {
          state.charts[chartId].isLoading = false;
          state.charts[chartId].error = message;
        }
      })
      // Export dashboard data cases
      .addCase(exportDashboardData.pending, (state) => {
        state.isExporting = true;
        state.exportError = null;
      })
      .addCase(exportDashboardData.fulfilled, (state) => {
        state.isExporting = false;
        state.exportError = null;
      })
      .addCase(exportDashboardData.rejected, (state, action) => {
        state.isExporting = false;
        state.exportError = action.payload as string;
      });
  },
});

export const {
  clearError,
  setSelectedPeriod,
  setAutoRefresh,
  setRefreshInterval,
  updateMetrics,
  addRecentActivity,
  clearDashboard,
  // Real-time connection actions
  setRealTimeConnection,
  setLiveUpdatesEnabled,
  // Layout and customization actions
  setCurrentLayout,
  setIsCustomizing,
  updateWidgetPosition,
  toggleWidgetVisibility,
  updateWidgetConfig,
  // Preferences actions
  updatePreferences,
  // Chart data actions
  setChartData,
  setChartLoading,
  setChartError,
  // Time range actions
  setTimeRange,
  // Export actions
  setExportError,
} = dashboardSlice.actions;

// Selectors
export const selectDashboardSummary = (state: { dashboard: DashboardState }) =>
  state.dashboard.summary;
export const selectFleetMetrics = (state: { dashboard: DashboardState }) =>
  state.dashboard.fleetMetrics;
export const selectAlertMetrics = (state: { dashboard: DashboardState }) =>
  state.dashboard.alertMetrics;
export const selectTelemetryMetrics = (state: { dashboard: DashboardState }) =>
  state.dashboard.telemetryMetrics;
export const selectPerformanceMetrics = (state: {
  dashboard: DashboardState;
}) => state.dashboard.performanceMetrics;
export const selectRecentActivity = (state: { dashboard: DashboardState }) =>
  state.dashboard.recentActivity;
export const selectDashboardLoading = (state: { dashboard: DashboardState }) =>
  state.dashboard.isLoading;
export const selectDashboardMetricsLoading = (state: {
  dashboard: DashboardState;
}) => state.dashboard.isLoadingMetrics;
export const selectDashboardActivityLoading = (state: {
  dashboard: DashboardState;
}) => state.dashboard.isLoadingActivity;
export const selectDashboardError = (state: { dashboard: DashboardState }) =>
  state.dashboard.error;
export const selectSelectedPeriod = (state: { dashboard: DashboardState }) =>
  state.dashboard.selectedPeriod;
export const selectAutoRefresh = (state: { dashboard: DashboardState }) =>
  state.dashboard.autoRefresh;
export const selectRefreshInterval = (state: { dashboard: DashboardState }) =>
  state.dashboard.refreshInterval;
export const selectDashboardLastUpdated = (state: {
  dashboard: DashboardState;
}) => state.dashboard.lastUpdated;

// New selectors for enhanced features
export const selectRealTimeConnection = (state: { dashboard: DashboardState }) =>
  state.dashboard.realTimeConnection;
export const selectLiveUpdatesEnabled = (state: { dashboard: DashboardState }) =>
  state.dashboard.liveUpdatesEnabled;
export const selectCurrentLayout = (state: { dashboard: DashboardState }) =>
  state.dashboard.currentLayout;
export const selectAvailableLayouts = (state: { dashboard: DashboardState }) =>
  state.dashboard.availableLayouts;
export const selectIsCustomizing = (state: { dashboard: DashboardState }) =>
  state.dashboard.isCustomizing;
export const selectDashboardPreferences = (state: { dashboard: DashboardState }) =>
  state.dashboard.preferences;
export const selectCharts = (state: { dashboard: DashboardState }) =>
  state.dashboard.charts;
export const selectChartById = (chartId: string) => (state: { dashboard: DashboardState }) =>
  state.dashboard.charts[chartId];
export const selectTimeRange = (state: { dashboard: DashboardState }) =>
  state.dashboard.timeRange;
export const selectIsExporting = (state: { dashboard: DashboardState }) =>
  state.dashboard.isExporting;
export const selectExportError = (state: { dashboard: DashboardState }) =>
  state.dashboard.exportError;

// Computed selectors
export const selectVisibleWidgets = (state: { dashboard: DashboardState }) =>
  state.dashboard.currentLayout?.widgets.filter(widget => widget.isVisible) || [];
export const selectIsConnected = (state: { dashboard: DashboardState }) =>
  state.dashboard.realTimeConnection.isConnected;
export const selectHasActiveAlerts = (state: { dashboard: DashboardState }) =>
  state.dashboard.recentActivity.some(activity =>
    activity.type === 'alert' &&
    (activity.severity === 'critical' || activity.severity === 'high')
  );

export default dashboardSlice.reducer;
