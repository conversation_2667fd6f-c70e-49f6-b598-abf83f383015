import React, { useState, useEffect } from 'react';
import { 
  UserIcon, 
  ClockIcon, 
  ArrowRightIcon,
  FunnelIcon,
  CalendarIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';
import { useAppDispatch, useAppSelector } from '../../../store/hooks';
import {
  fetchAssignmentHistory,
  selectAssignmentHistory,
  selectVehiclesLoading
} from '../vehiclesSlice';
import type { VehicleAssignmentHistory } from '../../../types';
import LoadingSpinner from '../../../components/LoadingSpinner';

interface VehicleAssignmentHistoryProps {
  vehicleId: string;
  className?: string;
}

interface AssignmentFilters {
  search?: string;
  dateRange?: {
    startDate: string;
    endDate: string;
  };
  driverId?: string;
}

const VehicleAssignmentHistory: React.FC<VehicleAssignmentHistoryProps> = ({
  vehicleId,
  className = ''
}) => {
  const dispatch = useAppDispatch();
  const assignmentHistory = useAppSelector(selectAssignmentHistory);
  const isLoading = useAppSelector(selectVehiclesLoading);

  const [filters, setFilters] = useState<AssignmentFilters>({});
  const [showFilters, setShowFilters] = useState(false);

  // Mock data for demonstration - in real app, this would come from Redux state
  const mockAssignmentHistory: VehicleAssignmentHistory[] = [
    {
      id: '1',
      vehicleId,
      driverId: '1',
      driverName: 'John Doe',
      assignedAt: '2024-01-15T08:00:00Z',
      unassignedAt: '2024-01-20T17:00:00Z',
      assignedBy: 'Admin User',
      reason: 'Regular assignment rotation'
    },
    {
      id: '2',
      vehicleId,
      driverId: '2',
      driverName: 'Jane Smith',
      assignedAt: '2024-01-20T17:30:00Z',
      unassignedAt: '2024-01-25T16:00:00Z',
      assignedBy: 'Fleet Manager',
      reason: 'Temporary assignment for special route'
    },
    {
      id: '3',
      vehicleId,
      driverId: '3',
      driverName: 'Mike Johnson',
      assignedAt: '2024-01-25T16:30:00Z',
      assignedBy: 'Admin User',
      reason: 'Long-term assignment'
    }
  ];

  const [displayHistory, setDisplayHistory] = useState(mockAssignmentHistory);

  useEffect(() => {
    dispatch(fetchAssignmentHistory(vehicleId));
  }, [dispatch, vehicleId]);

  // Filter assignment history based on filters
  useEffect(() => {
    let filtered = mockAssignmentHistory;

    // Search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(assignment =>
        assignment.driverName.toLowerCase().includes(searchLower) ||
        assignment.assignedBy.toLowerCase().includes(searchLower) ||
        assignment.reason?.toLowerCase().includes(searchLower)
      );
    }

    // Date range filter
    if (filters.dateRange?.startDate && filters.dateRange?.endDate) {
      const startDate = new Date(filters.dateRange.startDate);
      const endDate = new Date(filters.dateRange.endDate);
      filtered = filtered.filter(assignment => {
        const assignedDate = new Date(assignment.assignedAt);
        return assignedDate >= startDate && assignedDate <= endDate;
      });
    }

    // Driver filter
    if (filters.driverId) {
      filtered = filtered.filter(assignment => assignment.driverId === filters.driverId);
    }

    setDisplayHistory(filtered);
  }, [filters]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const calculateDuration = (startDate: string, endDate?: string) => {
    const start = new Date(startDate);
    const end = endDate ? new Date(endDate) : new Date();
    const diffMs = end.getTime() - start.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Less than a day';
    if (diffDays === 1) return '1 day';
    if (diffDays < 30) return `${diffDays} days`;
    if (diffDays < 365) return `${Math.floor(diffDays / 30)} months`;
    return `${Math.floor(diffDays / 365)} years`;
  };

  const getUniqueDrivers = () => {
    const drivers = new Set(mockAssignmentHistory.map(a => ({ id: a.driverId, name: a.driverName })));
    return Array.from(drivers);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-32">
        <LoadingSpinner size="md" />
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Assignment History</h3>
          <p className="text-sm text-gray-600">
            Track driver assignments and changes over time
          </p>
        </div>
        <button
          onClick={() => setShowFilters(!showFilters)}
          className={`flex items-center space-x-2 px-3 py-2 rounded-lg border ${
            showFilters ? 'bg-blue-50 border-blue-200 text-blue-700' : 'bg-white border-gray-300 text-gray-700'
          } hover:bg-blue-50`}
        >
          <FunnelIcon className="h-4 w-4" />
          <span>Filters</span>
        </button>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Search */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Search
              </label>
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search driver, assigned by, or reason..."
                  value={filters.search || ''}
                  onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value || undefined }))}
                  className="input-field pl-10"
                />
              </div>
            </div>

            {/* Driver Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Driver
              </label>
              <select
                value={filters.driverId || ''}
                onChange={(e) => setFilters(prev => ({ ...prev, driverId: e.target.value || undefined }))}
                className="input-field"
              >
                <option value="">All Drivers</option>
                {getUniqueDrivers().map(driver => (
                  <option key={driver.id} value={driver.id}>
                    {driver.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Date Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date Range
              </label>
              <div className="flex space-x-2">
                <input
                  type="date"
                  value={filters.dateRange?.startDate || ''}
                  onChange={(e) => setFilters(prev => ({
                    ...prev,
                    dateRange: {
                      ...prev.dateRange,
                      startDate: e.target.value,
                      endDate: prev.dateRange?.endDate || ''
                    }
                  }))}
                  className="input-field text-sm"
                />
                <input
                  type="date"
                  value={filters.dateRange?.endDate || ''}
                  onChange={(e) => setFilters(prev => ({
                    ...prev,
                    dateRange: {
                      ...prev.dateRange,
                      startDate: prev.dateRange?.startDate || '',
                      endDate: e.target.value
                    }
                  }))}
                  className="input-field text-sm"
                />
              </div>
            </div>
          </div>

          <div className="flex justify-end mt-4">
            <button
              onClick={() => setFilters({})}
              className="text-sm text-gray-600 hover:text-gray-800"
            >
              Clear Filters
            </button>
          </div>
        </div>
      )}

      {/* Assignment History List */}
      <div className="space-y-4">
        {displayHistory.length === 0 ? (
          <div className="text-center py-8">
            <UserIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-sm font-medium text-gray-900 mb-2">No Assignment History</h3>
            <p className="text-sm text-gray-500">
              {Object.keys(filters).length > 0 ? 
                'No assignments match your current filters.' : 
                'This vehicle has no assignment history yet.'
              }
            </p>
          </div>
        ) : (
          displayHistory.map((assignment, index) => (
            <div key={assignment.id} className="bg-white rounded-lg border border-gray-200 p-4">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                      <UserIcon className="h-5 w-5 text-blue-600" />
                    </div>
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className="text-sm font-semibold text-gray-900">
                        {assignment.driverName}
                      </h4>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        assignment.unassignedAt ? 
                          'bg-gray-100 text-gray-800' : 
                          'bg-green-100 text-green-800'
                      }`}>
                        {assignment.unassignedAt ? 'Completed' : 'Current'}
                      </span>
                    </div>
                    
                    <div className="flex items-center space-x-4 text-sm text-gray-600 mb-2">
                      <div className="flex items-center space-x-1">
                        <CalendarIcon className="h-4 w-4" />
                        <span>{formatDate(assignment.assignedAt)}</span>
                      </div>
                      
                      {assignment.unassignedAt && (
                        <>
                          <ArrowRightIcon className="h-4 w-4" />
                          <div className="flex items-center space-x-1">
                            <CalendarIcon className="h-4 w-4" />
                            <span>{formatDate(assignment.unassignedAt)}</span>
                          </div>
                        </>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <div className="flex items-center space-x-1">
                        <ClockIcon className="h-4 w-4" />
                        <span>Duration: {calculateDuration(assignment.assignedAt, assignment.unassignedAt)}</span>
                      </div>
                      <span>•</span>
                      <span>Assigned by: {assignment.assignedBy}</span>
                    </div>
                    
                    {assignment.reason && (
                      <div className="mt-2 text-sm text-gray-600">
                        <span className="font-medium">Reason:</span> {assignment.reason}
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="flex-shrink-0 text-right">
                  <div className="text-xs text-gray-500">
                    Assignment #{displayHistory.length - index}
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Summary Stats */}
      {displayHistory.length > 0 && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Summary</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="text-gray-500">Total Assignments:</span>
              <span className="ml-2 font-medium text-gray-900">{displayHistory.length}</span>
            </div>
            <div>
              <span className="text-gray-500">Unique Drivers:</span>
              <span className="ml-2 font-medium text-gray-900">{getUniqueDrivers().length}</span>
            </div>
            <div>
              <span className="text-gray-500">Current Status:</span>
              <span className="ml-2 font-medium text-gray-900">
                {displayHistory.some(a => !a.unassignedAt) ? 'Assigned' : 'Unassigned'}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VehicleAssignmentHistory;
