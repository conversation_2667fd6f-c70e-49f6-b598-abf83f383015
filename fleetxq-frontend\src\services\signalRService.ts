import * as signalR from '@microsoft/signalr';
import { store } from '../store';
import { 
  updateMetrics, 
  addRecentActivity,
  setAutoRefresh 
} from '../features/dashboard/dashboardSlice';
import type { 
  RecentActivity, 
  VehicleRealTimeUpdate, 
  TelemetryUpdate,
  DashboardSummary 
} from '../types';

export interface SignalRConnectionConfig {
  url: string;
  accessTokenFactory?: () => string | Promise<string>;
  automaticReconnect?: boolean;
  reconnectIntervals?: number[];
  logLevel?: signalR.LogLevel;
}

export interface ConnectionStatus {
  state: signalR.HubConnectionState;
  isConnected: boolean;
  lastConnected?: Date;
  reconnectAttempts: number;
  error?: string;
}

class SignalRService {
  private connection: signalR.HubConnection | null = null;
  private config: SignalRConnectionConfig;
  private connectionStatus: ConnectionStatus = {
    state: signalR.HubConnectionState.Disconnected,
    isConnected: false,
    reconnectAttempts: 0,
  };
  private eventListeners: Map<string, Set<Function>> = new Map();
  private reconnectTimer: NodeJS.Timeout | null = null;
  private maxReconnectAttempts = 10;

  constructor(config: SignalRConnectionConfig) {
    this.config = {
      automaticReconnect: true,
      reconnectIntervals: [0, 2000, 10000, 30000],
      logLevel: signalR.LogLevel.Information,
      ...config,
    };
  }

  async initialize(): Promise<void> {
    if (this.connection) {
      await this.disconnect();
    }

    this.connection = new signalR.HubConnectionBuilder()
      .withUrl(this.config.url, {
        accessTokenFactory: this.config.accessTokenFactory,
      })
      .withAutomaticReconnect(this.config.reconnectIntervals)
      .configureLogging(this.config.logLevel!)
      .build();

    this.setupEventHandlers();
    this.setupConnectionEvents();
  }

  private setupConnectionEvents(): void {
    if (!this.connection) return;

    this.connection.onclose((error) => {
      this.connectionStatus = {
        ...this.connectionStatus,
        state: signalR.HubConnectionState.Disconnected,
        isConnected: false,
        error: error?.message,
      };
      this.emit('connectionClosed', { error });
      
      if (this.config.automaticReconnect) {
        this.scheduleReconnect();
      }
    });

    this.connection.onreconnecting((error) => {
      this.connectionStatus = {
        ...this.connectionStatus,
        state: signalR.HubConnectionState.Reconnecting,
        isConnected: false,
        reconnectAttempts: this.connectionStatus.reconnectAttempts + 1,
        error: error?.message,
      };
      this.emit('reconnecting', { error, attempts: this.connectionStatus.reconnectAttempts });
    });

    this.connection.onreconnected((connectionId) => {
      this.connectionStatus = {
        ...this.connectionStatus,
        state: signalR.HubConnectionState.Connected,
        isConnected: true,
        lastConnected: new Date(),
        reconnectAttempts: 0,
        error: undefined,
      };
      this.emit('reconnected', { connectionId });
      this.clearReconnectTimer();
    });
  }

  private setupEventHandlers(): void {
    if (!this.connection) return;

    // Dashboard metrics updates
    this.connection.on('DashboardMetricsUpdated', (data: Partial<DashboardSummary>) => {
      store.dispatch(updateMetrics(data));
      this.emit('dashboardMetricsUpdated', data);
    });

    // Real-time activity updates
    this.connection.on('ActivityAdded', (activity: RecentActivity) => {
      store.dispatch(addRecentActivity(activity));
      this.emit('activityAdded', activity);
    });

    // Vehicle real-time updates
    this.connection.on('VehicleUpdated', (update: VehicleRealTimeUpdate) => {
      this.emit('vehicleUpdated', update);
    });

    // Telemetry updates
    this.connection.on('TelemetryUpdated', (update: TelemetryUpdate) => {
      this.emit('telemetryUpdated', update);
    });

    // Alert notifications
    this.connection.on('AlertTriggered', (alert: RecentActivity) => {
      store.dispatch(addRecentActivity(alert));
      this.emit('alertTriggered', alert);
      
      // Show browser notification for critical alerts
      if (alert.severity === 'critical' && 'Notification' in window) {
        this.showBrowserNotification(alert);
      }
    });

    // Connection status updates
    this.connection.on('ConnectionStatus', (status: any) => {
      this.emit('connectionStatus', status);
    });
  }

  async connect(): Promise<void> {
    if (!this.connection) {
      throw new Error('SignalR connection not initialized');
    }

    try {
      await this.connection.start();
      this.connectionStatus = {
        ...this.connectionStatus,
        state: this.connection.state,
        isConnected: true,
        lastConnected: new Date(),
        reconnectAttempts: 0,
        error: undefined,
      };
      this.emit('connected', { connectionId: this.connection.connectionId });
    } catch (error) {
      this.connectionStatus = {
        ...this.connectionStatus,
        state: signalR.HubConnectionState.Disconnected,
        isConnected: false,
        error: (error as Error).message,
      };
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    this.clearReconnectTimer();
    
    if (this.connection) {
      await this.connection.stop();
      this.connectionStatus = {
        ...this.connectionStatus,
        state: signalR.HubConnectionState.Disconnected,
        isConnected: false,
      };
      this.emit('disconnected', {});
    }
  }

  private scheduleReconnect(): void {
    if (this.connectionStatus.reconnectAttempts >= this.maxReconnectAttempts) {
      this.emit('maxReconnectAttemptsReached', {});
      return;
    }

    const delay = Math.min(1000 * Math.pow(2, this.connectionStatus.reconnectAttempts), 30000);
    
    this.reconnectTimer = setTimeout(async () => {
      try {
        await this.connect();
      } catch (error) {
        console.error('Reconnection failed:', error);
      }
    }, delay);
  }

  private clearReconnectTimer(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  private showBrowserNotification(alert: RecentActivity): void {
    if (Notification.permission === 'granted') {
      new Notification('FleetXQ Critical Alert', {
        body: alert.message,
        icon: '/favicon.ico',
        tag: alert.id,
      });
    }
  }

  // Event system
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    this.eventListeners.get(event)!.add(callback);
  }

  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.delete(callback);
    }
  }

  private emit(event: string, data: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => callback(data));
    }
  }

  // Public methods
  getConnectionStatus(): ConnectionStatus {
    return { ...this.connectionStatus };
  }

  isConnected(): boolean {
    return this.connectionStatus.isConnected;
  }

  async joinGroup(groupName: string): Promise<void> {
    if (this.connection && this.isConnected()) {
      await this.connection.invoke('JoinGroup', groupName);
    }
  }

  async leaveGroup(groupName: string): Promise<void> {
    if (this.connection && this.isConnected()) {
      await this.connection.invoke('LeaveGroup', groupName);
    }
  }

  async requestNotificationPermission(): Promise<NotificationPermission> {
    if ('Notification' in window) {
      return await Notification.requestPermission();
    }
    return 'denied';
  }
}

// Create singleton instance
const getSignalRService = (() => {
  let instance: SignalRService | null = null;
  
  return (config?: SignalRConnectionConfig): SignalRService => {
    if (!instance && config) {
      instance = new SignalRService(config);
    }
    if (!instance) {
      throw new Error('SignalR service not initialized. Call with config first.');
    }
    return instance;
  };
})();

export default getSignalRService;
