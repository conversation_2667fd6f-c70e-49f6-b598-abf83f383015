import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { usePermissions, type Permission } from '../hooks/usePermissions';
import LoadingSpinner from '../components/LoadingSpinner';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: 'admin' | 'manager' | 'driver';
  allowedRoles?: ('admin' | 'manager' | 'driver')[];
  requiredPermissions?: Permission[];
  fallbackPath?: string;
  showLoadingSpinner?: boolean;
}

// Role hierarchy for access control
const ROLE_HIERARCHY: Record<'admin' | 'manager' | 'driver', number> = {
  admin: 3,
  manager: 2,
  driver: 1,
};

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole,
  allowedRoles = [],
  requiredPermissions = [],
  fallbackPath = '/dashboard',
  showLoadingSpinner = true,
}) => {
  const location = useLocation();
  const { user, isAuthenticated, isLoading } = useAuth();
  const { hasAnyPermission, canAccessRoute } = usePermissions();

  // Show loading spinner while authentication is being checked
  if (isLoading && showLoadingSpinner) {
    return <LoadingSpinner />;
  }

  // If not authenticated, redirect to login
  if (!isAuthenticated) {
    return <Navigate to="/auth/login" state={{ from: location }} replace />;
  }

  // If no user data, redirect to login
  if (!user) {
    return <Navigate to="/auth/login" replace />;
  }

  // Check role-based access with hierarchy
  let hasRequiredRole = true;

  if (requiredRole) {
    // Check if user has the exact required role or higher in hierarchy
    const userRoleLevel = ROLE_HIERARCHY[user.role];
    const requiredRoleLevel = ROLE_HIERARCHY[requiredRole];
    hasRequiredRole = userRoleLevel >= requiredRoleLevel;
  }

  if (allowedRoles.length > 0) {
    hasRequiredRole = allowedRoles.includes(user.role);
  }

  // Check permission-based access
  const hasRequiredPermissions = requiredPermissions.length === 0 ||
    hasAnyPermission(requiredPermissions) ||
    canAccessRoute(requiredPermissions);

  // If user doesn't have required role or permissions, redirect
  if (!hasRequiredRole || !hasRequiredPermissions) {
    // If user is trying to access admin routes without permission, redirect to dashboard
    if (location.pathname.startsWith('/admin') && user.role !== 'admin') {
      return <Navigate to="/dashboard" replace />;
    }

    // If user is trying to access manager routes without permission, redirect to dashboard
    if (location.pathname.startsWith('/manager') && !['admin', 'manager'].includes(user.role)) {
      return <Navigate to="/dashboard" replace />;
    }

    return <Navigate to={fallbackPath} replace />;
  }

  return <>{children}</>;
};

export default ProtectedRoute;
